{"version": 3, "file": "static/js/608.afcc5fd3.chunk.js", "mappings": "gOASA,MAqKA,EArK+BA,KAC7B,MAAM,GAAEC,IAAOC,EAAAA,EAAAA,KACTC,GAAWC,EAAAA,EAAAA,MACXC,EAASJ,GAAM,IACf,YAAEK,EAAW,WAAEC,IAAeC,EAAAA,EAAAA,IAAS,CAAEC,cAAc,KACvD,oBAAEC,IAAwBC,EAAAA,EAAAA,MAEzBC,EAAMC,IAAWC,EAAAA,EAAAA,UAAsB,OACvCC,EAAYC,IAAiBF,EAAAA,EAAAA,UAAgB,KAC7CG,EAAWC,IAAgBJ,EAAAA,EAAAA,WAAS,IACpCK,EAAcC,IAAmBN,EAAAA,EAAAA,WAAS,IAC1CO,EAAWC,IAAgBR,EAAAA,EAAAA,UAAS,YACpCS,EAAOC,IAAYV,EAAAA,EAAAA,UAAwB,OAKlDW,EAAAA,EAAAA,YAAU,KACR,IAAKpB,EAGH,OAFAa,GAAa,QACbM,EAAS,uBAIOE,WAChB,IACER,GAAa,GACbM,EAAS,MAET,MAAMG,QAAiBrB,EAAYD,GACnCQ,EAAQc,GAER,MAAMC,QAAelB,EAAoBL,GACzCW,EAAcY,EAChB,CAAE,MAAOL,GACPM,QAAQN,MAAM,4BAA6BA,GAC3C,MAAMO,EAAeP,aAAiBQ,MAAQR,EAAMS,QAAU,4BAC9DR,EAASM,EAEX,CAAC,QACCZ,GAAa,EACf,GAGFe,EAAW,GACV,CACD5B,EACAK,EACAJ,IAGF,MAAM4B,GAAmBC,EAAAA,EAAAA,cAAYT,UACnC,GAAKd,EAEL,IACEQ,GAAgB,GAChB,MAAMgB,QAAoB7B,EAAWK,EAAKX,GAAI0B,GAC9Cd,EAAQuB,GAIRd,EAAa,UACf,CAAE,MAAOC,GAGP,MAFAM,QAAQN,MAAM,uBAAwBA,GAEhCA,CACR,CAAC,QACCH,GAAgB,EAClB,IACC,CAACR,EAAML,IAEV,GAAIU,EACF,OACEoB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,KAACG,EAAAA,EAAc,CAACC,KAAK,SAK3B,GAAIlB,IAAUX,EACZ,OACE8B,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,oEAAmEC,SAAA,EAChFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCAAkCC,SAAC,wBAClDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,SAAEhB,KAChCc,EAAAA,EAAAA,KAAA,UACEM,QAASA,IAAMxC,EAAS,UACxBmC,UAAU,mEAAkEC,SAC7E,qBAOP,IAAK3B,EACH,OACE8B,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,oEAAmEC,SAAA,EAChFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,SAAC,oBACnDF,EAAAA,EAAAA,KAAA,UACEM,QAASA,IAAMxC,EAAS,UACxBmC,UAAU,mEAAkEC,SAC7E,qBAQP,MAAMK,EAAoB,CACxBC,YAAa9B,EAAW+B,OACxBC,WAAYhC,EAAWiC,QAAO,CAACC,EAAKC,IAAUD,EAAMC,EAAMC,aAAa,GACvEC,kBAAmBrC,EAAW+B,OAAS,EACnC/B,EAAWiC,QAAO,CAACC,EAAKC,IAAUD,EAAMC,EAAMC,aAAa,GAAKpC,EAAW+B,OAC3E,EACJO,eAAgB,EAChBC,aAAcvC,EAAWwC,KAAIL,IAAK,CAChCM,KAAMN,EAAMO,UACZC,OAAQR,EAAMC,iBAIlB,OACET,EAAAA,EAAAA,MAAA,OAAKJ,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAACsB,EAAAA,EAAU,CACTC,MAAO,SAAShD,EAAKiD,OACrBC,YAAY,6BACZC,YAAa,CACX,CAAEC,MAAO,QAASC,KAAM,UACxB,CAAED,MAAOpD,EAAKiD,UAIlBxB,EAAAA,EAAAA,KAAC6B,EAAAA,EAAI,CACHC,KAAM,CACJ,CAAElE,GAAI,UAAW+D,MAAO,WACxB,CAAE/D,GAAI,OAAQ+D,MAAO,QACrB,CAAE/D,GAAI,YAAa+D,MAAO,cAE5B3C,UAAWA,EACX+C,SAAU9C,IAGG,YAAdD,IACCgB,EAAAA,EAAAA,KAACgC,EAAAA,GAAW,CAACzD,KAAMA,EAAMG,WAAYA,IAGxB,SAAdM,IACCgB,EAAAA,EAAAA,KAACiC,EAAAA,GAAY,CACX1D,KAAMA,EACN2D,SAAUrC,EACVjB,UAAWE,IAIA,cAAdE,IACCgB,EAAAA,EAAAA,KAACmC,EAAAA,GAAa,CACZnE,OAAQA,EACRsB,SAAUiB,MAGV,C,uDC1JV,MAmCA,EAnCkC6B,IAK3B,IAL4B,KACjCN,EAAI,UACJ9C,EAAS,SACT+C,EAAQ,UACR9B,EAAY,IACbmC,EACC,OACEpC,EAAAA,EAAAA,KAAA,OAAKC,UAAW,4BAA4BA,IAAYC,UACtDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,SACnC4B,EAAKZ,KAAKmB,IACT,MAAMC,EAAWD,EAAIzE,KAAOoB,EAE5B,OACEgB,EAAAA,EAAAA,KAAA,UAEEM,QAASA,KAAO+B,EAAIE,UAAYR,EAASM,EAAIzE,IAC7CqC,UAAW,iNAGPqC,EACE,8BACA,iGACFD,EAAIE,SAAW,gCAAkC,mCAErDA,SAAUF,EAAIE,SAASrC,SAEtBmC,EAAIV,OAZAU,EAAIzE,GAaF,OAIX,C", "sources": ["pages/UserEditPage.tsx", "components/common/Tabs.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\r\nimport { useParams, useNavigate } from 'react-router-dom';\r\nimport PageHeader from '../components/layout/PageHeader';\r\nimport Tabs from '../components/common/Tabs';\r\nimport { UserDetails, EditUserForm, UserAnalytics, useUsers } from '../features/users/index';\r\nimport { useOrders } from '../features/orders/index';\r\nimport LoadingSpinner from '../components/common/LoadingSpinner';\r\nimport type { User, UserFormDataFrontend } from '../features/users/types';\r\n\r\nconst UserEditPage: React.FC = () => {\r\n  const { id } = useParams<{ id: string }>();\r\n  const navigate = useNavigate();\r\n  const userId = id || ''; // Default to empty string if undefined\r\n  const { getUserById, updateUser } = useUsers({ initialFetch: false });\r\n  const { getOrdersByCustomer } = useOrders();\r\n\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [userOrders, setUserOrders] = useState<any[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [activeTab, setActiveTab] = useState('details');\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n\r\n\r\n  // Move the data fetching directly into useEffect to avoid dependency issues\r\n  useEffect(() => {\r\n    if (!userId) {\r\n      setIsLoading(false);\r\n      setError('No user ID provided');\r\n      return;\r\n    }\r\n\r\n    const fetchData = async () => {\r\n      try {\r\n        setIsLoading(true);\r\n        setError(null);\r\n\r\n        const userData = await getUserById(userId);\r\n        setUser(userData);\r\n\r\n        const orders = await getOrdersByCustomer(userId);\r\n        setUserOrders(orders);\r\n      } catch (error) {\r\n        console.error('Error fetching user data:', error);\r\n        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch user data';\r\n        setError(errorMessage);\r\n        // Error notifications are handled by the hooks (useUsers, useOrders)\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchData();\r\n  }, [\r\n    userId,\r\n    getOrdersByCustomer,\r\n    getUserById\r\n  ]); // Only userId as dependency - functions are called directly\r\n  \r\n  const handleUpdateUser = useCallback(async (userData: UserFormDataFrontend) => {\r\n    if (!user) return;\r\n\r\n    try {\r\n      setIsSubmitting(true);\r\n      const updatedUser = await updateUser(user.id, userData);\r\n      setUser(updatedUser);\r\n\r\n      // Success notification is handled by the useUsers hook\r\n      // Switch back to details tab after successful update\r\n      setActiveTab('details');\r\n    } catch (error) {\r\n      console.error('Error updating user:', error);\r\n      // Error notification is also handled by the useUsers hook\r\n      throw error; // Re-throw to let the form handle it\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  }, [user, updateUser]); // Keep essential dependencies only\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"flex justify-center items-center min-h-screen\">\r\n        <LoadingSpinner size=\"lg\" />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error && !user) {\r\n    return (\r\n      <div className=\"flex flex-col items-center justify-center min-h-[400px] space-y-4\">\r\n        <div className=\"text-red-600 text-lg font-medium\">Error Loading User</div>\r\n        <div className=\"text-gray-600\">{error}</div>\r\n        <button\r\n          onClick={() => navigate('/users')}\r\n          className=\"px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark\"\r\n        >\r\n          Back to Users\r\n        </button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!user) {\r\n    return (\r\n      <div className=\"flex flex-col items-center justify-center min-h-[400px] space-y-4\">\r\n        <div className=\"text-gray-600 text-lg font-medium\">User not found</div>\r\n        <button\r\n          onClick={() => navigate('/users')}\r\n          className=\"px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark\"\r\n        >\r\n          Back to Users\r\n        </button>\r\n      </div>\r\n    );\r\n  }\r\n  \r\n  // Calculate analytics data\r\n  const userAnalyticsData = {\r\n    totalOrders: userOrders.length,\r\n    totalSpent: userOrders.reduce((sum, order) => sum + order.totalAmount, 0),\r\n    averageOrderValue: userOrders.length > 0 \r\n      ? userOrders.reduce((sum, order) => sum + order.totalAmount, 0) / userOrders.length \r\n      : 0,\r\n    orderFrequency: 0, // Would calculate based on date ranges in a real app\r\n    orderHistory: userOrders.map(order => ({\r\n      date: order.orderDate,\r\n      amount: order.totalAmount\r\n    }))\r\n  };\r\n  \r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <PageHeader\r\n        title={`User: ${user.name}`}\r\n        description=\"View and edit user details\"\r\n        breadcrumbs={[\r\n          { label: 'Users', path: '/users' },\r\n          { label: user.name }\r\n        ]}\r\n      />\r\n      \r\n      <Tabs\r\n        tabs={[\r\n          { id: 'details', label: 'Details' },\r\n          { id: 'edit', label: 'Edit' },\r\n          { id: 'analytics', label: 'Analytics' }\r\n        ]}\r\n        activeTab={activeTab}\r\n        onChange={setActiveTab}\r\n      />\r\n      \r\n      {activeTab === 'details' && (\r\n        <UserDetails user={user} userOrders={userOrders} />\r\n      )}\r\n      \r\n      {activeTab === 'edit' && (\r\n        <EditUserForm\r\n          user={user}\r\n          onSubmit={handleUpdateUser}\r\n          isLoading={isSubmitting}\r\n        />\r\n      )}\r\n      \r\n      {activeTab === 'analytics' && (\r\n        <UserAnalytics\r\n          userId={userId}\r\n          userData={userAnalyticsData}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UserEditPage;\r\n", "// src/components/common/Tabs.tsx\r\nimport React from 'react';\r\n\r\nexport interface Tab {\r\n  id: string;\r\n  label: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface TabsProps {\r\n  tabs: Tab[];\r\n  activeTab: string;\r\n  onChange: (tabId: string) => void;\r\n  className?: string;\r\n}\r\n\r\nconst Tabs: React.FC<TabsProps> = ({\r\n  tabs,\r\n  activeTab,\r\n  onChange,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <div className={`border-b border-gray-200 ${className}`}>\r\n      <nav className=\"-mb-px flex space-x-8\">\r\n        {tabs.map((tab) => {\r\n          const isActive = tab.id === activeTab;\r\n          \r\n          return (\r\n            <button\r\n              key={tab.id}\r\n              onClick={() => !tab.disabled && onChange(tab.id)}\r\n              className={`\r\n                whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm\r\n                focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2\r\n                ${isActive\r\n                  ? 'border-primary text-primary'\r\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}\r\n                ${tab.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}\r\n              `}\r\n              disabled={tab.disabled}\r\n            >\r\n              {tab.label}\r\n            </button>\r\n          );\r\n        })}\r\n      </nav>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Tabs;"], "names": ["UserEditPage", "id", "useParams", "navigate", "useNavigate", "userId", "getUserById", "updateUser", "useUsers", "initialFetch", "getOrdersByCustomer", "useOrders", "user", "setUser", "useState", "userOrders", "setUserOrders", "isLoading", "setIsLoading", "isSubmitting", "setIsSubmitting", "activeTab", "setActiveTab", "error", "setError", "useEffect", "async", "userData", "orders", "console", "errorMessage", "Error", "message", "fetchData", "handleUpdateUser", "useCallback", "updatedUser", "_jsx", "className", "children", "LoadingSpinner", "size", "_jsxs", "onClick", "userAnalyticsData", "totalOrders", "length", "totalSpent", "reduce", "sum", "order", "totalAmount", "averageOrderValue", "orderFrequency", "orderHistory", "map", "date", "orderDate", "amount", "<PERSON><PERSON><PERSON><PERSON>", "title", "name", "description", "breadcrumbs", "label", "path", "Tabs", "tabs", "onChange", "UserDetails", "EditUserForm", "onSubmit", "UserAnalytics", "_ref", "tab", "isActive", "disabled"], "sourceRoot": ""}