{"version": 3, "file": "static/js/791.8023b868.chunk.js", "mappings": "yJAgBO,MAAMA,EAAgBA,CAC3BC,EACAC,KAEA,MAAOC,EAAUC,IAAeC,EAAAA,EAAAA,UAAc,KACvCC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAuB,OAC3C,iBAAEK,IAAqBC,EAAAA,EAAAA,KAGvBC,GAAgBC,EAAAA,EAAAA,QAAOZ,GACvBa,GAAsBD,EAAAA,EAAAA,QAAOH,GAC7BK,GAAgBF,EAAAA,EAAAA,QAAOX,EAAQc,YAC/BC,GAAoBJ,EAAAA,EAAAA,SAAO,IAGjCK,EAAAA,EAAAA,YAAU,KACRN,EAAcO,QAAUlB,EACxBa,EAAoBK,QAAUT,EAC9BK,EAAcI,QAAUjB,EAAQc,UAAU,IAG5C,MAAMI,GAAgBC,EAAAA,EAAAA,cAAYC,UAChCf,GAAa,GACbE,EAAS,MACT,IACE,MAAMc,QAAaX,EAAcO,QAAQK,OAAOC,GAEhD,OADArB,EAAYmB,GACLA,CACT,CAAE,MAAOG,GACP,MAAMlB,EAAQkB,EAOd,MANAjB,EAASD,GACTM,EAAoBK,QAAQ,CAC1BQ,KAAM,QACNC,MAAO,QACPC,QAAS,mBAAmBd,EAAcI,YAEtCX,CACR,CAAC,QACCD,GAAa,EACf,IACC,IAEGuB,GAAgBT,EAAAA,EAAAA,cAAYC,UAChCf,GAAa,GACbE,EAAS,MACT,IAEE,aADqBG,EAAcO,QAAQY,QAAQC,EAErD,CAAE,MAAON,GACP,MAAMlB,EAAQkB,EAOd,MANAjB,EAASD,GACTM,EAAoBK,QAAQ,CAC1BQ,KAAM,QACNC,MAAO,QACPC,QAAS,mBAAmBd,EAAcI,YAEtCX,CACR,CAAC,QACCD,GAAa,EACf,IACC,IAEG0B,GAAeZ,EAAAA,EAAAA,cAAYC,UAC/Bf,GAAa,GACbE,EAAS,MACT,IACE,MAAMyB,QAAkBtB,EAAcO,QAAQgB,OAAOZ,GAOrD,OANAnB,GAAYgC,GAAQ,IAAIA,EAAMF,KAC9BpB,EAAoBK,QAAQ,CAC1BQ,KAAM,UACNC,MAAO,UACPC,QAAS,GAAGd,EAAcI,iCAErBe,CACT,CAAE,MAAOR,GACP,MAAMlB,EAAQkB,EAOd,MANAjB,EAASD,GACTM,EAAoBK,QAAQ,CAC1BQ,KAAM,QACNC,MAAO,QACPC,QAAS,oBAAoBd,EAAcI,YAEvCX,CACR,CAAC,QACCD,GAAa,EACf,IACC,IAEG8B,GAAehB,EAAAA,EAAAA,cAAYC,MAAOU,EAAYT,KAClDhB,GAAa,GACbE,EAAS,MACT,IACE,MAAM6B,QAAsB1B,EAAcO,QAAQoB,OAAOP,EAAIT,GAS7D,OARAnB,GAAYgC,GAAQA,EAAKI,KAAIC,GAC1BA,EAAeT,KAAOA,EAAKM,EAAgBG,MAE9C3B,EAAoBK,QAAQ,CAC1BQ,KAAM,UACNC,MAAO,UACPC,QAAS,GAAGd,EAAcI,iCAErBmB,CACT,CAAE,MAAOZ,GACP,MAAMlB,EAAQkB,EAOd,MANAjB,EAASD,GACTM,EAAoBK,QAAQ,CAC1BQ,KAAM,QACNC,MAAO,QACPC,QAAS,oBAAoBd,EAAcI,YAEvCX,CACR,CAAC,QACCD,GAAa,EACf,IACC,IAEGmC,GAAerB,EAAAA,EAAAA,cAAYC,UAC/Bf,GAAa,GACbE,EAAS,MACT,UACQG,EAAcO,QAAQwB,OAAOX,GACnC5B,GAAYgC,GAAQA,EAAKQ,QAAOH,GAAWA,EAAeT,KAAOA,MACjElB,EAAoBK,QAAQ,CAC1BQ,KAAM,UACNC,MAAO,UACPC,QAAS,GAAGd,EAAcI,gCAE9B,CAAE,MAAOO,GACP,MAAMlB,EAAQkB,EAOd,MANAjB,EAASD,GACTM,EAAoBK,QAAQ,CAC1BQ,KAAM,QACNC,MAAO,QACPC,QAAS,oBAAoBd,EAAcI,YAEvCX,CACR,CAAC,QACCD,GAAa,EACf,IACC,IAuCH,OApCAW,EAAAA,EAAAA,YAAU,KACR,IAA6B,IAAzBhB,EAAQ2C,eAA2B5B,EAAkBE,QAAS,CAChE2B,QAAQC,IAAI,8CAA8C7C,EAAQc,cAClEC,EAAkBE,SAAU,EAE5B,MAAM0B,EAAevB,UACnBf,GAAa,GACbE,EAAS,MACT,IACEqC,QAAQC,IAAI,mCAAmC7C,EAAQc,cACvD,MAAMO,QAAatB,EAAWuB,SAC9BsB,QAAQC,IAAI,qCAAqC7C,EAAQc,cAAeO,GACxEnB,EAAYmB,EACd,CAAE,MAAOG,GACP,MAAMlB,EAAQkB,EACdoB,QAAQtC,MAAM,kCAAkCN,EAAQc,cAAeR,GACvEC,EAASD,GACTM,EAAoBK,QAAQ,CAC1BQ,KAAM,QACNC,MAAO,QACPC,QAAS,mBAAmB3B,EAAQc,cAExC,CAAC,QACC8B,QAAQC,IAAI,sCAAsC7C,EAAQc,cAC1DT,GAAa,EACf,GAGFsC,GACF,IACC,CACD5C,EACAC,EAAQc,WACRd,EAAQ2C,eAGH,CACL1C,WACAG,YACAE,QACAY,gBACAU,gBACAG,eACAI,eACAK,eACAtC,cACD,C,gDC5MH,SAAS4C,EAAUC,EAIhBC,GAAQ,IAJS,MAClBtB,EAAK,QACLuB,KACGC,GACJH,EACC,OAAoBI,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQxB,EAAqByB,EAAAA,cAAoB,QAAS,CAC3DrB,GAAImB,GACHvB,GAAS,KAAmByB,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,wKAEP,CACA,MACA,EADiCX,EAAAA,WAAiBL,E,yDCGlD,MAAMiB,EAA4BhB,IAgB3B,IAhB4B,MACjCrB,EAAK,SACLsC,EAAQ,SACRC,EAAQ,UACRC,EAAY,GAAE,cACdC,EAAgB,GAAE,gBAClBC,EAAkB,GAAE,gBACpBC,EAAkB,GAAE,KACpBC,EAAI,OACJC,EAAM,QACNC,EAAO,UACPC,GAAY,EAAK,UACjBC,GAAY,EAAK,SACjBC,GAAW,EAAI,QACfC,GAAU,EAAK,OACfC,GACD9B,EAEC,MAAM+B,EAAc,6BACIH,EAAW,yBAA2B,uDAC1DF,EAAY,uEAAyE,oBACrFD,EAAU,iBAAmB,WAC7BN,QAIEa,EAAgB,mFAElBX,QAIEY,EAAc,SAChBN,EAAY,GAAK,cACjBP,QAIEc,EAAgB,4DAElBZ,QAIJ,OAAIO,GAEAM,EAAAA,EAAAA,MAAA,OAAKhB,UAAWY,EAAa,cAAaD,EAAOZ,SAAA,EAC7CvC,GAASsC,GAAYM,KACrBY,EAAAA,EAAAA,MAAA,OAAKhB,UAAWa,EAAcd,SAAA,EAC5BiB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,SAAQD,SAAA,CACpBvC,IAASyD,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,gDACxBF,IAAYmB,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,wDAE7BI,IAAQa,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,uDAI5BiB,EAAAA,EAAAA,KAAA,OAAKjB,UAAWc,EAAYf,UAC1BkB,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,6CAGhBK,IACCY,EAAAA,EAAAA,KAAA,OAAKjB,UAAWe,EAAchB,UAC5BkB,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,sDAQvBgB,EAAAA,EAAAA,MAAA,OACEhB,UAAWY,EACXN,QAASA,EACT,cAAaK,EAAOZ,SAAA,EAElBvC,GAASsC,GAAYM,KACrBY,EAAAA,EAAAA,MAAA,OAAKhB,UAAWa,EAAcd,SAAA,EAC5BiB,EAAAA,EAAAA,MAAA,OAAAjB,SAAA,CACoB,kBAAVvC,GACNyD,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,qCAAoCD,SAAEvC,IAEpDA,EAEmB,kBAAbsC,GACNmB,EAAAA,EAAAA,KAAA,KAAGjB,UAAU,6BAA4BD,SAAED,IAE3CA,KAGHM,IAAQa,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,eAAcD,SAAEK,QAI5Ca,EAAAA,EAAAA,KAAA,OAAKjB,UAAWc,EAAYf,SAAEA,IAE7BM,IACCY,EAAAA,EAAAA,KAAA,OAAKjB,UAAWe,EAAchB,SAC3BM,MAGD,EAIV,GAAea,EAAAA,EAAAA,MAAKrB,E,uDCxHpB,MAsGA,EAtGsDhB,IAM/C,IANgD,KACrDsC,EAAO,KAAI,UACXnB,EAAY,GAAE,QACdoB,EAAU,UAAS,MACnBC,EAAQ,UAAS,gBACjBC,GAAkB,GACnBzC,EACC,MAAM0C,EAAU,CACdC,GAAI,CAAEC,QAAS,UAAWC,KAAM,UAAWC,MAAO,UAAWC,OAAQ,WACrEC,GAAI,CAAEJ,QAAS,UAAWC,KAAM,cAAeC,MAAO,UAAWC,OAAQ,aACzEE,GAAI,CAAEL,QAAS,YAAaC,KAAM,UAAWC,MAAO,UAAWC,OAAQ,cAGnEG,EAAeT,EAAkB,eAAiBD,EAGxD,MAAgB,YAAZD,GAEAJ,EAAAA,EAAAA,MAAA,OACEhB,UAAW,oCAAoCA,IAC/CgC,KAAK,SACL,aAAW,UAASjC,SAAA,EAEpBkB,EAAAA,EAAAA,KAAA,OACEjB,UAAW,wDAAwDuB,EAAQJ,GAAMM,UACjFQ,MAAO,CACLC,eAAgBH,EAChBI,iBAAkBJ,MAGtBd,EAAAA,EAAAA,KAAA,QAAMjB,UAAU,UAASD,SAAC,kBAMhB,SAAZqB,GAEAJ,EAAAA,EAAAA,MAAA,OACEhB,UAAW,0DAA0DA,IACrEgC,KAAK,SACL,aAAW,UAASjC,SAAA,EAEpBkB,EAAAA,EAAAA,KAAA,OACEjB,UAAW,GAAGuB,EAAQJ,GAAMO,wBAC5BO,MAAO,CAAEG,gBAAiBL,MAE5Bd,EAAAA,EAAAA,KAAA,OACEjB,UAAW,GAAGuB,EAAQJ,GAAMO,wBAC5BO,MAAO,CAAEG,gBAAiBL,MAE5Bd,EAAAA,EAAAA,KAAA,OACEjB,UAAW,GAAGuB,EAAQJ,GAAMO,wBAC5BO,MAAO,CAAEG,gBAAiBL,MAE5Bd,EAAAA,EAAAA,KAAA,QAAMjB,UAAU,UAASD,SAAC,kBAMhB,UAAZqB,GAEAJ,EAAAA,EAAAA,MAAA,OACEhB,UAAW,oCAAoCA,IAC/CgC,KAAK,SACL,aAAW,UAASjC,SAAA,EAEpBkB,EAAAA,EAAAA,KAAA,OACEjB,UAAW,GAAGuB,EAAQJ,GAAMQ,kCAC5BM,MAAO,CAAEG,gBAAiBL,MAE5Bd,EAAAA,EAAAA,KAAA,QAAMjB,UAAU,UAASD,SAAC,kBAMhB,WAAZqB,GAEAJ,EAAAA,EAAAA,MAAA,OACEhB,UAAW,oCAAoCA,IAC/CgC,KAAK,SACL,aAAW,UAASjC,SAAA,EAEpBkB,EAAAA,EAAAA,KAAA,OACEjB,UAAW,GAAGuB,EAAQJ,GAAMS,oCAC5BK,MAAO,CAAEZ,MAAOU,GAAehC,UAE/BkB,EAAAA,EAAAA,KAAA,OACEjB,UAAW,GAAGuB,EAAQJ,GAAMQ,0CAC5BM,MAAO,CAAEG,gBAAiBL,QAG9Bd,EAAAA,EAAAA,KAAA,QAAMjB,UAAU,UAASD,SAAC,kBAKzB,IAAI,C,2CC/FN,MAAMsC,EAAgBC,GACR,mDACDC,KAAKD,GAGZE,EAAgBC,GACR,oBACDF,KAAKE,GAGZC,EAAcC,IACzB,IAEE,OADA,IAAIC,IAAID,IACD,CACT,CAAE,MAAOvG,GACP,OAAO,CACT,GAGWyG,EAAcC,GACX,OAAVA,QAA4BC,IAAVD,IACD,kBAAVA,EAA2BA,EAAME,OAAOC,OAAS,GACxDC,MAAMC,QAAQL,IAAeA,EAAMG,OAAS,GAYrCG,EAAaN,GACjB,WAAWP,KAAKO,GAGZO,EAAaP,GACjB,sBAAsBP,KAAKO,GAGvBQ,EAAkBR,GACtB,iBAAiBP,KAAKO,GAGlBS,EAAeC,IAC1B,MAAMC,EAAO,IAAIC,KAAKF,GACtB,OAAQG,MAAMF,EAAKG,UAAU,EAGlBC,EAAmBA,CAACC,EAAkBC,IAC1CD,IAAaC,EAGTC,EAAoBF,KAE3BA,EAASb,OAAS,OAGjB,QAAQV,KAAKuB,OAGb,QAAQvB,KAAKuB,OAGb,QAAQvB,KAAKuB,MAGb,sCAAsCvB,KAAKuB,MAwBrCG,EAAeA,CAC1BC,EACAC,KAEA,MAAMC,EAA2C,CAAC,EAUlD,OARAlF,OAAOmF,QAAQF,GAAiBG,SAAQzF,IAAyB,IAAvB0F,EAAWC,GAAM3F,EACzD,MAAM4F,EAAMF,EACNnI,EA1BmBsI,EAC3BC,EACA7B,EACA0B,EACAI,KAEA,MAAMC,EAAY3B,MAAMC,QAAQqB,GAASA,EAAQ,CAACA,GAElD,IAAK,MAAMM,KAAQD,EACjB,IAAKC,EAAKC,UAAUjC,EAAO8B,GACzB,OAAOE,EAAKrH,QAIhB,MAAO,EAAE,EAYOiH,CAAcH,EAAWL,EAAOO,GAAMD,EAAON,GACvD9H,IACFgI,EAAOK,GAAOrI,EAChB,IAGKgI,CAAM,EAIFD,EAAkB,CAC7Ba,SAAU,WAA2C,MAAsB,CACzED,UAAWlC,EACXpF,QAFwBwH,UAAAhC,OAAA,QAAAF,IAAAkC,UAAA,GAAAA,UAAA,GAAG,yBAG5B,EAED3C,MAAO,WAAuD,MAAsB,CAClFyC,UAAW1C,EACX5E,QAFqBwH,UAAAhC,OAAA,QAAAF,IAAAkC,UAAA,GAAAA,UAAA,GAAG,qCAGzB,EAEDxC,MAAO,WAAsD,MAAsB,CACjFsC,UAAWvC,EACX/E,QAFqBwH,UAAAhC,OAAA,QAAAF,IAAAkC,UAAA,GAAAA,UAAA,GAAG,oCAGzB,EAEDtC,IAAK,WAA6C,MAAsB,CACtEoC,UAAWrC,EACXjF,QAFmBwH,UAAAhC,OAAA,QAAAF,IAAAkC,UAAA,GAAAA,UAAA,GAAG,2BAGvB,EAEDC,UAAWA,CAACC,EAAa1H,KAAgB,CACvCsH,UAAYjC,GA3GSoC,EAACpC,EAAeqC,IAChCrC,EAAMG,QAAUkC,EA0GSD,CAAUpC,EAAOqC,GAC/C1H,QAASA,GAAW,oBAAoB0H,iBAG1CC,UAAWA,CAACC,EAAa5H,KAAgB,CACvCsH,UAAYjC,GA5GSsC,EAACtC,EAAeuC,IAChCvC,EAAMG,QAAUoC,EA2GSD,CAAUtC,EAAOuC,GAC/C5H,QAASA,GAAW,wBAAwB4H,iBAG9CC,QAAS,WAAiD,MAAsB,CAC9EP,UAAW3B,EACX3F,QAFuBwH,UAAAhC,OAAA,QAAAF,IAAAkC,UAAA,GAAAA,UAAA,GAAG,+BAG3B,EAEDM,QAAS,WAAwD,MAAsB,CACrFR,UAAW1B,EACX5F,QAFuBwH,UAAAhC,OAAA,QAAAF,IAAAkC,UAAA,GAAAA,UAAA,GAAG,sCAG3B,EAEDO,aAAc,WAAwD,MAAsB,CAC1FT,UAAWzB,EACX7F,QAF4BwH,UAAAhC,OAAA,QAAAF,IAAAkC,UAAA,GAAAA,UAAA,GAAG,sCAGhC,EAEDxB,KAAM,WAA8C,MAAsB,CACxEsB,UAAWxB,EACX9F,QAFoBwH,UAAAhC,OAAA,QAAAF,IAAAkC,UAAA,GAAAA,UAAA,GAAG,4BAGxB,EAEDnB,SAAU,WAA2H,MAAsB,CACzJiB,UAAWf,EACXvG,QAFwBwH,UAAAhC,OAAA,QAAAF,IAAAkC,UAAA,GAAAA,UAAA,GAAG,yGAG5B,EAEDQ,cAAe,WAA2C,MAAsB,CAC9EV,UAAWA,CAACjC,EAAe8B,IAAmBf,EAAiBf,EAAe,OAAR8B,QAAQ,IAARA,OAAQ,EAARA,EAAUb,iBAChFtG,QAF6BwH,UAAAhC,OAAA,QAAAF,IAAAkC,UAAA,GAAAA,UAAA,GAAG,yBAGjC,EAEDS,qBAAsB,WAA2C,MAAsB,CACrFX,UAAWA,CAACjC,EAAe8B,IAAmBf,EAAiBf,EAAe,OAAR8B,QAAQ,IAARA,OAAQ,EAARA,EAAUd,UAChFrG,QAFoCwH,UAAAhC,OAAA,QAAAF,IAAAkC,UAAA,GAAAA,UAAA,GAAG,yBAGxC,EAGDU,IAAK,WAA6C,MAAsB,CACtEZ,UAAYjC,GAAkB,sBAAsBP,KAAKO,GACzDrF,QAFmBwH,UAAAhC,OAAA,QAAAF,IAAAkC,UAAA,GAAAA,UAAA,GAAG,2BAGvB,EAEDW,MAAO,WAA+C,MAAsB,CAC1Eb,UAAYjC,GAAkBA,EAAQ,GAAKA,GAAS,OACpDrF,QAFqBwH,UAAAhC,OAAA,QAAAF,IAAAkC,UAAA,GAAAA,UAAA,GAAG,6BAGzB,EAEDY,MAAO,WAAwD,MAAsB,CACnFd,UAAYjC,GAAkBgD,OAAOC,UAAUjD,IAAUA,GAAS,EAClErF,QAFqBwH,UAAAhC,OAAA,QAAAF,IAAAkC,UAAA,GAAAA,UAAA,GAAG,sCAGzB,EAEDe,aAAc,WAA6D,MAAsB,CAC/FjB,UAAYjC,GAAkBgD,OAAOC,UAAUjD,IAAUA,GAAS,EAClErF,QAF4BwH,UAAAhC,OAAA,QAAAF,IAAAkC,UAAA,GAAAA,UAAA,GAAG,2CAGhC,EAEDgB,iBAAkB,WAAuE,MAAsB,CAC7GlB,UAAWA,CAACiB,EAAsBpB,KAC3BA,IAAaA,EAASiB,OACpBG,GAAgBpB,EAASiB,MAElCpI,QALgCwH,UAAAhC,OAAA,QAAAF,IAAAkC,UAAA,GAAAA,UAAA,GAAG,qDAMpC,EAEDiB,cAAe,WAAkD,MAAsB,CACrFnB,UAAYjC,GAAiBI,MAAMC,QAAQL,IAAUA,EAAMG,OAAS,EACpExF,QAF6BwH,UAAAhC,OAAA,QAAAF,IAAAkC,UAAA,GAAAA,UAAA,GAAG,gCAGjC,EAEDkB,WAAY,eAACC,EAAgBnB,UAAAhC,OAAA,QAAAF,IAAAkC,UAAA,GAAAA,UAAA,GAAG,GAAoB,MAAsB,CACxEF,UAAYjC,KACLI,MAAMC,QAAQL,IACZA,EAAMG,QAAUmD,EAEzB3I,SALkDwH,UAAAhC,OAAA,EAAAgC,UAAA,QAAAlC,IAK9B,WAAWqD,mBAChC,E,gDCxOH,SAASC,EAAmBxH,EAIzBC,GAAQ,IAJkB,MAC3BtB,EAAK,QACLuB,KACGC,GACJH,EACC,OAAoBI,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQxB,EAAqByB,EAAAA,cAAoB,QAAS,CAC3DrB,GAAImB,GACHvB,GAAS,KAAmByB,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,kFAEP,CACA,MACA,EADiCX,EAAAA,WAAiBoH,GCvBlD,SAASC,EAAazH,EAInBC,GAAQ,IAJY,MACrBtB,EAAK,QACLuB,KACGC,GACJH,EACC,OAAoBI,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQxB,EAAqByB,EAAAA,cAAoB,QAAS,CAC3DrB,GAAImB,GACHvB,GAAS,KAAmByB,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,+BAEP,CACA,MACA,EADiCX,EAAAA,WAAiBqH,G,2CCoBlD,SAASC,EAAS1H,GAqBK,IArB2B,QAChD2H,EAAO,KACPrJ,EAAI,WACJsJ,EAAU,MACVjJ,EAAK,YACLkJ,EAAW,QACXhG,GAAU,EAAK,WACfiG,GAAa,EAAI,SACjBC,EAAWC,EAAAA,GAAOC,kBAAiB,WACnCC,GAAa,EAAI,kBACjBC,EAAiB,QACjBC,EAAO,aACPC,EAAe,mBAAkB,UACjClH,EAAY,GAAE,gBACdE,EAAkB,GAAE,cACpBD,EAAgB,GAAE,gBAClBE,EAAkB,GAAE,aACpBgH,EAAY,eACZC,EAAc,qBACdC,EAAuB,MAAK,OAC5B1G,GACkB9B,EAElB,MAAOyI,EAAYC,IAAiBtL,EAAAA,EAAAA,UAG1BmL,EAAiB,CAAE3C,IAAK2C,EAAgBI,UAAWH,GAAyB,OAE/EI,EAAYC,IAAiBzL,EAAAA,EAAAA,UAAS,KACtC0L,EAAaC,IAAkB3L,EAAAA,EAAAA,UAAS,IACxC4L,EAAcC,IAAmB7L,EAAAA,EAAAA,UAAmB,KACpD8L,EAAYC,IAAiB/L,EAAAA,EAAAA,UAAwB,MAWtDgM,GAAaC,EAAAA,EAAAA,UAAQ,IACpBZ,EAEE,IAAInK,GAAMgL,MAAK,CAACC,EAAGC,KACxB,MAAMC,EAASF,EAAEd,EAAW7C,KACtB8D,EAASF,EAAEf,EAAW7C,KAG5B,OAAc,MAAV6D,GAA4B,MAAVC,EAAuB,EAC/B,MAAVD,EAAgD,QAAzBhB,EAAWE,WAAuB,EAAI,EACnD,MAAVe,EAAgD,QAAzBjB,EAAWE,UAAsB,GAAK,EAG3C,kBAAXc,GAAyC,kBAAXC,EACP,QAAzBjB,EAAWE,UACdc,EAAOE,cAAcD,GACrBA,EAAOC,cAAcF,GAGvBA,EAASC,EACqB,QAAzBjB,EAAWE,WAAuB,EAAI,EAE3Cc,EAASC,EACqB,QAAzBjB,EAAWE,UAAsB,GAAK,EAExC,CAAC,IAxBcrK,GA0BvB,CAACA,EAAMmK,IAGJmB,GAAeP,EAAAA,EAAAA,UAAQ,IACtBT,EAEEQ,EAAWzJ,QAAQkK,GACxBxJ,OAAOmF,QAAQqE,GAAKC,MAAKC,IAAoB,IAAlBC,EAAM/F,GAAM8F,EAErC,OAAc,OAAV9F,QAA4BC,IAAVD,IACD,kBAAVA,GAEJgG,OAAOhG,GAAOiG,cAAcC,SAASvB,EAAWsB,eAAc,MARjDd,GAWvB,CAACA,EAAYR,IAGVwB,EAAaC,KAAKC,KAAKV,EAAaxF,OAAS2D,GAC7CwC,GAAgBlB,EAAAA,EAAAA,UAAQ,KAC5B,MAAMmB,GAAc1B,EAAc,GAAKf,EACvC,OAAO6B,EAAaa,MAAMD,EAAYA,EAAazC,EAAS,GAC3D,CAAC6B,EAAcd,EAAaf,IAEzB2C,EAAoBC,IACxB5B,EAAe4B,EAAK,EA0ChBC,EAAqBC,IACzB,IAAIC,EAAU,4BAEd,GAAsB,kBAAXD,EAAqB,CAC9B,MAAME,EAAcF,EAAOX,cAEvBa,EAAYZ,SAAS,WAAaY,EAAYZ,SAAS,aACvDY,EAAYZ,SAAS,aAAeY,EAAYZ,SAAS,cACzDY,EAAYZ,SAAS,WACvBW,EAAU,8BACDC,EAAYZ,SAAS,YAAcY,EAAYZ,SAAS,cACjEW,EAAU,gCACDC,EAAYZ,SAAS,aAAeY,EAAYZ,SAAS,WAC1DY,EAAYZ,SAAS,WAAaY,EAAYZ,SAAS,SAC/DW,EAAU,0BACDC,EAAYZ,SAAS,cAC9BW,EAAU,4BAEd,CAEA,OACE1I,EAAAA,EAAAA,KAAA,QAAMjB,UAAW,2EAA2E2J,IAAU5J,SACnG2J,GACI,EAIX,OACE1I,EAAAA,EAAAA,MAAA,OACEhB,UAAW,oHAAoHA,IAC/H,cAAaW,EAAOZ,SAAA,EAGlBvC,GAASkJ,KACT1F,EAAAA,EAAAA,MAAA,OAAKhB,UAAW,sCAAsCE,IAAkBH,SAAA,CACpD,kBAAVvC,GACNyD,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,sCAAqCD,SAAEvC,IAErDA,EAEsB,kBAAhBkJ,GACNzF,EAAAA,EAAAA,KAAA,KAAGjB,UAAU,6BAA4BD,SAAE2G,IAE3CA,MAMN1F,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,kGAAiGD,SAAA,EAC9GiB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,kBAAiBD,SAAA,EAC9BkB,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,uEAAsED,UACnFkB,EAAAA,EAAAA,KAACoF,EAAmB,CAACrG,UAAU,6BAEjCiB,EAAAA,EAAAA,KAAA,SACE1D,KAAK,OACLsM,YAAY,YACZ7J,UAAU,sMACV8C,MAAO2E,EACPqC,SAAWC,IACTrC,EAAcqC,EAAEC,OAAOlH,OACvB8E,EAAe,EAAE,EAEnB,cAAa,GAAGjH,iBAIpBK,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,8BAA6BD,SAAA,CACzC8H,EAAa5E,OAAS,IACrBjC,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,8BAA6BD,SAAA,EAC1CiB,EAAAA,EAAAA,MAAA,QAAMhB,UAAU,wBAAuBD,SAAA,CAAE8H,EAAa5E,OAAO,gBAC7DhC,EAAAA,EAAAA,KAAA,UACEjB,UAAU,uGACVM,QAASA,KACPwH,EAAgB,IACZd,GAAmBA,EAAkB,GAAG,EAE9C,cAAa,GAAGrG,oBAAyBZ,SAC1C,aAKJkH,SAKLhG,EAAAA,EAAAA,KAAA,OAAKjB,UAAW,mBAAmBC,IAAgBF,SAChDW,GACCO,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,yCAAwCD,UACrDkB,EAAAA,EAAAA,KAACgJ,EAAAA,EAAc,CAAC9I,KAAK,KAAKC,QAAQ,eAGpCJ,EAAAA,EAAAA,MAAA,SAAOhB,UAAU,sCAAqCD,SAAA,EACpDkB,EAAAA,EAAAA,KAAA,SAAOjB,UAAU,aAAYD,UAC3BiB,EAAAA,EAAAA,MAAA,MAAAjB,SAAA,CACGgH,IACC9F,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,iBAAgBD,UAC5BkB,EAAAA,EAAAA,KAAA,SACE1D,KAAK,WACLyC,UAAU,kEACV8J,SAtHKI,IACvB,MAAMC,EAAkBD,EAAMF,OAAOI,QACjClH,MAAMmH,KAAK,CAAEpH,OAAQmG,EAAcnG,SAAU,CAACqH,EAAGC,IAAMA,IACvD,GAIJ,GAFAzC,EAAgBqC,GAEZnD,EAAmB,CACrB,MAAMwD,EAAgBL,EACnB/L,KAAIqM,GAAOrB,EAAcqB,KACzBjM,QAAQkM,QAA6B3H,IAAT2H,IAC/B1D,EAAkBwD,EACpB,GA2GkBJ,QAASvC,EAAa5E,SAAWmG,EAAcnG,QAAUmG,EAAcnG,OAAS,EAChF,cAAa,GAAGtC,mBAIrB6F,EAAQpI,KAAKuM,IACZ1J,EAAAA,EAAAA,KAAA,MAEEjB,UAAW,kBAAkB2K,EAAOC,OAAS,qEAAqED,EAAOE,SAAW,mCAAqC,qCAAqCF,EAAOG,MAAQH,EAAOG,MAAQ,MAAMH,EAAO3K,WAAa,KACtQM,QAASA,IAAMqK,EAAOE,UAtNpBpG,KAClB,IAAI+C,EAA4B,MAC5BF,GAAcA,EAAW7C,MAAQA,GAAgC,QAAzB6C,EAAWE,YACrDA,EAAY,QAEdD,EAAc,CAAE9C,MAAK+C,aAAY,EAiNiBuD,CAAWJ,EAAOlG,KACpD,cAAa,GAAG9D,YAAiBgK,EAAOlG,MAAM1E,UAE9CiB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,8BAA6BD,SAAA,EAC1CkB,EAAAA,EAAAA,KAAA,QAAAlB,SAAO4K,EAAOK,QACbL,EAAOE,WACN5J,EAAAA,EAAAA,KAAA,QAAMjB,UAAW,oCACL,OAAVsH,QAAU,IAAVA,OAAU,EAAVA,EAAY7C,OAAQkG,EAAOlG,IAAM,eAAiB,iBACjD1E,UACU,OAAVuH,QAAU,IAAVA,OAAU,EAAVA,EAAY7C,OAAQkG,EAAOlG,KAAgC,QAAzB6C,EAAWE,WAC1CvG,EAAAA,EAAAA,KAACqF,EAAa,CAACtG,UAAU,aACf,OAAVsH,QAAU,IAAVA,OAAU,EAAVA,EAAY7C,OAAQkG,EAAOlG,KAAgC,SAAzB6C,EAAWE,WAC3CvG,EAAAA,EAAAA,KAACgK,EAAAA,EAAe,CAACjL,UAAU,aAC3BiB,EAAAA,EAAAA,KAAA,QAAMjB,UAAU,gBAAeD,SAAC,iBAfvC4K,EAAOlG,aAuBpBxD,EAAAA,EAAAA,KAAA,SAAOjB,UAAU,oCAAmCD,SACjDqJ,EAAcnG,OAAS,EACtBmG,EAAchL,KAAI,CAACsK,EAAKwC,KACtBlK,EAAAA,EAAAA,MAAA,MAEEhB,UAAW,qCACTyG,EAAa,iBAAmB,MAC9BoB,EAAamB,SAASkC,GAAS,0BAA4B,2BAC7DnD,IAAemD,EAAQ,aAAe,2BACtC/D,EAAeA,EAAauB,EAAKwC,GAAS,KAC5C5K,QAASA,IAAMmG,GAAcA,EAAWiC,GACxCyC,aAAcA,IAAMnD,EAAckD,GAClCE,aAAcA,IAAMpD,EAAc,MAClC,cAAa,GAAGrH,SAAcuK,IAAQnL,SAAA,CAErCgH,IACC9F,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,8BAA6BD,UACzCkB,EAAAA,EAAAA,KAAA,SACE1D,KAAK,WACLyC,UAAU,kEACVoK,QAASvC,EAAamB,SAASkC,GAC/BpB,SAAUA,OACVxJ,QAAUyJ,GAjMVsB,EAACH,EAAehB,KACtCA,EAAMoB,kBAEN,MAAMnB,EAAkB,IAAItC,GAE5B,GAAIA,EAAamB,SAASkC,GAAQ,CAChC,MAAMT,EAAMN,EAAgBoB,QAAQL,GACpCf,EAAgBqB,OAAOf,EAAK,EAC9B,MACEN,EAAgBsB,KAAKP,GAKvB,GAFApD,EAAgBqC,GAEZnD,EAAmB,CACrB,MAAMwD,EAAgBL,EACnB/L,KAAIqM,GAAOrB,EAAcqB,KACzBjM,QAAQkM,QAA6B3H,IAAT2H,IAC/B1D,EAAkBwD,EACpB,GA8KsCa,CAAgBH,EAAOnB,GACvC,cAAa,GAAGpJ,SAAcuK,iBAInC1E,EAAQpI,KAAKuM,IACZ1J,EAAAA,EAAAA,KAAA,MAEEjB,UAAW,oFAAoF2K,EAAOC,OAAS,UAAUD,EAAO3K,WAAa,KAC7I,cAAa,GAAGW,SAAcuK,UAAcP,EAAOlG,MAAM1E,SAExD4K,EAAOe,OACJf,EAAOe,OAAOhD,EAAIiC,EAAOlG,KAAMiE,GAC/BiC,EAAOlG,IAAIsE,cAAcC,SAAS,UAChCS,EAAkBf,EAAIiC,EAAOlG,MAC7BiE,EAAIiC,EAAOlG,MARZkG,EAAOlG,SAzBXyG,MAuCTjK,EAAAA,EAAAA,KAAA,MAAAlB,UACEkB,EAAAA,EAAAA,KAAA,MACE0K,QAASnF,EAAQvD,QAAU8D,EAAa,EAAI,GAC5C/G,UAAU,uCACV,cAAa,GAAGW,kBAAuBZ,SAEtCmH,aAUdP,GAAcsC,EAAa,IAC1BjI,EAAAA,EAAAA,MAAA,OAAKhB,UAAW,wEAAwEG,IAAkBJ,SAAA,EACxGiB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,wBAAuBD,SAAA,CAAC,YAC1B4H,EAAc,GAAKf,EAAY,EAAE,OAAKsC,KAAK/D,IAAIwC,EAAcf,EAAU6B,EAAaxF,QAAQ,OAAKwF,EAAaxF,OAAO,eAElIjC,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,iBAAgBD,SAAA,EAC7BkB,EAAAA,EAAAA,KAAA,UACEX,QAASA,IAAMiJ,EAAiBL,KAAK7D,IAAI,EAAGsC,EAAc,IAC1DiE,SAA0B,IAAhBjE,EACV3H,UAAW,iCACO,IAAhB2H,EACI,mCACA,mCAEN,cAAa,GAAGhH,oBAAyBZ,SAC1C,aAGAmD,MAAMmH,KAAK,CAAEpH,OAAQiG,KAAK/D,IAAI,EAAG8D,KAAe,CAACqB,EAAGC,KAEnD,IAAIsB,EAWJ,OATEA,EADE5C,GAAc,GAEPtB,GAAe,EADd4C,EAAI,EAGL5C,GAAesB,EAAa,EAC3BA,EAAa,EAAIsB,EAEjB5C,EAAc,EAAI4C,GAI5BtJ,EAAAA,EAAAA,KAAA,UAEEX,QAASA,IAAMiJ,EAAiBsC,GAChC7L,UAAW,iCACT2H,IAAgBkE,EACZ,wBACA,mCAEN,cAAa,GAAGlL,gBAAqBkL,IAAU9L,SAE9C8L,GATIA,EAUE,KAGb5K,EAAAA,EAAAA,KAAA,UACEX,QAASA,IAAMiJ,EAAiBL,KAAK/D,IAAI8D,EAAYtB,EAAc,IACnEiE,SAAUjE,IAAgBsB,EAC1BjJ,UAAW,iCACT2H,IAAgBsB,EACZ,mCACA,mCAEN,cAAa,GAAGtI,oBAAyBZ,SAC1C,iBAQb,CAEA,SAAemB,EAAAA,EAAAA,MAAKqF,E,yDCpZpB,MAAMuF,EAAgCjN,IAmB/B,IAnBgC,SACrCkB,EAAQ,QACRqB,EAAU,UAAS,KACnBD,EAAO,KAAI,UACXnB,EAAY,GAAE,QACdM,EAAO,SACPsL,GAAW,EAAK,KAChBrO,EAAO,SAAQ,KACf6C,EAAI,aACJ2L,EAAe,OAAM,UACrBC,GAAY,EAAK,QACjBtL,GAAU,EAAK,QACfuL,GAAU,EAAK,KACfC,EAAI,OACJlC,EAAM,IACNmC,EAAG,MACH3O,EAAK,UACL4O,EAAS,OACTzL,GACD9B,EACC,MAwBMwN,EAAgB,kKAtBC,CACrBC,QAAS,uEACTC,UAAW,0EACXC,QAAS,4FACTC,OAAQ,oEACRC,QAAS,0EACTC,KAAM,2EACNC,KAAM,kFAiBWxL,WAdC,CAClByL,GAAI,oBACJrL,GAAI,sBACJK,GAAI,oBACJC,GAAI,wBACJgL,GAAI,qBAUU3L,WAPQyK,EAAW,gCAAkC,yBAClDI,EAAY,SAAW,WACrBC,EAAU,eAAiB,qBAS5CjM,QAGE+M,GACJ/L,EAAAA,EAAAA,MAAAgM,EAAAA,SAAA,CAAAjN,SAAA,CACGW,IACCM,EAAAA,EAAAA,MAAA,OACEhB,UAAU,+CACVZ,MAAM,6BACNC,KAAK,OACLC,QAAQ,YACR,cAAY,OAAMS,SAAA,EAElBkB,EAAAA,EAAAA,KAAA,UACEjB,UAAU,aACViN,GAAG,KACHC,GAAG,KACHC,EAAE,KACF3N,OAAO,eACPD,YAAY,OAEd0B,EAAAA,EAAAA,KAAA,QACEjB,UAAU,aACVX,KAAK,eACLO,EAAE,uHAKPQ,GAAyB,SAAjB2L,IAA4BrL,IACnCO,EAAAA,EAAAA,KAAA,QAAMjB,UAAU,OAAMD,SAAEK,IAGzBL,EAEAK,GAAyB,UAAjB2L,IACP9K,EAAAA,EAAAA,KAAA,QAAMjB,UAAU,OAAMD,SAAEK,OAM9B,OAAI8L,GAEAjL,EAAAA,EAAAA,KAAA,KACEiL,KAAMA,EACNlM,UAAWqM,EACXrC,OAAQA,EACRmC,IAAKA,IAAmB,WAAXnC,EAAsB,2BAAwBjH,GAC3DzC,QAASA,EACT9C,MAAOA,EACP,aAAY4O,EACZ,cAAazL,EAAOZ,SAEnBgN,KAOL9L,EAAAA,EAAAA,KAAA,UACE1D,KAAMA,EACNyC,UAAWqM,EACX/L,QAASA,EACTsL,SAAUA,GAAYlL,EACtBlD,MAAOA,EACP,aAAY4O,EACZ,cAAazL,EAAOZ,SAEnBgN,GACM,EAIb,GAAe7L,EAAAA,EAAAA,MAAK4K,E,gDC9JpB,SAASsB,EAASvO,EAIfC,GAAQ,IAJQ,MACjBtB,EAAK,QACLuB,KACGC,GACJH,EACC,OAAoBI,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQxB,EAAqByB,EAAAA,cAAoB,QAAS,CAC3DrB,GAAImB,GACHvB,GAAS,KAAmByB,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,kaAEP,CACA,MACA,EADiCX,EAAAA,WAAiBmO,E", "sources": ["hooks/useEntityData.ts", "../node_modules/@heroicons/react/24/outline/esm/PencilIcon.js", "components/common/Card.tsx", "components/common/LoadingSpinner.tsx", "utils/validation.ts", "../node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ChevronUpIcon.js", "components/common/DataTable.tsx", "components/common/Button.tsx", "../node_modules/@heroicons/react/24/outline/esm/TrashIcon.js"], "sourcesContent": ["import { useState, useCallback, useEffect, useRef } from 'react';\r\nimport useNotification from './useNotification';\r\n\r\nexport interface EntityApi<T, IdType = string> {\r\n  getAll: (params?: any) => Promise<T[]>;\r\n  getById: (id: IdType) => Promise<T>;\r\n  create: (data: any) => Promise<T>;\r\n  update: (id: IdType, data: any) => Promise<T>;\r\n  delete: (id: IdType) => Promise<void>;\r\n}\r\n\r\nexport interface UseEntityDataOptions {\r\n  entityName: string;\r\n  initialFetch?: boolean;\r\n}\r\n\r\nexport const useEntityData = <T, IdType = string>(\r\n  apiService: EntityApi<T, IdType>,\r\n  options: UseEntityDataOptions\r\n) => {\r\n  const [entities, setEntities] = useState<T[]>([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<Error | null>(null);\r\n  const { showNotification } = useNotification();\r\n\r\n  // Use refs to store current values and avoid stale closures\r\n  const apiServiceRef = useRef(apiService);\r\n  const showNotificationRef = useRef(showNotification);\r\n  const entityNameRef = useRef(options.entityName);\r\n  const hasInitialFetched = useRef(false);\r\n\r\n  // Update refs when values change\r\n  useEffect(() => {\r\n    apiServiceRef.current = apiService;\r\n    showNotificationRef.current = showNotification;\r\n    entityNameRef.current = options.entityName;\r\n  });\r\n\r\n  const fetchEntities = useCallback(async (params?: any) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const data = await apiServiceRef.current.getAll(params);\r\n      setEntities(data);\r\n      return data;\r\n    } catch (err) {\r\n      const error = err as Error;\r\n      setError(error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to fetch ${entityNameRef.current}`\r\n      });\r\n      throw error;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []); // No dependencies needed due to refs\r\n\r\n  const getEntityById = useCallback(async (id: IdType) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const entity = await apiServiceRef.current.getById(id);\r\n      return entity;\r\n    } catch (err) {\r\n      const error = err as Error;\r\n      setError(error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to fetch ${entityNameRef.current}`\r\n      });\r\n      throw error;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []); // No dependencies needed due to refs\r\n\r\n  const createEntity = useCallback(async (data: any) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const newEntity = await apiServiceRef.current.create(data);\r\n      setEntities(prev => [...prev, newEntity]);\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: `${entityNameRef.current} created successfully`\r\n      });\r\n      return newEntity;\r\n    } catch (err) {\r\n      const error = err as Error;\r\n      setError(error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to create ${entityNameRef.current}`\r\n      });\r\n      throw error;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const updateEntity = useCallback(async (id: IdType, data: any) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const updatedEntity = await apiServiceRef.current.update(id, data);\r\n      setEntities(prev => prev.map(entity =>\r\n        (entity as any).id === id ? updatedEntity : entity\r\n      ));\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: `${entityNameRef.current} updated successfully`\r\n      });\r\n      return updatedEntity;\r\n    } catch (err) {\r\n      const error = err as Error;\r\n      setError(error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to update ${entityNameRef.current}`\r\n      });\r\n      throw error;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const deleteEntity = useCallback(async (id: IdType) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      await apiServiceRef.current.delete(id);\r\n      setEntities(prev => prev.filter(entity => (entity as any).id !== id));\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: `${entityNameRef.current} deleted successfully`\r\n      });\r\n    } catch (err) {\r\n      const error = err as Error;\r\n      setError(error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to delete ${entityNameRef.current}`\r\n      });\r\n      throw error;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Initial fetch effect - runs only once\r\n  useEffect(() => {\r\n    if (options.initialFetch !== false && !hasInitialFetched.current) {\r\n      console.log(`[useEntityData] Starting initial fetch for ${options.entityName}`);\r\n      hasInitialFetched.current = true;\r\n\r\n      const initialFetch = async () => {\r\n        setIsLoading(true);\r\n        setError(null);\r\n        try {\r\n          console.log(`[useEntityData] Calling API for ${options.entityName}`);\r\n          const data = await apiService.getAll();\r\n          console.log(`[useEntityData] Received data for ${options.entityName}:`, data);\r\n          setEntities(data);\r\n        } catch (err) {\r\n          const error = err as Error;\r\n          console.error(`[useEntityData] Error fetching ${options.entityName}:`, error);\r\n          setError(error);\r\n          showNotificationRef.current({\r\n            type: 'error',\r\n            title: 'Error',\r\n            message: `Failed to fetch ${options.entityName}`\r\n          });\r\n        } finally {\r\n          console.log(`[useEntityData] Finished fetch for ${options.entityName}`);\r\n          setIsLoading(false);\r\n        }\r\n      };\r\n\r\n      initialFetch();\r\n    }\r\n  }, [\r\n    apiService,\r\n    options.entityName,\r\n    options.initialFetch\r\n  ]); // Empty dependency array - runs only once on mount\r\n\r\n  return {\r\n    entities,\r\n    isLoading,\r\n    error,\r\n    fetchEntities,\r\n    getEntityById,\r\n    createEntity,\r\n    updateEntity,\r\n    deleteEntity,\r\n    setEntities // Expose setEntities for custom state updates\r\n  };\r\n};\r\n", "import * as React from \"react\";\nfunction PencilIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PencilIcon);\nexport default ForwardRef;", "/**\r\n * Card Component\r\n *\r\n * A reusable card component for displaying content in a contained box.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\nexport interface CardProps {\r\n  title?: string | ReactNode;\r\n  subtitle?: string | ReactNode;\r\n  children: ReactNode;\r\n  className?: string;\r\n  bodyClassName?: string;\r\n  headerClassName?: string;\r\n  footerClassName?: string;\r\n  icon?: ReactNode;\r\n  footer?: ReactNode;\r\n  onClick?: () => void;\r\n  hoverable?: boolean;\r\n  noPadding?: boolean;\r\n  bordered?: boolean;\r\n  loading?: boolean;\r\n  testId?: string;\r\n}\r\n\r\nconst Card: React.FC<CardProps> = ({\r\n  title,\r\n  subtitle,\r\n  children,\r\n  className = '',\r\n  bodyClassName = '',\r\n  headerClassName = '',\r\n  footerClassName = '',\r\n  icon,\r\n  footer,\r\n  onClick,\r\n  hoverable = false,\r\n  noPadding = false,\r\n  bordered = true,\r\n  loading = false,\r\n  testId,\r\n}) => {\r\n  // Base classes\r\n  const cardClasses = `\r\n    bg-white rounded-xl ${bordered ? 'border border-gray-100' : ''} overflow-hidden transition-all duration-300\r\n    ${hoverable ? 'hover:shadow-md hover:border-gray-200 transform hover:-translate-y-1' : 'shadow-sm'}\r\n    ${onClick ? 'cursor-pointer' : ''}\r\n    ${className}\r\n  `;\r\n\r\n  // Header classes\r\n  const headerClasses = `\r\n    px-6 py-4 border-b border-gray-100 flex items-center justify-between\r\n    ${headerClassName}\r\n  `;\r\n\r\n  // Body classes\r\n  const bodyClasses = `\r\n    ${noPadding ? '' : 'p-6'}\r\n    ${bodyClassName}\r\n  `;\r\n\r\n  // Footer classes\r\n  const footerClasses = `\r\n    px-6 py-4 bg-gray-50 border-t border-gray-100\r\n    ${footerClassName}\r\n  `;\r\n\r\n  // Loading skeleton\r\n  if (loading) {\r\n    return (\r\n      <div className={cardClasses} data-testid={testId}>\r\n        {(title || subtitle || icon) && (\r\n          <div className={headerClasses}>\r\n            <div className=\"w-full\">\r\n              {title && <div className=\"h-6 bg-gray-200 rounded w-1/3 animate-pulse\"></div>}\r\n              {subtitle && <div className=\"h-4 mt-2 bg-gray-200 rounded w-1/2 animate-pulse\"></div>}\r\n            </div>\r\n            {icon && <div className=\"h-8 w-8 bg-gray-200 rounded-full animate-pulse\"></div>}\r\n          </div>\r\n        )}\r\n\r\n        <div className={bodyClasses}>\r\n          <div className=\"h-24 bg-gray-200 rounded animate-pulse\"></div>\r\n        </div>\r\n\r\n        {footer && (\r\n          <div className={footerClasses}>\r\n            <div className=\"h-8 bg-gray-200 rounded w-1/4 animate-pulse\"></div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className={cardClasses}\r\n      onClick={onClick}\r\n      data-testid={testId}\r\n    >\r\n      {(title || subtitle || icon) && (\r\n        <div className={headerClasses}>\r\n          <div>\r\n            {typeof title === 'string' ? (\r\n              <h3 className=\"text-lg font-semibold text-primary\">{title}</h3>\r\n            ) : (\r\n              title\r\n            )}\r\n            {typeof subtitle === 'string' ? (\r\n              <p className=\"mt-1 text-sm text-gray-500\">{subtitle}</p>\r\n            ) : (\r\n              subtitle\r\n            )}\r\n          </div>\r\n          {icon && <div className=\"text-primary\">{icon}</div>}\r\n        </div>\r\n      )}\r\n\r\n      <div className={bodyClasses}>{children}</div>\r\n\r\n      {footer && (\r\n        <div className={footerClasses}>\r\n          {footer}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(Card);\r\n", "// src/components/common/LoadingSpinner.tsx\r\nimport React from 'react';\r\nimport './LoadingSpinner.css';\r\n\r\ninterface LoadingSpinnerProps {\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  variant?: 'spinner' | 'dots' | 'pulse' | 'ripple';\r\n  color?: string;\r\n  useCurrentColor?: boolean;\r\n}\r\n\r\nconst LoadingSpinner: React.FC<LoadingSpinnerProps> = ({\r\n  size = 'md',\r\n  className = '',\r\n  variant = 'spinner',\r\n  color = '#F28B22', // Primary color\r\n  useCurrentColor = false\r\n}) => {\r\n  const sizeMap = {\r\n    sm: { spinner: 'w-5 h-5', dots: 'w-1 h-1', pulse: 'w-4 h-4', ripple: 'w-6 h-6' },\r\n    md: { spinner: 'w-8 h-8', dots: 'w-1.5 h-1.5', pulse: 'w-6 h-6', ripple: 'w-10 h-10' },\r\n    lg: { spinner: 'w-12 h-12', dots: 'w-2 h-2', pulse: 'w-8 h-8', ripple: 'w-16 h-16' }\r\n  };\r\n\r\n  const currentColor = useCurrentColor ? 'currentColor' : color;\r\n\r\n  // Simple rotating ring spinner\r\n  if (variant === 'spinner') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`spinner-smooth rounded-full border-2 border-gray-200 ${sizeMap[size].spinner}`}\r\n          style={{\r\n            borderTopColor: currentColor,\r\n            borderRightColor: currentColor,\r\n          }}\r\n        />\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Three bouncing dots\r\n  if (variant === 'dots') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center space-x-1 dots-bounce ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`${sizeMap[size].dots} rounded-full dot`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <div\r\n          className={`${sizeMap[size].dots} rounded-full dot`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <div\r\n          className={`${sizeMap[size].dots} rounded-full dot`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Pulsing circle\r\n  if (variant === 'pulse') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`${sizeMap[size].pulse} rounded-full pulse-smooth`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Ripple effect\r\n  if (variant === 'ripple') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`${sizeMap[size].ripple} rounded-full ripple-effect`}\r\n          style={{ color: currentColor }}\r\n        >\r\n          <div\r\n            className={`${sizeMap[size].pulse} rounded-full pulse-smooth mx-auto`}\r\n            style={{ backgroundColor: currentColor }}\r\n          />\r\n        </div>\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\nexport default LoadingSpinner;", "/**\r\n * Validation Utilities\r\n * \r\n * This file provides a comprehensive form validation utility with both function-based\r\n * and rule-based validation approaches.\r\n */\r\n\r\n// Type definitions\r\nexport type ValidationRule = {\r\n  validator: (value: any, formData?: any) => boolean;\r\n  message: string;\r\n};\r\n\r\nexport type ValidationRules = Record<string, ValidationRule | ValidationRule[]>;\r\n\r\n// Individual validation functions\r\nexport const isValidEmail = (email: string): boolean => {\r\n  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\r\n  return emailRegex.test(email);\r\n};\r\n\r\nexport const isValidPhone = (phone: string): boolean => {\r\n  const phoneRegex = /^\\+?[0-9]{10,15}$/;\r\n  return phoneRegex.test(phone);\r\n};\r\n\r\nexport const isValidUrl = (url: string): boolean => {\r\n  try {\r\n    new URL(url);\r\n    return true;\r\n  } catch (error) {\r\n    return false;\r\n  }\r\n};\r\n\r\nexport const isRequired = (value: any): boolean => {\r\n  if (value === null || value === undefined) return false;\r\n  if (typeof value === 'string') return value.trim().length > 0;\r\n  if (Array.isArray(value)) return value.length > 0;\r\n  return true;\r\n};\r\n\r\nexport const minLength = (value: string, min: number): boolean => {\r\n  return value.length >= min;\r\n};\r\n\r\nexport const maxLength = (value: string, max: number): boolean => {\r\n  return value.length <= max;\r\n};\r\n\r\nexport const isNumeric = (value: string): boolean => {\r\n  return /^[0-9]+$/.test(value);\r\n};\r\n\r\nexport const isDecimal = (value: string): boolean => {\r\n  return /^[0-9]+(\\.[0-9]+)?$/.test(value);\r\n};\r\n\r\nexport const isAlphanumeric = (value: string): boolean => {\r\n  return /^[a-zA-Z0-9]+$/.test(value);\r\n};\r\n\r\nexport const isValidDate = (dateString: string): boolean => {\r\n  const date = new Date(dateString);\r\n  return !isNaN(date.getTime());\r\n};\r\n\r\nexport const doPasswordsMatch = (password: string, confirmPassword: string): boolean => {\r\n  return password === confirmPassword;\r\n};\r\n\r\nexport const isStrongPassword = (password: string): boolean => {\r\n  // Password must be at least 8 characters long\r\n  if (password.length < 8) return false;\r\n  \r\n  // Password must contain at least one uppercase letter\r\n  if (!/[A-Z]/.test(password)) return false;\r\n  \r\n  // Password must contain at least one lowercase letter\r\n  if (!/[a-z]/.test(password)) return false;\r\n  \r\n  // Password must contain at least one number\r\n  if (!/[0-9]/.test(password)) return false;\r\n  \r\n  // Password must contain at least one special character\r\n  if (!/[!@#$%^&*()_+\\-=[\\]{};':\"\\\\|,.<>/?]/.test(password)) return false;\r\n  \r\n  return true;\r\n};\r\n\r\n// Field validation\r\nexport const validateField = (\r\n  _name: string,\r\n  value: any,\r\n  rules: ValidationRule | ValidationRule[],\r\n  formData?: any\r\n): string => {\r\n  const ruleArray = Array.isArray(rules) ? rules : [rules];\r\n  \r\n  for (const rule of ruleArray) {\r\n    if (!rule.validator(value, formData)) {\r\n      return rule.message;\r\n    }\r\n  }\r\n  \r\n  return '';\r\n};\r\n\r\n// Form validation\r\nexport const validateForm = <T extends Record<string, any>>(\r\n  values: T,\r\n  validationRules: ValidationRules\r\n): Partial<Record<keyof T, string>> => {\r\n  const errors: Partial<Record<keyof T, string>> = {};\r\n  \r\n  Object.entries(validationRules).forEach(([fieldName, rules]) => {\r\n    const key = fieldName as keyof T;\r\n    const error = validateField(fieldName, values[key], rules, values);\r\n    if (error) {\r\n      errors[key] = error;\r\n    }\r\n  });\r\n  \r\n  return errors;\r\n};\r\n\r\n// Common validation rules\r\nexport const validationRules = {\r\n  required: (message: string = 'This field is required'): ValidationRule => ({\r\n    validator: isRequired,\r\n    message\r\n  }),\r\n  \r\n  email: (message: string = 'Please enter a valid email address'): ValidationRule => ({\r\n    validator: isValidEmail,\r\n    message\r\n  }),\r\n  \r\n  phone: (message: string = 'Please enter a valid phone number'): ValidationRule => ({\r\n    validator: isValidPhone,\r\n    message\r\n  }),\r\n  \r\n  url: (message: string = 'Please enter a valid URL'): ValidationRule => ({\r\n    validator: isValidUrl,\r\n    message\r\n  }),\r\n  \r\n  minLength: (min: number, message?: string): ValidationRule => ({\r\n    validator: (value: string) => minLength(value, min),\r\n    message: message || `Must be at least ${min} characters`\r\n  }),\r\n  \r\n  maxLength: (max: number, message?: string): ValidationRule => ({\r\n    validator: (value: string) => maxLength(value, max),\r\n    message: message || `Must be no more than ${max} characters`\r\n  }),\r\n  \r\n  numeric: (message: string = 'Please enter a numeric value'): ValidationRule => ({\r\n    validator: isNumeric,\r\n    message\r\n  }),\r\n  \r\n  decimal: (message: string = 'Please enter a valid decimal number'): ValidationRule => ({\r\n    validator: isDecimal,\r\n    message\r\n  }),\r\n  \r\n  alphanumeric: (message: string = 'Please use only letters and numbers'): ValidationRule => ({\r\n    validator: isAlphanumeric,\r\n    message\r\n  }),\r\n  \r\n  date: (message: string = 'Please enter a valid date'): ValidationRule => ({\r\n    validator: isValidDate,\r\n    message\r\n  }),\r\n  \r\n  password: (message: string = 'Password must be at least 8 characters and include uppercase, lowercase, number, and special character'): ValidationRule => ({\r\n    validator: isStrongPassword,\r\n    message\r\n  }),\r\n  \r\n  passwordMatch: (message: string = 'Passwords do not match'): ValidationRule => ({\r\n    validator: (value: string, formData?: any) => doPasswordsMatch(value, formData?.confirmPassword),\r\n    message\r\n  }),\r\n  \r\n  confirmPasswordMatch: (message: string = 'Passwords do not match'): ValidationRule => ({\r\n    validator: (value: string, formData?: any) => doPasswordsMatch(value, formData?.password),\r\n    message\r\n  }),\r\n\r\n  // Product-specific validation rules\r\n  sku: (message: string = 'Please enter a valid SKU'): ValidationRule => ({\r\n    validator: (value: string) => /^[A-Z0-9-_]{3,20}$/i.test(value),\r\n    message\r\n  }),\r\n\r\n  price: (message: string = 'Please enter a valid price'): ValidationRule => ({\r\n    validator: (value: number) => value > 0 && value <= 999999,\r\n    message\r\n  }),\r\n\r\n  stock: (message: string = 'Please enter a valid stock quantity'): ValidationRule => ({\r\n    validator: (value: number) => Number.isInteger(value) && value >= 0,\r\n    message\r\n  }),\r\n\r\n  minimumStock: (message: string = 'Please enter a valid minimum stock level'): ValidationRule => ({\r\n    validator: (value: number) => Number.isInteger(value) && value >= 0,\r\n    message\r\n  }),\r\n\r\n  stockConsistency: (message: string = 'Minimum stock cannot be greater than current stock'): ValidationRule => ({\r\n    validator: (minimumStock: number, formData?: any) => {\r\n      if (!formData || !formData.stock) return true;\r\n      return minimumStock <= formData.stock;\r\n    },\r\n    message\r\n  }),\r\n\r\n  arrayNotEmpty: (message: string = 'At least one item is required'): ValidationRule => ({\r\n    validator: (value: any[]) => Array.isArray(value) && value.length > 0,\r\n    message\r\n  }),\r\n\r\n  imageArray: (maxFiles: number = 10, message?: string): ValidationRule => ({\r\n    validator: (value: any[]) => {\r\n      if (!Array.isArray(value)) return false;\r\n      return value.length <= maxFiles;\r\n    },\r\n    message: message || `Maximum ${maxFiles} images allowed`\r\n  })\r\n};\r\n\r\n", "import * as React from \"react\";\nfunction MagnifyingGlassIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MagnifyingGlassIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ChevronUpIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m4.5 15.75 7.5-7.5 7.5 7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChevronUpIcon);\nexport default ForwardRef;", "/**\r\n * DataTable Component\r\n *\r\n * A reusable data table component with sorting, filtering, pagination, and row selection.\r\n */\r\n\r\nimport React, { useState, useMemo, memo } from 'react';\r\nimport { MagnifyingGlassIcon, ChevronUpIcon, ChevronDownIcon } from '@heroicons/react/24/outline';\r\nimport LoadingSpinner from './LoadingSpinner';\r\nimport { CONFIG } from '../../constants/config';\r\n\r\nexport interface Column<T = Record<string, any>> {\r\n  key: string;\r\n  label: string;\r\n  sortable?: boolean;\r\n  render?: (value: any, row: T) => React.ReactNode;\r\n  width?: string;\r\n  align?: 'left' | 'center' | 'right';\r\n  className?: string;\r\n}\r\n\r\nexport interface DataTableProps<T = Record<string, any>> {\r\n  columns: Column<T>[];\r\n  data: T[];\r\n  onRowClick?: ((row: T) => void) | undefined;\r\n  title?: string | React.ReactNode;\r\n  description?: string | React.ReactNode;\r\n  loading?: boolean;\r\n  pagination?: boolean;\r\n  pageSize?: number;\r\n  selectable?: boolean;\r\n  onSelectionChange?: (selectedRows: T[]) => void;\r\n  actions?: React.ReactNode;\r\n  emptyMessage?: string;\r\n  className?: string;\r\n  headerClassName?: string;\r\n  bodyClassName?: string;\r\n  footerClassName?: string;\r\n  rowClassName?: (row: T, index: number) => string;\r\n  initialSortKey?: string;\r\n  initialSortDirection?: 'asc' | 'desc';\r\n  testId?: string;\r\n}\r\n\r\nfunction DataTable<T extends Record<string, any>>({\r\n  columns,\r\n  data,\r\n  onRowClick,\r\n  title,\r\n  description,\r\n  loading = false,\r\n  pagination = true,\r\n  pageSize = CONFIG.DEFAULT_PAGE_SIZE,\r\n  selectable = true,\r\n  onSelectionChange,\r\n  actions,\r\n  emptyMessage = 'No results found',\r\n  className = '',\r\n  headerClassName = '',\r\n  bodyClassName = '',\r\n  footerClassName = '',\r\n  rowClassName,\r\n  initialSortKey,\r\n  initialSortDirection = 'asc',\r\n  testId,\r\n}: DataTableProps<T>) {\r\n  // State\r\n  const [sortConfig, setSortConfig] = useState<{\r\n    key: string;\r\n    direction: 'asc' | 'desc';\r\n  } | null>(initialSortKey ? { key: initialSortKey, direction: initialSortDirection } : null);\r\n\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [selectedRows, setSelectedRows] = useState<number[]>([]);\r\n  const [hoveredRow, setHoveredRow] = useState<number | null>(null);\r\n\r\n  // Sorting\r\n  const handleSort = (key: string) => {\r\n    let direction: 'asc' | 'desc' = 'asc';\r\n    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {\r\n      direction = 'desc';\r\n    }\r\n    setSortConfig({ key, direction });\r\n  };\r\n\r\n  const sortedData = useMemo(() => {\r\n    if (!sortConfig) return data;\r\n\r\n    return [...data].sort((a, b) => {\r\n      const aValue = a[sortConfig.key];\r\n      const bValue = b[sortConfig.key];\r\n\r\n      // Handle null or undefined values\r\n      if (aValue == null && bValue == null) return 0;\r\n      if (aValue == null) return sortConfig.direction === 'asc' ? -1 : 1;\r\n      if (bValue == null) return sortConfig.direction === 'asc' ? 1 : -1;\r\n\r\n      // Handle different data types\r\n      if (typeof aValue === 'string' && typeof bValue === 'string') {\r\n        return sortConfig.direction === 'asc'\r\n          ? aValue.localeCompare(bValue)\r\n          : bValue.localeCompare(aValue);\r\n      }\r\n\r\n      if (aValue < bValue) {\r\n        return sortConfig.direction === 'asc' ? -1 : 1;\r\n      }\r\n      if (aValue > bValue) {\r\n        return sortConfig.direction === 'asc' ? 1 : -1;\r\n      }\r\n      return 0;\r\n    });\r\n  }, [data, sortConfig]);\r\n\r\n  // Filtering\r\n  const filteredData = useMemo(() => {\r\n    if (!searchTerm) return sortedData;\r\n\r\n    return sortedData.filter((row) =>\r\n      Object.entries(row).some(([_key, value]) => {\r\n        // Skip filtering on complex objects\r\n        if (value === null || value === undefined) return false;\r\n        if (typeof value === 'object') return false;\r\n\r\n        return String(value).toLowerCase().includes(searchTerm.toLowerCase());\r\n      })\r\n    );\r\n  }, [sortedData, searchTerm]);\r\n\r\n  // Pagination\r\n  const totalPages = Math.ceil(filteredData.length / pageSize);\r\n  const paginatedData = useMemo(() => {\r\n    const startIndex = (currentPage - 1) * pageSize;\r\n    return filteredData.slice(startIndex, startIndex + pageSize);\r\n  }, [filteredData, currentPage, pageSize]);\r\n\r\n  const handlePageChange = (page: number) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  // Row selection\r\n  const handleRowSelect = (index: number, event: React.MouseEvent) => {\r\n    event.stopPropagation();\r\n\r\n    const newSelectedRows = [...selectedRows];\r\n\r\n    if (selectedRows.includes(index)) {\r\n      const idx = newSelectedRows.indexOf(index);\r\n      newSelectedRows.splice(idx, 1);\r\n    } else {\r\n      newSelectedRows.push(index);\r\n    }\r\n\r\n    setSelectedRows(newSelectedRows);\r\n\r\n    if (onSelectionChange) {\r\n      const selectedItems = newSelectedRows\r\n        .map(idx => paginatedData[idx])\r\n        .filter((item): item is T => item !== undefined);\r\n      onSelectionChange(selectedItems);\r\n    }\r\n  };\r\n\r\n  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const newSelectedRows = event.target.checked\r\n      ? Array.from({ length: paginatedData.length }, (_, i) => i)\r\n      : [];\r\n\r\n    setSelectedRows(newSelectedRows);\r\n\r\n    if (onSelectionChange) {\r\n      const selectedItems = newSelectedRows\r\n        .map(idx => paginatedData[idx])\r\n        .filter((item): item is T => item !== undefined);\r\n      onSelectionChange(selectedItems);\r\n    }\r\n  };\r\n\r\n  // Status badge renderer\r\n  const renderStatusBadge = (status: string) => {\r\n    let bgColor = 'bg-gray-100 text-gray-800';\r\n\r\n    if (typeof status === 'string') {\r\n      const statusLower = status.toLowerCase();\r\n\r\n      if (statusLower.includes('active') || statusLower.includes('approved') ||\r\n          statusLower.includes('verified') || statusLower.includes('completed') ||\r\n          statusLower.includes('success')) {\r\n        bgColor = 'bg-green-100 text-green-800';\r\n      } else if (statusLower.includes('pending') || statusLower.includes('processing')) {\r\n        bgColor = 'bg-yellow-100 text-yellow-800';\r\n      } else if (statusLower.includes('rejected') || statusLower.includes('banned') ||\r\n                statusLower.includes('failed') || statusLower.includes('error')) {\r\n        bgColor = 'bg-red-100 text-red-800';\r\n      } else if (statusLower.includes('inactive')) {\r\n        bgColor = 'bg-gray-100 text-gray-800';\r\n      }\r\n    }\r\n\r\n    return (\r\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${bgColor}`}>\r\n        {status}\r\n      </span>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-md ${className}`}\r\n      data-testid={testId}\r\n    >\r\n      {/* Header */}\r\n      {(title || description) && (\r\n        <div className={`px-6 py-4 border-b border-gray-100 ${headerClassName}`}>\r\n          {typeof title === 'string' ? (\r\n            <h3 className=\"text-lg font-semibold text-gray-800\">{title}</h3>\r\n          ) : (\r\n            title\r\n          )}\r\n          {typeof description === 'string' ? (\r\n            <p className=\"mt-1 text-sm text-gray-500\">{description}</p>\r\n          ) : (\r\n            description\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      {/* Search and Actions */}\r\n      <div className=\"p-4 border-b border-gray-100 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\r\n        <div className=\"relative flex-1\">\r\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n            <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\r\n          </div>\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"Search...\"\r\n            className=\"block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200\"\r\n            value={searchTerm}\r\n            onChange={(e) => {\r\n              setSearchTerm(e.target.value);\r\n              setCurrentPage(1); // Reset to first page on search\r\n            }}\r\n            data-testid={`${testId}-search`}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-2\">\r\n          {selectedRows.length > 0 && (\r\n            <div className=\"flex items-center space-x-2\">\r\n              <span className=\"text-sm text-gray-500\">{selectedRows.length} selected</span>\r\n              <button\r\n                className=\"px-3 py-1.5 bg-red-50 text-red-600 rounded-md text-sm font-medium hover:bg-red-100 transition-colors\"\r\n                onClick={() => {\r\n                  setSelectedRows([]);\r\n                  if (onSelectionChange) onSelectionChange([]);\r\n                }}\r\n                data-testid={`${testId}-clear-selection`}\r\n              >\r\n                Clear\r\n              </button>\r\n            </div>\r\n          )}\r\n          {actions}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Table */}\r\n      <div className={`overflow-x-auto ${bodyClassName}`}>\r\n        {loading ? (\r\n          <div className=\"flex justify-center items-center py-20\">\r\n            <LoadingSpinner size=\"lg\" variant=\"spinner\" />\r\n          </div>\r\n        ) : (\r\n          <table className=\"min-w-full divide-y divide-gray-100\">\r\n            <thead className=\"bg-gray-50\">\r\n              <tr>\r\n                {selectable && (\r\n                  <th className=\"w-12 px-6 py-3\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      className=\"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary\"\r\n                      onChange={handleSelectAll}\r\n                      checked={selectedRows.length === paginatedData.length && paginatedData.length > 0}\r\n                      data-testid={`${testId}-select-all`}\r\n                    />\r\n                  </th>\r\n                )}\r\n                {columns.map((column) => (\r\n                  <th\r\n                    key={column.key}\r\n                    className={`px-6 py-3 text-${column.align || 'left'} text-xs font-medium text-gray-500 uppercase tracking-wider ${column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''} transition-colors duration-200 ${column.width ? column.width : ''} ${column.className || ''}`}\r\n                    onClick={() => column.sortable && handleSort(column.key)}\r\n                    data-testid={`${testId}-column-${column.key}`}\r\n                  >\r\n                    <div className=\"flex items-center space-x-1\">\r\n                      <span>{column.label}</span>\r\n                      {column.sortable && (\r\n                        <span className={`transition-colors duration-200 ${\r\n                          sortConfig?.key === column.key ? 'text-primary' : 'text-gray-400'\r\n                        }`}>\r\n                          {sortConfig?.key === column.key && sortConfig.direction === 'asc'\r\n                            ? <ChevronUpIcon className=\"h-4 w-4\" />\r\n                            : sortConfig?.key === column.key && sortConfig.direction === 'desc'\r\n                              ? <ChevronDownIcon className=\"h-4 w-4\" />\r\n                              : <span className=\"text-gray-300\">↕</span>}\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                  </th>\r\n                ))}\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"bg-white divide-y divide-gray-100\">\r\n              {paginatedData.length > 0 ? (\r\n                paginatedData.map((row, index) => (\r\n                  <tr\r\n                    key={index}\r\n                    className={`group transition-all duration-200 ${\r\n                      onRowClick ? 'cursor-pointer' : ''\r\n                    } ${selectedRows.includes(index) ? 'bg-primary bg-opacity-5' : ''}\r\n                    ${hoveredRow === index ? 'bg-gray-50' : ''}\r\n                    ${rowClassName ? rowClassName(row, index) : ''}`}\r\n                    onClick={() => onRowClick && onRowClick(row)}\r\n                    onMouseEnter={() => setHoveredRow(index)}\r\n                    onMouseLeave={() => setHoveredRow(null)}\r\n                    data-testid={`${testId}-row-${index}`}\r\n                  >\r\n                    {selectable && (\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          className=\"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary\"\r\n                          checked={selectedRows.includes(index)}\r\n                          onChange={() => {}} // Empty handler to avoid React warning about controlled component\r\n                          onClick={(e) => handleRowSelect(index, e)}\r\n                          data-testid={`${testId}-row-${index}-checkbox`}\r\n                        />\r\n                      </td>\r\n                    )}\r\n                    {columns.map((column) => (\r\n                      <td\r\n                        key={column.key}\r\n                        className={`px-6 py-4 whitespace-nowrap text-sm text-gray-600 group-hover:text-gray-900 text-${column.align || 'left'} ${column.className || ''}`}\r\n                        data-testid={`${testId}-row-${index}-cell-${column.key}`}\r\n                      >\r\n                        {column.render\r\n                          ? column.render(row[column.key], row)\r\n                          : column.key.toLowerCase().includes('status')\r\n                            ? renderStatusBadge(row[column.key])\r\n                            : row[column.key]}\r\n                      </td>\r\n                    ))}\r\n                  </tr>\r\n                ))\r\n              ) : (\r\n                <tr>\r\n                  <td\r\n                    colSpan={columns.length + (selectable ? 1 : 0)}\r\n                    className=\"px-6 py-10 text-center text-gray-500\"\r\n                    data-testid={`${testId}-empty-message`}\r\n                  >\r\n                    {emptyMessage}\r\n                  </td>\r\n                </tr>\r\n              )}\r\n            </tbody>\r\n          </table>\r\n        )}\r\n      </div>\r\n\r\n      {/* Pagination */}\r\n      {pagination && totalPages > 1 && (\r\n        <div className={`px-6 py-4 border-t border-gray-100 flex items-center justify-between ${footerClassName}`}>\r\n          <div className=\"text-sm text-gray-500\">\r\n            Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, filteredData.length)} of {filteredData.length} entries\r\n          </div>\r\n          <div className=\"flex space-x-1\">\r\n            <button\r\n              onClick={() => handlePageChange(Math.max(1, currentPage - 1))}\r\n              disabled={currentPage === 1}\r\n              className={`px-3 py-1 rounded-md text-sm ${\r\n                currentPage === 1\r\n                  ? 'text-gray-400 cursor-not-allowed'\r\n                  : 'text-gray-700 hover:bg-gray-100'\r\n              }`}\r\n              data-testid={`${testId}-pagination-prev`}\r\n            >\r\n              Previous\r\n            </button>\r\n            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\r\n              // Show pages around current page\r\n              let pageNum: number;\r\n              if (totalPages <= 5) {\r\n                pageNum = i + 1;\r\n              } else if (currentPage <= 3) {\r\n                pageNum = i + 1;\r\n              } else if (currentPage >= totalPages - 2) {\r\n                pageNum = totalPages - 4 + i;\r\n              } else {\r\n                pageNum = currentPage - 2 + i;\r\n              }\r\n\r\n              return (\r\n                <button\r\n                  key={pageNum}\r\n                  onClick={() => handlePageChange(pageNum)}\r\n                  className={`px-3 py-1 rounded-md text-sm ${\r\n                    currentPage === pageNum\r\n                      ? 'bg-primary text-white'\r\n                      : 'text-gray-700 hover:bg-gray-100'\r\n                  }`}\r\n                  data-testid={`${testId}-pagination-${pageNum}`}\r\n                >\r\n                  {pageNum}\r\n                </button>\r\n              );\r\n            })}\r\n            <button\r\n              onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}\r\n              disabled={currentPage === totalPages}\r\n              className={`px-3 py-1 rounded-md text-sm ${\r\n                currentPage === totalPages\r\n                  ? 'text-gray-400 cursor-not-allowed'\r\n                  : 'text-gray-700 hover:bg-gray-100'\r\n              }`}\r\n              data-testid={`${testId}-pagination-next`}\r\n            >\r\n              Next\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default memo(DataTable) as typeof DataTable;\r\n\r\n\r\n\r\n\r\n", "/**\r\n * Button Component\r\n * \r\n * A reusable button component with various styles and states.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\nexport type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'danger' | 'success' | 'text' | 'link';\r\nexport type ButtonSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';\r\n\r\nexport interface ButtonProps {\r\n  children: ReactNode;\r\n  variant?: ButtonVariant;\r\n  size?: ButtonSize;\r\n  className?: string;\r\n  onClick?: () => void;\r\n  disabled?: boolean;\r\n  type?: 'button' | 'submit' | 'reset';\r\n  icon?: ReactNode;\r\n  iconPosition?: 'left' | 'right';\r\n  fullWidth?: boolean;\r\n  loading?: boolean;\r\n  rounded?: boolean;\r\n  href?: string;\r\n  target?: string;\r\n  rel?: string;\r\n  title?: string;\r\n  ariaLabel?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst Button: React.FC<ButtonProps> = ({\r\n  children,\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  onClick,\r\n  disabled = false,\r\n  type = 'button',\r\n  icon,\r\n  iconPosition = 'left',\r\n  fullWidth = false,\r\n  loading = false,\r\n  rounded = false,\r\n  href,\r\n  target,\r\n  rel,\r\n  title,\r\n  ariaLabel,\r\n  testId,\r\n}) => {\r\n  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2';\r\n  \r\n  const variantClasses = {\r\n    primary: 'bg-primary text-white hover:bg-primary/90 focus-visible:ring-primary',\r\n    secondary: 'bg-gray-200 text-gray-800 hover:bg-gray-300 focus-visible:ring-gray-300',\r\n    outline: 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus-visible:ring-primary',\r\n    danger: 'bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500',\r\n    success: 'bg-green-600 text-white hover:bg-green-700 focus-visible:ring-green-500',\r\n    text: 'bg-transparent text-primary hover:bg-gray-100 focus-visible:ring-primary',\r\n    link: 'bg-transparent text-primary hover:underline focus-visible:ring-transparent p-0',\r\n  };\r\n  \r\n  const sizeClasses = {\r\n    xs: 'text-xs px-2 py-1',\r\n    sm: 'text-xs px-3 py-1.5',\r\n    md: 'text-sm px-4 py-2',\r\n    lg: 'text-base px-5 py-2.5',\r\n    xl: 'text-lg px-6 py-3',\r\n  };\r\n  \r\n  const disabledClasses = disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer';\r\n  const widthClass = fullWidth ? 'w-full' : '';\r\n  const roundedClass = rounded ? 'rounded-full' : 'rounded-lg';\r\n  \r\n  const buttonClasses = `\r\n    ${baseClasses}\r\n    ${variantClasses[variant]}\r\n    ${sizeClasses[size]}\r\n    ${disabledClasses}\r\n    ${widthClass}\r\n    ${roundedClass}\r\n    ${className}\r\n  `;\r\n  \r\n  const content = (\r\n    <>\r\n      {loading && (\r\n        <svg\r\n          className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-current\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n          aria-hidden=\"true\"\r\n        >\r\n          <circle\r\n            className=\"opacity-25\"\r\n            cx=\"12\"\r\n            cy=\"12\"\r\n            r=\"10\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"4\"\r\n          />\r\n          <path\r\n            className=\"opacity-75\"\r\n            fill=\"currentColor\"\r\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n          />\r\n        </svg>\r\n      )}\r\n\r\n      {icon && iconPosition === 'left' && !loading && (\r\n        <span className=\"mr-2\">{icon}</span>\r\n      )}\r\n\r\n      {children}\r\n\r\n      {icon && iconPosition === 'right' && (\r\n        <span className=\"ml-2\">{icon}</span>\r\n      )}\r\n    </>\r\n  );\r\n  \r\n  // If href is provided, render an anchor tag\r\n  if (href) {\r\n    return (\r\n      <a\r\n        href={href}\r\n        className={buttonClasses}\r\n        target={target}\r\n        rel={rel || (target === '_blank' ? 'noopener noreferrer' : undefined)}\r\n        onClick={onClick}\r\n        title={title}\r\n        aria-label={ariaLabel}\r\n        data-testid={testId}\r\n      >\r\n        {content}\r\n      </a>\r\n    );\r\n  }\r\n  \r\n  // Otherwise render a button\r\n  return (\r\n    <button\r\n      type={type}\r\n      className={buttonClasses}\r\n      onClick={onClick}\r\n      disabled={disabled || loading}\r\n      title={title}\r\n      aria-label={ariaLabel}\r\n      data-testid={testId}\r\n    >\r\n      {content}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default memo(Button);\r\n", "import * as React from \"react\";\nfunction TrashIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(TrashIcon);\nexport default ForwardRef;"], "names": ["useEntityData", "apiService", "options", "entities", "setEntities", "useState", "isLoading", "setIsLoading", "error", "setError", "showNotification", "useNotification", "apiServiceRef", "useRef", "showNotificationRef", "entityNameRef", "entityName", "hasInitialFetched", "useEffect", "current", "fetchEntities", "useCallback", "async", "data", "getAll", "params", "err", "type", "title", "message", "getEntityById", "getById", "id", "createEntity", "newEntity", "create", "prev", "updateEntity", "updatedEntity", "update", "map", "entity", "deleteEntity", "delete", "filter", "initialFetch", "console", "log", "PencilIcon", "_ref", "svgRef", "titleId", "props", "React", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "strokeLinecap", "strokeLinejoin", "d", "Card", "subtitle", "children", "className", "bodyClassName", "headerClassName", "footerClassName", "icon", "footer", "onClick", "hoverable", "noPadding", "bordered", "loading", "testId", "cardClasses", "headerClasses", "bodyClasses", "footerClasses", "_jsxs", "_jsx", "memo", "size", "variant", "color", "useCurrentColor", "sizeMap", "sm", "spinner", "dots", "pulse", "ripple", "md", "lg", "currentColor", "role", "style", "borderTopColor", "borderRightColor", "backgroundColor", "isValidEmail", "email", "test", "isValidPhone", "phone", "isValidUrl", "url", "URL", "isRequired", "value", "undefined", "trim", "length", "Array", "isArray", "isNumeric", "isDecimal", "isAlphanumeric", "isValidDate", "dateString", "date", "Date", "isNaN", "getTime", "doPasswordsMatch", "password", "confirmPassword", "isStrongPassword", "validateForm", "values", "validationRules", "errors", "entries", "for<PERSON>ach", "fieldName", "rules", "key", "validateField", "_name", "formData", "ruleArray", "rule", "validator", "required", "arguments", "<PERSON><PERSON><PERSON><PERSON>", "min", "max<PERSON><PERSON><PERSON>", "max", "numeric", "decimal", "alphanumeric", "passwordMatch", "confirmPasswordMatch", "sku", "price", "stock", "Number", "isInteger", "minimumStock", "stockConsistency", "arrayNotEmpty", "imageArray", "maxFiles", "MagnifyingGlassIcon", "ChevronUpIcon", "DataTable", "columns", "onRowClick", "description", "pagination", "pageSize", "CONFIG", "DEFAULT_PAGE_SIZE", "selectable", "onSelectionChange", "actions", "emptyMessage", "rowClassName", "initialSortKey", "initialSortDirection", "sortConfig", "setSortConfig", "direction", "searchTerm", "setSearchTerm", "currentPage", "setCurrentPage", "selectedRows", "setSelectedRows", "hoveredRow", "setHoveredRow", "sortedData", "useMemo", "sort", "a", "b", "aValue", "bValue", "localeCompare", "filteredData", "row", "some", "_ref2", "_key", "String", "toLowerCase", "includes", "totalPages", "Math", "ceil", "paginatedData", "startIndex", "slice", "handlePageChange", "page", "renderStatusBadge", "status", "bgColor", "statusLower", "placeholder", "onChange", "e", "target", "LoadingSpinner", "event", "newSelectedRows", "checked", "from", "_", "i", "selectedItems", "idx", "item", "column", "align", "sortable", "width", "handleSort", "label", "ChevronDownIcon", "index", "onMouseEnter", "onMouseLeave", "handleRowSelect", "stopPropagation", "indexOf", "splice", "push", "render", "colSpan", "disabled", "pageNum", "<PERSON><PERSON>", "iconPosition", "fullWidth", "rounded", "href", "rel", "aria<PERSON><PERSON><PERSON>", "buttonClasses", "primary", "secondary", "outline", "danger", "success", "text", "link", "xs", "xl", "content", "_Fragment", "cx", "cy", "r", "TrashIcon"], "sourceRoot": ""}