"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[412],{3927:(e,t,a)=>{a.d(t,{A:()=>s});a(5043);var r=a(579);const s=e=>{let{size:t="md",className:a="",variant:s="spinner",color:i="#F28B22",useCurrentColor:n=!1}=e;const l={sm:{spinner:"w-5 h-5",dots:"w-1 h-1",pulse:"w-4 h-4",ripple:"w-6 h-6"},md:{spinner:"w-8 h-8",dots:"w-1.5 h-1.5",pulse:"w-6 h-6",ripple:"w-10 h-10"},lg:{spinner:"w-12 h-12",dots:"w-2 h-2",pulse:"w-8 h-8",ripple:"w-16 h-16"}},c=n?"currentColor":i;return"spinner"===s?(0,r.jsxs)("div",{className:`flex justify-center items-center ${a}`,role:"status","aria-label":"Loading",children:[(0,r.jsx)("div",{className:`spinner-smooth rounded-full border-2 border-gray-200 ${l[t].spinner}`,style:{borderTopColor:c,borderRightColor:c}}),(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"dots"===s?(0,r.jsxs)("div",{className:`flex justify-center items-center space-x-1 dots-bounce ${a}`,role:"status","aria-label":"Loading",children:[(0,r.jsx)("div",{className:`${l[t].dots} rounded-full dot`,style:{backgroundColor:c}}),(0,r.jsx)("div",{className:`${l[t].dots} rounded-full dot`,style:{backgroundColor:c}}),(0,r.jsx)("div",{className:`${l[t].dots} rounded-full dot`,style:{backgroundColor:c}}),(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"pulse"===s?(0,r.jsxs)("div",{className:`flex justify-center items-center ${a}`,role:"status","aria-label":"Loading",children:[(0,r.jsx)("div",{className:`${l[t].pulse} rounded-full pulse-smooth`,style:{backgroundColor:c}}),(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"ripple"===s?(0,r.jsxs)("div",{className:`flex justify-center items-center ${a}`,role:"status","aria-label":"Loading",children:[(0,r.jsx)("div",{className:`${l[t].ripple} rounded-full ripple-effect`,style:{color:c},children:(0,r.jsx)("div",{className:`${l[t].pulse} rounded-full pulse-smooth mx-auto`,style:{backgroundColor:c}})}),(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})]}):null}},7012:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(5043);function s(e,t){let{title:a,titleId:s,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},i),a?r.createElement("title",{id:s},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const i=r.forwardRef(s)},7098:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(5043);function s(e,t){let{title:a,titleId:s,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},i),a?r.createElement("title",{id:s},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const i=r.forwardRef(s)},7907:(e,t,a)=>{a.d(t,{A:()=>n});var r=a(5043),s=a(579);const i=e=>{let{children:t,variant:a="primary",size:r="md",className:i="",onClick:n,disabled:l=!1,type:c="button",icon:o,iconPosition:u="left",fullWidth:d=!1,loading:p=!1,rounded:y=!1,href:m,target:h,rel:g,title:f,ariaLabel:w,testId:b}=e;const S=`\n    inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2\n    ${{primary:"bg-primary text-white hover:bg-primary/90 focus-visible:ring-primary",secondary:"bg-gray-200 text-gray-800 hover:bg-gray-300 focus-visible:ring-gray-300",outline:"bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus-visible:ring-primary",danger:"bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500",success:"bg-green-600 text-white hover:bg-green-700 focus-visible:ring-green-500",text:"bg-transparent text-primary hover:bg-gray-100 focus-visible:ring-primary",link:"bg-transparent text-primary hover:underline focus-visible:ring-transparent p-0"}[a]}\n    ${{xs:"text-xs px-2 py-1",sm:"text-xs px-3 py-1.5",md:"text-sm px-4 py-2",lg:"text-base px-5 py-2.5",xl:"text-lg px-6 py-3"}[r]}\n    ${l?"opacity-60 cursor-not-allowed":"cursor-pointer"}\n    ${d?"w-full":""}\n    ${y?"rounded-full":"rounded-lg"}\n    ${i}\n  `,x=(0,s.jsxs)(s.Fragment,{children:[p&&(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-current",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","aria-hidden":"true",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),o&&"left"===u&&!p&&(0,s.jsx)("span",{className:"mr-2",children:o}),t,o&&"right"===u&&(0,s.jsx)("span",{className:"ml-2",children:o})]});return m?(0,s.jsx)("a",{href:m,className:S,target:h,rel:g||("_blank"===h?"noopener noreferrer":void 0),onClick:n,title:f,"aria-label":w,"data-testid":b,children:x}):(0,s.jsx)("button",{type:c,className:S,onClick:n,disabled:l||p,title:f,"aria-label":w,"data-testid":b,children:x})},n=(0,r.memo)(i)},8736:(e,t,a)=>{a.d(t,{h:()=>d});var r=a(5043),s=a(1568),i=a(4703),n=a(8479);const l=e=>({id:e.id,name:e.name||"",email:e.email,phone:e.phone||"",address:e.address||"",status:"banned"===e.status?"banned":"active",verificationStatus:e.verificationStatus,categories:e.categories?[e.categories]:[],contactPerson:e.contactPerson,logo:e.image||"",website:""}),c=e=>({id:e.id,name:e.name,sku:e.sku,category:e.category,price:e.price,stock:e.stock,minimumStock:10,status:e.status,description:e.description||"",image:e.image||"",images:e.image?[e.image]:[],attributes:[],variants:[],createdAt:e.createdDate||(new Date).toISOString(),updatedAt:e.updatedDate||(new Date).toISOString()}),o={getSuppliers:async e=>{try{const t=await s.A.get("/suppliers",{params:e});if(t.data&&"success"in t.data&&t.data.success){const e=t.data;if(e.data&&"suppliers"in e.data){return e.data.suppliers.map(l)}if(Array.isArray(e.data)){return e.data.map(l)}return[]}return(Array.isArray(t.data)?t.data:[]).map(l)}catch(t){throw(0,i.hS)(t)}},getSupplierById:async e=>{try{const t=await s.A.get(`/suppliers/${e}`);if(t.data&&"success"in t.data&&t.data.success)return l(t.data.data);{const e=t.data;return l(e)}}catch(t){throw(0,i.hS)(t)}},createSupplier:async e=>{try{const t={email:e.email,password:e.password,contactPerson:e.contactPerson||e.supplierName||"",name:e.name||e.supplierName,phone:e.phone,address:e.address,categories:e.categories||e.businessType,image:e.image},a=await s.A.post("/suppliers",t);if(a.data&&"success"in a.data&&a.data.success)return l(a.data.data);{const e=a.data;return l(e)}}catch(t){throw(0,i.hS)(t)}},updateSupplier:async(e,t)=>{try{const a={};t.supplierName&&(a.name=t.supplierName),t.email&&(a.email=t.email),t.phone&&(a.phone=t.phone),t.address&&(a.address=t.address),t.businessType&&(a.categories=[t.businessType]),t.image&&(a.image=t.image);const r=await s.A.put(`/suppliers/${e}`,a);return n.lg.update(r,"supplier",e)}catch(a){throw(0,i.hS)(a)}},deleteSupplier:async e=>{try{const t=await s.A.delete(`/suppliers/${e}`);return n.lg.delete(t,"supplier",e)}catch(t){throw(0,i.hS)(t)}},updateVerificationStatus:async(e,t)=>{try{const a=await s.A.put(`/suppliers/${e}/verification-status`,{verificationStatus:t});if(a.data&&"success"in a.data&&a.data.success)return l(a.data.data);{const e=a.data;return l(e)}}catch(a){throw(0,i.hS)(a)}},getSuppliersByVerificationStatus:async e=>{try{const t=await s.A.get("/suppliers",{params:{verificationStatus:e}});if(!t.data)throw new Error(`No suppliers found with status: ${e}`);return t.data}catch(t){throw(0,i.hS)(t)}},getSupplierProducts:async(e,t)=>{try{const a=await s.A.get(`/suppliers/${e}/products`,{params:t});if(a.data&&"success"in a.data&&a.data.success){const e=a.data;if(e.data&&"products"in e.data){return e.data.products.map(c)}if(Array.isArray(e.data)){return e.data.map(c)}return[]}return(Array.isArray(a.data)?a.data:[]).map(c)}catch(a){throw(0,i.hS)(a)}},getProductById:async e=>{try{const t=await s.A.get(`/products/${e}`);if(!t.data)throw new Error(`No product data received for ID: ${e}`);return t.data}catch(t){throw(0,i.hS)(t)}},updateProduct:async(e,t)=>{try{const a=await s.A.put(`/products/${e}`,t);if(!a.data)throw new Error(`Failed to update product ${e}`);return a.data}catch(a){throw(0,i.hS)(a)}},uploadProductImages:async(e,t)=>{try{const a=new FormData;t.forEach(((e,t)=>{a.append(`images[${t}]`,e)}));const r=await s.A.post(`/products/${e}/upload-images`,a,{headers:{"Content-Type":"multipart/form-data"}});if(!r.data)throw new Error("Failed to upload product images");return r.data}catch(a){throw(0,i.hS)(a)}},uploadSupplierImage:async(e,t)=>{try{const a=new FormData;a.append("image",t);const r=await s.A.post(`/suppliers/${e}/upload-image`,a,{headers:{"Content-Type":"multipart/form-data"}});if(!r.data)throw new Error("Failed to upload supplier image");return r.data}catch(a){throw(0,i.hS)(a)}},banSupplier:async e=>{try{const t=await s.A.put(`/suppliers/${e}/ban`,{status:"banned"});if(t.data&&"success"in t.data&&t.data.success)return l(t.data.data);{const e=t.data;return l(e)}}catch(t){throw(0,i.hS)(t)}},unbanSupplier:async e=>{try{const t=await s.A.put(`/suppliers/${e}/unban`);if(t.data&&"success"in t.data&&t.data.success)return l(t.data.data);{const e=t.data;return l(e)}}catch(t){throw(0,i.hS)(t)}}};var u=a(9705);const d=()=>{const[e,t]=(0,r.useState)([]),[a,s]=(0,r.useState)(!1),[i,n]=(0,r.useState)(null),{showNotification:l}=(0,u.A)(),c=(0,r.useRef)(l),d=(0,r.useRef)(!1);(0,r.useEffect)((()=>{c.current=l}));const p=(0,r.useCallback)((async()=>{s(!0),n(null);try{const e=await o.getSuppliers();t(e)}catch(e){n(e),c.current({type:"error",title:"Error",message:"Failed to fetch suppliers"})}finally{s(!1)}}),[]),y=(0,r.useCallback)((async function(e){let a=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];s(!0),n(null);try{const r=await o.createSupplier(e);return t((e=>[...e,r])),a&&c.current({type:"success",title:"Success",message:"Supplier created successfully"}),r}catch(r){throw n(r),a&&c.current({type:"error",title:"Error",message:"Failed to create supplier"}),r}finally{s(!1)}}),[]),m=(0,r.useCallback)((async(e,a)=>{s(!0),n(null);try{const r=await o.updateSupplier(e,a);return t((t=>t.map((t=>t.id===e?r:t)))),c.current({type:"success",title:"Success",message:"Supplier updated successfully"}),r}catch(r){throw n(r),c.current({type:"error",title:"Error",message:"Failed to update supplier"}),r}finally{s(!1)}}),[]),h=(0,r.useCallback)((async e=>{s(!0),n(null);try{await o.deleteSupplier(e),t((t=>t.filter((t=>t.id!==e))))}catch(a){throw n(a),c.current({type:"error",title:"Error",message:"Failed to delete supplier"}),a}finally{s(!1)}}),[]),g=(0,r.useCallback)((async e=>{s(!0),n(null);try{return await o.getSupplierById(e)}catch(t){throw n(t),c.current({type:"error",title:"Error",message:"Failed to fetch supplier details"}),t}finally{s(!1)}}),[]),f=(0,r.useCallback)((async(e,a)=>{s(!0),n(null);try{const r=await o.updateVerificationStatus(e,a);return t((t=>t.map((t=>t.id===e?r:t)))),c.current({type:"success",title:"Success",message:`Supplier ${"verified"===a?"verified":"set to pending"} successfully`}),r}catch(r){throw n(r),c.current({type:"error",title:"Error",message:"Failed to update supplier verification status"}),r}finally{s(!1)}}),[]);(0,r.useEffect)((()=>{d.current||(d.current=!0,p())}),[]);const w=(0,r.useCallback)((async e=>{s(!0),n(null);try{return await o.getSupplierProducts(e)}catch(t){throw n(t),c.current({type:"error",title:"Error",message:"Failed to fetch supplier products"}),t}finally{s(!1)}}),[]),b=(0,r.useCallback)((async e=>{s(!0),n(null);try{return await o.getProductById(e)}catch(t){throw n(t),c.current({type:"error",title:"Error",message:"Failed to fetch product details"}),t}finally{s(!1)}}),[]),S=(0,r.useCallback)((async(e,t)=>{s(!0),n(null);try{const a=await o.updateProduct(e,t);return c.current({type:"success",title:"Success",message:"Product updated successfully"}),a}catch(a){throw n(a),c.current({type:"error",title:"Error",message:"Failed to update product"}),a}finally{s(!1)}}),[]),x=(0,r.useCallback)((async(e,t)=>{s(!0),n(null);try{const a=await o.uploadProductImages(e,t);return c.current({type:"success",title:"Success",message:"Product images uploaded successfully"}),a}catch(a){throw n(a),c.current({type:"error",title:"Error",message:"Failed to upload product images"}),a}finally{s(!1)}}),[]),v=(0,r.useCallback)((async(e,t)=>{s(!0),n(null);try{const a=await o.uploadSupplierImage(e,t);return c.current({type:"success",title:"Success",message:"Supplier image uploaded successfully"}),a}catch(a){throw n(a),c.current({type:"error",title:"Error",message:"Failed to upload supplier image"}),a}finally{s(!1)}}),[]),k=(0,r.useCallback)((async e=>{s(!0),n(null);try{const a=await o.banSupplier(e);return t((t=>t.map((t=>t.id===e?a:t)))),a}catch(a){throw n(a),c.current({type:"error",title:"Error",message:"Failed to ban supplier"}),a}finally{s(!1)}}),[]),C=(0,r.useCallback)((async e=>{s(!0),n(null);try{const a=await o.unbanSupplier(e);return t((t=>t.map((t=>t.id===e?a:t)))),a}catch(a){throw n(a),c.current({type:"error",title:"Error",message:"Failed to unban supplier"}),a}finally{s(!1)}}),[]);return{suppliers:e,isLoading:a,error:i,fetchSuppliers:p,getSupplierById:g,createSupplier:y,createEntity:y,updateSupplier:m,deleteSupplier:h,deleteEntity:h,updateVerificationStatus:f,getSupplierProducts:w,uploadSupplierImage:v,banSupplier:k,unbanSupplier:C,getProductById:b,updateProduct:S,uploadProductImages:x}}}}]);
//# sourceMappingURL=412.316153b9.chunk.js.map