{"version": 3, "file": "static/js/932.a1eab1bc.chunk.js", "mappings": "sJAQA,MAWA,EAX8CA,IAGvC,IAHwC,SAC7CC,EAAQ,UACRC,EAAY,IACbF,EACC,OACEG,EAAAA,EAAAA,KAAA,MAAID,UAAW,kCAAkCA,IAAYD,SAC1DA,GACE,C,uDCCT,MAmCA,EAnCkCD,IAK3B,IAL4B,KACjCI,EAAI,UACJC,EAAS,SACTC,EAAQ,UACRJ,EAAY,IACbF,EACC,OACEG,EAAAA,EAAAA,KAAA,OAAKD,UAAW,4BAA4BA,IAAYD,UACtDE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wBAAuBD,SACnCG,EAAKG,KAAKC,IACT,MAAMC,EAAWD,EAAIE,KAAOL,EAE5B,OACEF,EAAAA,EAAAA,KAAA,UAEEQ,QAASA,KAAOH,EAAII,UAAYN,EAASE,EAAIE,IAC7CR,UAAW,iNAGPO,EACE,8BACA,iGACFD,EAAII,SAAW,gCAAkC,mCAErDA,SAAUJ,EAAII,SAASX,SAEtBO,EAAIK,OAZAL,EAAIE,GAaF,OAIX,C,uFCtBV,MAAMI,EAAwCd,IAOvC,IAPwC,MAC7Ce,EAAK,YACLC,EAAW,QACXC,EAAO,YACPC,EAAW,UACXhB,EAAY,GAAE,OACdiB,GACDnB,EACC,OACEoB,EAAAA,EAAAA,MAAA,OACElB,UAAW,QAAQA,IACnB,cAAaiB,EAAOlB,SAAA,CAGnBiB,GAAeA,EAAYG,OAAS,IACnClB,EAAAA,EAAAA,KAAA,OAAKD,UAAU,YAAY,aAAW,aAAYD,UAChDmB,EAAAA,EAAAA,MAAA,MAAIlB,UAAU,oDAAmDD,SAAA,EAC/DE,EAAAA,EAAAA,KAAA,MAAAF,UACEE,EAAAA,EAAAA,KAACmB,EAAAA,GAAI,CACHC,GAAG,IACHrB,UAAU,uCACV,aAAW,OAAMD,UAEjBE,EAAAA,EAAAA,KAACqB,EAAAA,EAAQ,CAACtB,UAAU,gBAIvBgB,EAAYX,KAAI,CAACkB,EAAMC,KACtBN,EAAAA,EAAAA,MAAA,MAAgBlB,UAAU,oBAAmBD,SAAA,EAC3CE,EAAAA,EAAAA,KAACwB,EAAAA,EAAgB,CAACzB,UAAU,+BAC3BuB,EAAKG,MAAQF,EAAQR,EAAYG,OAAS,GACzClB,EAAAA,EAAAA,KAACmB,EAAAA,GAAI,CACHC,GAAIE,EAAKG,KACT1B,UAAU,qBAAoBD,SAE7BwB,EAAKZ,SAGRV,EAAAA,EAAAA,KAAA,QAAMD,UAAU,4BAA2BD,SAAEwB,EAAKZ,UAV7Ca,WAmBjBN,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,8EAA6ED,SAAA,EAC1FmB,EAAAA,EAAAA,MAAA,OAAAnB,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAID,UAAU,mCAAkCD,SAAEc,IACjDC,GAAsC,kBAAhBA,GACrBb,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BD,SAAEe,IAE3CA,KAIHC,IACCd,EAAAA,EAAAA,KAAA,OAAKD,UAAU,oCAAmCD,SAC/CgB,SAIH,EAIV,GAAeY,EAAAA,EAAAA,MAAKf,E,gDC3FpB,SAASgB,EAAU9B,EAIhB+B,GAAQ,IAJS,MAClBhB,EAAK,QACLiB,KACGC,GACJjC,EACC,OAAoBkC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQlB,EAAqBmB,EAAAA,cAAoB,QAAS,CAC3DxB,GAAIsB,GACHjB,GAAS,KAAmBmB,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,wKAEP,CACA,MACA,EADiCX,EAAAA,WAAiBJ,E,6ICvBlD,SAASgB,EAAY9C,EAIlB+B,GAAQ,IAJW,MACpBhB,EAAK,QACLiB,KACGC,GACJjC,EACC,OAAoBkC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQlB,EAAqBmB,EAAAA,cAAoB,QAAS,CAC3DxB,GAAIsB,GACHjB,GAAS,KAAmBmB,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,uGAEP,CACA,MACA,EADiCX,EAAAA,WAAiBY,G,iHCvBlD,SAASC,EAAU/C,EAIhB+B,GAAQ,IAJS,MAClBhB,EAAK,QACLiB,KACGC,GACJjC,EACC,OAAoBkC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQlB,EAAqBmB,EAAAA,cAAoB,QAAS,CAC3DxB,GAAIsB,GACHjB,GAAS,KAAmBmB,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,0CACYX,EAAAA,cAAoB,OAAQ,CAC3CS,cAAe,QACfC,eAAgB,QAChBC,EAAG,mFAEP,CACA,MACA,EADiCX,EAAAA,WAAiBa,GC3BlD,SAASC,EAAYhD,EAIlB+B,GAAQ,IAJW,MACpBhB,EAAK,QACLiB,KACGC,GACJjC,EACC,OAAoBkC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQlB,EAAqBmB,EAAAA,cAAoB,QAAS,CAC3DxB,GAAIsB,GACHjB,GAAS,KAAmBmB,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,geAEP,CACA,MACA,EADiCX,EAAAA,WAAiBc,G,aCKlD,MAsMA,EAtMkEhD,IAAmB,IAAlB,SAAEiD,GAAUjD,EA2B7E,OACEoB,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,YAAWD,SAAA,EAExBE,EAAAA,EAAAA,KAAC+C,EAAAA,EAAa,CACZnC,MAAM,oBACNC,YAAY,qDAAoDf,UAEhEE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,YAAWD,UACxBE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,oCAAmCD,UAChDmB,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,8BAA6BD,SAAA,EAC1CE,EAAAA,EAAAA,KAACgD,EAAAA,EAAM,IACAF,EAASG,MAAQ,CAAEC,IAAKJ,EAASG,MACtCE,IAAKL,EAASM,KACdA,KAAMN,EAASM,KACfC,KAAK,QAEPpC,EAAAA,EAAAA,MAAA,OAAAnB,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAID,UAAU,oCAAmCD,SAAEgD,EAASM,QAC5DnC,EAAAA,EAAAA,MAAA,KAAGlB,UAAU,wBAAuBD,SAAA,CAAC,OAAKgD,EAASvC,OACnDU,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,mCAAkCD,SAAA,EAC/CE,EAAAA,EAAAA,KAACsD,EAAAA,EAAW,CAACC,OAAQT,EAASS,OAAQC,KAAK,cAC3CxD,EAAAA,EAAAA,KAAA,QAAMD,UAAW,4EACiB,aAAhC+C,EAASW,mBACL,8BACgC,YAAhCX,EAASW,mBACP,gCACA,2BACL3D,SACAgD,EAASW,mBACRX,EAASW,mBAAmBC,OAAO,GAAGC,cAAgBb,EAASW,mBAAmBG,MAAM,GACxF,4BAWhB5D,EAAAA,EAAAA,KAAC+C,EAAAA,EAAa,CACZnC,MAAM,sBACNC,YAAY,wDAAuDf,UAEnEmB,EAAAA,EAAAA,MAAC4C,EAAAA,EAAU,CAAA/D,SAAA,EACTE,EAAAA,EAAAA,KAAC8D,EAAAA,EAAU,CACTpD,MAAM,iBACNqD,OACE9C,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAACgE,EAAAA,EAAQ,CAACjE,UAAU,+BACnB+C,EAASmB,oBAIhBjE,EAAAA,EAAAA,KAAC8D,EAAAA,EAAU,CACTpD,MAAM,gBACNqD,OACE9C,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAACkE,EAAAA,EAAY,CAACnE,UAAU,gCACxBC,EAAAA,EAAAA,KAAA,KACEmE,KAAM,UAAUrB,EAASsB,QACzBrE,UAAU,uCAAsCD,SAE/CgD,EAASsB,cAKlBpE,EAAAA,EAAAA,KAAC8D,EAAAA,EAAU,CACTpD,MAAM,eACNqD,OACE9C,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAACqE,EAAAA,EAAS,CAACtE,UAAU,gCACrBC,EAAAA,EAAAA,KAAA,KACEmE,KAAM,OAAOrB,EAASwB,QACtBvE,UAAU,uCAAsCD,SAE/CgD,EAASwB,cAKlBtE,EAAAA,EAAAA,KAAC8D,EAAAA,EAAU,CACTpD,MAAM,UACNqD,OACE9C,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,mBAAkBD,SAAA,EAC/BE,EAAAA,EAAAA,KAAC4C,EAAU,CAAC7C,UAAU,uCACtBC,EAAAA,EAAAA,KAAA,QAAAF,SAAOgD,EAASyB,eAIrBzB,EAAS0B,UACRxE,EAAAA,EAAAA,KAAC8D,EAAAA,EAAU,CACTpD,MAAM,UACNqD,OACE9C,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAAC6C,EAAY,CAAC9C,UAAU,gCACxBC,EAAAA,EAAAA,KAAA,KACEmE,KAAMrB,EAAS0B,QACfC,OAAO,SACPC,IAAI,sBACJ3E,UAAU,uCAAsCD,SAE/CgD,EAAS0B,qBAUxBxE,EAAAA,EAAAA,KAAC+C,EAAAA,EAAa,CACZnC,MAAM,uBACNC,YAAY,8CAA6Cf,UAEzDmB,EAAAA,EAAAA,MAAC4C,EAAAA,EAAU,CAAA/D,SAAA,EAETE,EAAAA,EAAAA,KAAC8D,EAAAA,EAAU,CACTpD,MAAM,sBACNqD,OACE/D,EAAAA,EAAAA,KAAA,OAAKD,UAAU,uBAAsBD,SAClCgD,EAAS6B,YAAc7B,EAAS6B,WAAWzD,OAAS,EACnD4B,EAAS6B,WAAWvE,KAAI,CAACwE,EAAUrD,KACjCvB,EAAAA,EAAAA,KAAA,QAEED,UAAU,oGAAmGD,SAE5G8E,GAHIrD,MAOTvB,EAAAA,EAAAA,KAAA,QAAMD,UAAU,wBAAuBD,SAAC,gCAKhDE,EAAAA,EAAAA,KAAC8D,EAAAA,EAAU,CACTpD,MAAM,iBACNqD,OAAO/D,EAAAA,EAAAA,KAACsD,EAAAA,EAAW,CAACC,OAAQT,EAASS,OAAQC,KAAK,qBAMxDxD,EAAAA,EAAAA,KAAC+C,EAAAA,EAAa,CACZnC,MAAM,sBACNC,YAAY,0CAAyCf,UAErDE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,YAAWD,UACxBmB,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,8BAA6BD,SAAA,CAlLvByD,KAC3B,OAAQA,GACN,IAAK,WACH,OAAOvD,EAAAA,EAAAA,KAAC6E,EAAAA,EAAe,CAAC9E,UAAU,2BACpC,IAAK,UACH,OAAOC,EAAAA,EAAAA,KAAC8E,EAAAA,EAAS,CAAC/E,UAAU,4BAC9B,IAAK,WACH,OAAOC,EAAAA,EAAAA,KAAC+E,EAAAA,EAAW,CAAChF,UAAU,yBAChC,QACE,OAAOC,EAAAA,EAAAA,KAAC8E,EAAAA,EAAS,CAAC/E,UAAU,0BAChC,EAyKSiF,CAAoBlC,EAASW,oBAAsB,YACpDxC,EAAAA,EAAAA,MAAA,OAAAnB,SAAA,EACEE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,oCAAmCD,SACf,aAAhCgD,EAASW,mBAAoC,oBACb,YAAhCX,EAASW,mBAAmC,uBAC5C,2BAEHzD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wBAAuBD,SA7KrByD,KAC3B,OAAQA,GACN,IAAK,WACH,MAAO,WACT,IAAK,UACH,MAAO,uBACT,IAAK,WACH,MAAO,WACT,QACE,MAAO,iBACX,EAoKa0B,CAAoBnC,EAASW,oBAAsB,yBAM1D,E,yEC9LV,MAyPA,EAzP0D5D,IAInD,IAJoD,SACzDqF,EACAC,WAAYC,EAAW,gBACvBC,GACDxF,EACC,MAAMyF,GAAWC,EAAAA,EAAAA,OACX,YAAEC,EAAW,UAAEC,EAAS,SAAEC,IAAaC,EAAAA,EAAAA,MACtCC,EAAiBC,IAAsBC,EAAAA,EAAAA,UAAiC,OACxEC,EAAmBC,IAAwBF,EAAAA,EAAAA,WAAS,GAErDG,EAAwB1C,IAC5B,OAAQA,GACN,IAAK,SACH,MAAO,SACT,IAAK,WAIL,QACE,MAAO,UAHT,IAAK,eACH,MAAO,WAGX,EAaI2C,EAAqBC,IACzBb,EAASc,EAAAA,EAAOC,uBAAuBF,EAAQ5F,IAAI,EA4B/C+F,EAAqC,CACzC,CACEC,IAAK,OACL7F,MAAO,eACP8F,UAAU,EACVC,OAAQA,CAACC,EAAgBP,KACvBlF,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,oBAAmBD,SAAA,CAC/BqG,EAAQQ,OACP3G,EAAAA,EAAAA,KAAA,OACEkD,IAAKiD,EAAQQ,MACbxD,IAAKgD,EAAQ/C,KACbrD,UAAU,4CAGZC,EAAAA,EAAAA,KAAA,OAAKD,UAAU,yEAAwED,UACrFE,EAAAA,EAAAA,KAAC4G,EAAAA,EAAQ,CAAC7G,UAAU,6BAGxBkB,EAAAA,EAAAA,MAAA,OAAAnB,SAAA,EACEE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,4BAA2BD,SAAEqG,EAAQ/C,QACpDnC,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,wBAAuBD,SAAA,CAAC,OAAKqG,EAAQ5F,aAK5D,CACEgG,IAAK,MACL7F,MAAO,MACP8F,UAAU,EACVC,OAAS1C,IACP/D,EAAAA,EAAAA,KAAA,QAAMD,UAAU,kCAAiCD,SAAEiE,KAGvD,CACEwC,IAAK,WACL7F,MAAO,WACP8F,UAAU,EACVC,OAAS1C,IACP/D,EAAAA,EAAAA,KAAA,QAAMD,UAAU,oGAAmGD,SAChHiE,KAIP,CACEwC,IAAK,QACL7F,MAAO,QACP8F,UAAU,EACVC,OAAS1C,IACP/D,EAAAA,EAAAA,KAAA,QAAMD,UAAU,4BAA2BD,UAAE+G,EAAAA,EAAAA,IAAe9C,MAGhE,CACEwC,IAAK,QACL7F,MAAO,QACP8F,UAAU,EACVC,OAAQA,CAAC1C,EAAeoC,KACtB,MAAMW,GA/FYC,EA+FiBhD,EA/FFiD,EA+FSb,EAAQa,cAAgB,GA9FvD,iBA8F2Db,EAAQ5C,QA9FvC,IAAVwD,EACxB,CAAEE,KAAM,eAAgBC,MAAO,gBAC7BH,GAASC,EACX,CAAEC,KAAM,YAAaC,MAAO,mBAE5B,CAAED,KAAM,WAAYC,MAAO,mBANfC,IAACJ,EAAeC,EAgGjC,OACE/F,EAAAA,EAAAA,MAAA,OAAAnB,SAAA,EACEE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,4BAA2BD,SAAEiE,KAC5C/D,EAAAA,EAAAA,KAAA,OAAKD,UAAW,WAAW+G,EAAYI,QAAQpH,SAAEgH,EAAYG,SACzD,GAIZ,CACEV,IAAK,SACL7F,MAAO,SACP8F,UAAU,EACVC,OAAS1C,IACP/D,EAAAA,EAAAA,KAACsD,EAAAA,EAAW,CAACC,OAAQ0C,EAAqBlC,GAAQP,KAAK,cAG3D,CACE+C,IAAK,UACL7F,MAAO,UACP8F,UAAU,EACVC,OAAQA,CAACC,EAAaP,KACpBlF,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,8BAA6BD,SAAA,EAC1CE,EAAAA,EAAAA,KAACoH,EAAAA,EAAM,CACLC,QAAQ,UACRhE,KAAK,KACL7C,QAASA,IAAM0F,EAAkBC,GACjCmB,MAAMtH,EAAAA,EAAAA,KAACuH,EAAAA,EAAO,CAACxH,UAAU,YACzBa,MAAM,uBAAsBd,SAC7B,UAGDE,EAAAA,EAAAA,KAACoH,EAAAA,EAAM,CACLC,QAAQ,UACRhE,KAAK,KACL7C,QAASA,IApHQ2F,KAEzBT,EAAS,iBAAiBS,EAAQ/C,QAClCoE,QAAQC,IAAI,gBAAiBtB,EAAQ,EAiHduB,CAAkBvB,GACjCmB,MAAMtH,EAAAA,EAAAA,KAAC2B,EAAAA,EAAU,CAAC5B,UAAU,YAC5Ba,MAAM,eAAcd,SACrB,UAGDE,EAAAA,EAAAA,KAACoH,EAAAA,EAAM,CACLC,QAAQ,SACRhE,KAAK,KACL7C,QAASA,IAvHU2F,KAC3BN,EAAmBM,GACnBH,GAAqB,EAAK,EAqHH2B,CAAoBxB,GACnCmB,MAAMtH,EAAAA,EAAAA,KAAC4H,EAAAA,EAAS,CAAC7H,UAAU,YAC3Ba,MAAM,iBAAgBd,SACvB,gBAQT,OAAwB,IAApBoF,EAAShE,QAETlB,EAAAA,EAAAA,KAAC+C,EAAAA,EAAa,CACZnC,MAAM,WACNC,YAAY,oCAAmCf,UAE/CmB,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,wBAAuBD,SAAA,EACpCE,EAAAA,EAAAA,KAAC4G,EAAAA,EAAQ,CAAC7G,UAAU,qCACpBC,EAAAA,EAAAA,KAAA,MAAID,UAAU,yCAAwCD,SAAC,uBACvDE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BD,SAAC,mDAG1CE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,OAAMD,UACnBE,EAAAA,EAAAA,KAACoH,EAAAA,EAAM,CACLC,QAAQ,UACRC,MAAMtH,EAAAA,EAAAA,KAAC6H,EAAAA,EAAQ,CAAC9H,UAAU,YAC1BS,QAASA,IAAMkF,EAAS,yCAAyC5F,SAClE,wBAUTmB,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,YAAWD,SAAA,EACxBE,EAAAA,EAAAA,KAAC+C,EAAAA,EAAa,CACZnC,MAAM,WACNC,YAAa,GAAGqE,EAAShE,2CAA2CpB,UAEpEE,EAAAA,EAAAA,KAAC8H,EAAAA,EAAS,CACRxB,QAASA,EACTyB,KAAM7C,EACN8C,WAAY9B,EACZ+B,YAAY,EACZC,SAAU,GACVC,aAAa,oBACbpI,UAAU,eAKb6F,IACC5F,EAAAA,EAAAA,KAACoI,EAAAA,EAAK,CACJC,OAAQtC,EACRuC,QAASA,IAAMtC,GAAqB,GACpCpF,MAAM,iBACNyC,KAAK,KACLkF,QACEtH,EAAAA,EAAAA,MAAAuH,EAAAA,SAAA,CAAA1I,SAAA,EACEE,EAAAA,EAAAA,KAACoH,EAAAA,EAAM,CACLC,QAAQ,UACR7G,QAASA,IAAMwF,GAAqB,GAAOlG,SAC5C,YAGDE,EAAAA,EAAAA,KAACoH,EAAAA,EAAM,CACLC,QAAQ,SACR7G,QAzLeiI,UAC3B,GAAK7C,EAEL,IAEEJ,EAAY,YAAYI,EAAgBxC,8BACxC4C,GAAqB,GACrBH,EAAmB,MACJ,OAAfR,QAAe,IAAfA,GAAAA,GACF,CAAE,MAAOqD,GACPjD,EAAU,2BACZ,GA8K0C3F,SAC/B,sBAIJA,UAEDmB,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,wBAAuBD,SAAA,CAAC,oCACH8F,EAAgBxC,KAAK,0CAIzD,E,cCrPV,MAwZA,EAxZsCuF,KACpC,MAAQpI,GAAI4E,IAAeyD,EAAAA,EAAAA,KACrBtD,GAAWC,EAAAA,EAAAA,OACX,UAAEE,EAAS,YAAED,IAAgBG,EAAAA,EAAAA,KAG7BkD,GAAeC,EAAAA,EAAAA,QAAOrD,GACtBsD,GAAiBD,EAAAA,EAAAA,QAAOtD,GACxBwD,GAAeF,EAAAA,EAAAA,SAAO,IAG5BG,EAAAA,EAAAA,YAAU,KACRJ,EAAaK,QAAUzD,EACvBsD,EAAeG,QAAU1D,CAAW,GACnC,CAACC,EAAWD,KAGfyD,EAAAA,EAAAA,YAAU,IACD,KACLD,EAAaE,SAAU,CAAK,GAE7B,IAIH,MAAOhJ,EAAWiJ,IAAgBrD,EAAAA,EAAAA,UAAkC,aAC7DhD,EAAUsG,IAAetD,EAAAA,EAAAA,UAA0B,OACnDuD,EAAkBC,IAAuBxD,EAAAA,EAAAA,UAA4B,KAIrEyD,EAAWC,IAAgB1D,EAAAA,EAAAA,WAAS,IACpC4C,EAAOe,IAAY3D,EAAAA,EAAAA,UAAwB,OAC3CC,EAAmBC,IAAwBF,EAAAA,EAAAA,WAAS,IACpD4D,EAAYC,IAAiB7D,EAAAA,EAAAA,WAAS,IACtC8D,EAAgBC,IAAqB/D,EAAAA,EAAAA,WAAS,IAC9CgE,EAAWC,IAAgBjE,EAAAA,EAAAA,WAAS,IAMrC,gBACJkE,EAAe,oBACfC,EAAmB,eAInBC,EAAc,YACdC,EAAW,cACXC,EACAb,UAAWc,IACTC,EAAAA,EAAAA,KAKEC,GAAoBC,EAAAA,EAAAA,cAAY/B,UACpC,IAAKtD,EAGH,OAFAsE,EAAS,gCACTD,GAAa,GAIf,IACEA,GAAa,GACbC,EAAS,MAGT,MAAMgB,QAAqBT,EAAgB7E,GAE3C,IAAK6D,EAAaE,QAAS,OAC3BE,EAAYqB,GAGZ,MAAMvF,QAAiB+E,EAAoB9E,GAE3C,IAAK6D,EAAaE,QAAS,OAC3BI,EAAoBpE,EAmBtB,CAAE,MAAOwD,GACP,IAAKM,EAAaE,QAAS,OAE3B1B,QAAQkB,MAAM,gCAAiCA,GAC/C,MAAMgC,EAAehC,aAAiBiC,MAAQjC,EAAMkC,QAAU,gCAC9DnB,EAASiB,GACT7B,EAAaK,QAAQ,+BACvB,CAAC,QACKF,EAAaE,SACfM,GAAa,EAEjB,IACC,CAACrE,EAAY6E,EAAiBC,KAGjChB,EAAAA,EAAAA,YAAU,KACRsB,GAAmB,GAClB,CAACA,IAgGJ,OAAIhB,GAAac,GAEbrK,EAAAA,EAAAA,KAAA,OAAKD,UAAU,gDAA+CD,UAC5DE,EAAAA,EAAAA,KAAC6K,EAAAA,EAAc,CAACxH,KAAK,SAMvBqF,IAAU5F,GAEV7B,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,YAAWD,SAAA,EACxBE,EAAAA,EAAAA,KAACW,EAAAA,EAAU,CACTC,MAAM,mBACNC,YAAY,qBACZE,YAAa,CACX,CAAEL,MAAO,YAAae,KAAM2E,EAAAA,EAAO0E,WACnC,CAAEpK,MAAO,eAGbV,EAAAA,EAAAA,KAAA,OAAKD,UAAU,oBAAmBD,UAChCE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,gBAAeD,SAAE4I,GAAS,6BAO7CzH,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,YAAWD,SAAA,EACxBE,EAAAA,EAAAA,KAACW,EAAAA,EAAU,CACTC,MAAO,aAAakC,EAASM,OAC7BvC,YAAY,gDACZE,YAAa,CACX,CAAEL,MAAO,YAAae,KAAM2E,EAAAA,EAAO0E,WACnC,CAAEpK,MAAOoC,EAASM,OAEpBtC,SACEG,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,aAAYD,SAAA,CACJ,WAApBgD,EAASS,QACRvD,EAAAA,EAAAA,KAACoH,EAAAA,EAAM,CACLC,QAAQ,UACRhE,KAAK,KACL7C,QA7EciI,UAC1B,GAAK3F,EAEL,IACEiH,GAAa,SAEPK,EAActH,EAASvC,IAE7BwI,EAAeG,QAAQ,aAAapG,EAASM,8CAGvCmH,GAER,CAAE,MAAO7B,GACPlB,QAAQkB,MAAM,4BAA6BA,GAC3CG,EAAaK,QAAQ,2BACvB,CAAC,QACCa,GAAa,EACf,GA4DYzC,MAAMtH,EAAAA,EAAAA,KAAC6E,EAAAA,EAAe,CAAC9E,UAAU,YACjCU,SAAU8I,GAAaO,GAAaJ,EACpCqB,QAASjB,EAAUhK,SACpB,oBAIDE,EAAAA,EAAAA,KAACoH,EAAAA,EAAM,CACLC,QAAQ,YACRhE,KAAK,KACL7C,QAASA,IAAMqJ,GAAkB,GACjCvC,MAAMtH,EAAAA,EAAAA,KAAC2C,EAAY,CAAC5C,UAAU,YAC9BU,SAAU8I,GAAaO,GAAaJ,EAAW5J,SAChD,kBAIHE,EAAAA,EAAAA,KAACoH,EAAAA,EAAM,CACLC,QAAQ,SACRhE,KAAK,KACL7C,QAASA,IAAMwF,GAAqB,GACpCsB,MAAMtH,EAAAA,EAAAA,KAAC4H,EAAAA,EAAS,CAAC7H,UAAU,YAC3BU,SAAU8I,GAAaG,GAAcI,EAAUhK,SAChD,0BAOPE,EAAAA,EAAAA,KAACgL,EAAAA,EAAI,CACH/K,KAAM,CACJ,CAAEM,GAAI,WAAYG,MAAO,wBAOzB,CAAEH,GAAI,WAAYG,MAAO,aAO3BR,UAAWA,EACXC,SAvGmB8K,IASvB9B,EAPmB8B,EAOK,IAiGP,aAAd/K,IACCF,EAAAA,EAAAA,KAACkL,EAAoB,CAACpI,SAAUA,IAKnB,aAAd5C,IACCF,EAAAA,EAAAA,KAACmL,EAAgB,CACfjG,SAAUmE,EACVlE,WAAYrC,EAASvC,GACrB8E,gBAAiBkF,KAOrBvK,EAAAA,EAAAA,KAACoI,EAAAA,EAAK,CACJC,OAAQuB,EACRtB,QAASA,IAAMuB,GAAkB,GACjCjJ,MAAM,eACNyC,KAAK,KACLkF,QACEtH,EAAAA,EAAAA,MAAAuH,EAAAA,SAAA,CAAA1I,SAAA,EACEE,EAAAA,EAAAA,KAACoH,EAAAA,EAAM,CACLC,QAAQ,UACR7G,QAASA,IAAMqJ,GAAkB,GACjCpJ,SAAUqJ,EAAUhK,SACrB,YAGDE,EAAAA,EAAAA,KAACoH,EAAAA,EAAM,CACLC,QAAQ,YACR7G,QAxLciI,UACxB,GAAK3F,EAEL,IACEiH,GAAa,SAEPI,EAAYrH,EAASvC,IAE3BwI,EAAeG,QAAQ,aAAapG,EAASM,sCAC7CyG,GAAkB,SAGZU,GAER,CAAE,MAAO7B,GACPlB,QAAQkB,MAAM,0BAA2BA,GACzCG,EAAaK,QAAQ,yBACvB,CAAC,QACCa,GAAa,EACf,GAsKUgB,QAASjB,EACTxC,MAAMtH,EAAAA,EAAAA,KAAC2C,EAAY,CAAC5C,UAAU,YAAaD,SAC5C,oBAIJA,UAEDmB,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,wBAAuBD,SAAA,EACpCmB,EAAAA,EAAAA,MAAA,KAAGlB,UAAU,OAAMD,SAAA,CAAC,iCACWmB,EAAAA,EAAAA,MAAA,UAAAnB,SAAA,CAAQ,IAAEgD,EAASM,KAAK,OAAU,QAEjEpD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,8BAA6BD,SAAC,uBAG3CmB,EAAAA,EAAAA,MAAA,MAAIlB,UAAU,6CAA4CD,SAAA,EACxDE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,8CACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,4CACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,2CACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,gDAENE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,qBAAoBD,SAAC,2GAOtCE,EAAAA,EAAAA,KAACoI,EAAAA,EAAK,CACJC,OAAQtC,EACRuC,QAASA,IAAMtC,GAAqB,GACpCpF,MAAM,kBACNyC,KAAK,KACLkF,QACEtH,EAAAA,EAAAA,MAAAuH,EAAAA,SAAA,CAAA1I,SAAA,EACEE,EAAAA,EAAAA,KAACoH,EAAAA,EAAM,CACLC,QAAQ,UACR7G,QAASA,IAAMwF,GAAqB,GACpCvF,SAAUiJ,EAAW5J,SACtB,YAGDE,EAAAA,EAAAA,KAACoH,EAAAA,EAAM,CACLC,QAAQ,SACR7G,QA5PiBiI,UAC3B,GAAK3F,EAEL,IACE6G,GAAc,SAERO,EAAepH,EAASvC,IAE9BwI,EAAeG,QAAQ,aAAapG,EAASM,uCAC7C4C,GAAqB,GAGrBV,EAASc,EAAAA,EAAO0E,UAElB,CAAE,MAAOpC,GACPlB,QAAQkB,MAAM,2BAA4BA,GAC1CG,EAAaK,QAAQ,4BACvB,CAAC,QACCS,GAAc,EAChB,GA0OUoB,QAASrB,EACTpC,MAAMtH,EAAAA,EAAAA,KAAC4H,EAAAA,EAAS,CAAC7H,UAAU,YAAaD,SACzC,uBAIJA,UAEDmB,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,wBAAuBD,SAAA,EACpCmB,EAAAA,EAAAA,MAAA,KAAGlB,UAAU,OAAMD,SAAA,CAAC,oCACcmB,EAAAA,EAAAA,MAAA,UAAAnB,SAAA,CAAQ,IAAEgD,EAASM,KAAK,OAAU,QAEpEpD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,2BAA0BD,SAAC,+DAGxCmB,EAAAA,EAAAA,MAAA,MAAIlB,UAAU,0CAAyCD,SAAA,EACrDE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,8BACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,uCACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,0CAIN,C,4CC5aH,MAAMsL,EAAa,SAACC,GAA0E,IAAtDC,EAAmCC,UAAArK,OAAA,QAAAsK,IAAAD,UAAA,GAAAA,UAAA,GAAG,CAAC,EACpF,IAAKF,EAAY,MAAO,IAExB,IACE,MAAMI,EAAO,IAAIC,KAAKL,GAGhBM,EAA6C,CACjDC,KAAM,UACNC,MAAO,QACPC,IAAK,aACFR,GAGL,OAAO,IAAIS,KAAKC,eAAe,QAASL,GAAgBM,OAAOR,EACjE,CAAE,MAAO/C,GAEP,OADAlB,QAAQkB,MAAM,yBAA0BA,GACjC2C,CACT,CACF,EAkBaxE,EAAiB,SAC5BqF,GAGY,IAFZC,EAAgBZ,UAAArK,OAAA,QAAAsK,IAAAD,UAAA,GAAAA,UAAA,GAAG,MACnBa,EAAcb,UAAArK,OAAA,QAAAsK,IAAAD,UAAA,GAAAA,UAAA,GAAG,QAEjB,IACE,OAAO,IAAIQ,KAAKM,aAAaD,EAAQ,CACnCE,MAAO,WACPH,WACAI,sBAAuB,EACvBC,sBAAuB,IACtBP,OAAOC,EACZ,CAAE,MAAOxD,GAEP,OADAlB,QAAQkB,MAAM,6BAA8BA,GACrC,GAAGyD,KAAYD,EAAOO,QAAQ,IACvC,CACF,C,gDC7DA,SAASzI,EAAQnE,EAId+B,GAAQ,IAJO,MAChBhB,EAAK,QACLiB,KACGC,GACJjC,EACC,OAAoBkC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQlB,EAAqBmB,EAAAA,cAAoB,QAAS,CAC3DxB,GAAIsB,GACHjB,GAAS,KAAmBmB,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,4JAEP,CACA,MACA,EADiCX,EAAAA,WAAiBiC,E,yGCPlD,MAiDA,EAjDgDnE,IAIzC,IAJ0C,OAC/C0D,EACAC,KAAMkJ,EAAQ,OAAM,UACpB3M,EAAY,IACbF,EAEC,IAAK0D,EACH,OACEvD,EAAAA,EAAAA,KAAA,QAAMD,UAAW,qGAAqGA,IAAYD,SAAC,YAMvI,MAAM6M,EAAYpJ,EAAOqJ,cACzB,IAAIC,EAAa,GACbvF,EAAO,KAGO,WAAdqF,GAAwC,aAAdA,GAA0C,cAAdA,GACxDE,EAAa,8BACbvF,GAAOtH,EAAAA,EAAAA,KAAC6E,EAAAA,EAAe,CAAC9E,UAAU,kBACX,YAAd4M,GAAyC,eAAdA,GACpCE,EAAa,4BACbvF,GAAOtH,EAAAA,EAAAA,KAAC8E,EAAAA,EAAS,CAAC/E,UAAU,kBACL,WAAd4M,GAAwC,aAAdA,GACnCE,EAAa,0BACbvF,GAAOtH,EAAAA,EAAAA,KAAC+E,EAAAA,EAAW,CAAChF,UAAU,kBACP,YAAd4M,GACTE,EAAa,gCACbvF,GAAOtH,EAAAA,EAAAA,KAAC8M,EAAAA,EAAS,CAAC/M,UAAU,kBACL,YAAd4M,GACTE,EAAa,gCACbvF,GAAOtH,EAAAA,EAAAA,KAAC+M,EAAAA,EAAqB,CAAChN,UAAU,kBAExC8M,EAAa,4BAIf,MAAMG,EAAkBzJ,EAASA,EAAOG,OAAO,GAAGC,cAAgBJ,EAAOK,MAAM,GAAK,UAEpF,OACE3C,EAAAA,EAAAA,MAAA,QAAMlB,UAAW,2EAA2E8M,KAAc9M,IAAYD,SAAA,CACnHwH,EACA0F,IACI,C,uDCrDX,MAaA,EAb8CnN,IAIvC,IAJwC,MAC7Ca,EAAK,MACLqD,EAAK,UACLhE,EAAY,IACbF,EACC,OACEoB,EAAAA,EAAAA,MAAA,OAAKlB,UAAW,wDAAwDA,IAAYD,SAAA,EAClFE,EAAAA,EAAAA,KAAA,MAAID,UAAU,oCAAmCD,SAAEY,KACnDV,EAAAA,EAAAA,KAAA,MAAID,UAAU,mDAAkDD,SAAEiE,MAC9D,C,gDCjBV,SAAS+I,EAASjN,EAIf+B,GAAQ,IAJQ,MACjBhB,EAAK,QACLiB,KACGC,GACJjC,EACC,OAAoBkC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQlB,EAAqBmB,EAAAA,cAAoB,QAAS,CAC3DxB,GAAIsB,GACHjB,GAAS,KAAmBmB,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,saAEP,CACA,MACA,EADiCX,EAAAA,WAAiB+K,E,gDCvBlD,SAAStL,EAAgB3B,EAItB+B,GAAQ,IAJe,MACxBhB,EAAK,QACLiB,KACGC,GACJjC,EACC,OAAoBkC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQlB,EAAqBmB,EAAAA,cAAoB,QAAS,CAC3DxB,GAAIsB,GACHjB,GAAS,KAAmBmB,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,8BAEP,CACA,MACA,EADiCX,EAAAA,WAAiBP,E,uDCdlD,MAqBA,EArBoD3B,IAK7C,IAL8C,MACnDe,EAAK,YACLC,EAAW,SACXf,EAAQ,UACRC,EAAY,IACbF,EACC,OACEoB,EAAAA,EAAAA,MAAA,OAAKlB,UAAW,iDAAiDA,IAAYD,SAAA,EAC3EmB,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAAA,MAAID,UAAU,8CAA6CD,SAAEc,IAC5DC,IACCb,EAAAA,EAAAA,KAAA,KAAGD,UAAU,uCAAsCD,SAAEe,QAGzDb,EAAAA,EAAAA,KAAA,OAAKD,UAAU,2BAA0BD,SACtCA,MAEC,C,gDC1BV,SAASmN,EAAmBpN,EAIzB+B,GAAQ,IAJkB,MAC3BhB,EAAK,QACLiB,KACGC,GACJjC,EACC,OAAoBkC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQlB,EAAqBmB,EAAAA,cAAoB,QAAS,CAC3DxB,GAAIsB,GACHjB,GAAS,KAAmBmB,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,kFAEP,CACA,MACA,EADiCX,EAAAA,WAAiBkL,GCvBlD,SAASC,EAAarN,EAInB+B,GAAQ,IAJY,MACrBhB,EAAK,QACLiB,KACGC,GACJjC,EACC,OAAoBkC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQlB,EAAqBmB,EAAAA,cAAoB,QAAS,CAC3DxB,GAAIsB,GACHjB,GAAS,KAAmBmB,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,+BAEP,CACA,MACA,EADiCX,EAAAA,WAAiBmL,G,2CCoBlD,SAASpF,EAASjI,GAqBK,IArB2B,QAChDyG,EAAO,KACPyB,EAAI,WACJC,EAAU,MACVpH,EAAK,YACLC,EAAW,QACXkK,GAAU,EAAK,WACf9C,GAAa,EAAI,SACjBC,EAAWiF,EAAAA,GAAOC,kBAAiB,WACnCC,GAAa,EAAI,kBACjBC,EAAiB,QACjBxM,EAAO,aACPqH,EAAe,mBAAkB,UACjCpI,EAAY,GAAE,gBACdwN,EAAkB,GAAE,cACpBC,EAAgB,GAAE,gBAClBC,EAAkB,GAAE,aACpBC,EAAY,eACZC,EAAc,qBACdC,EAAuB,MAAK,OAC5B5M,GACkBnB,EAElB,MAAOgO,EAAYC,IAAiBhI,EAAAA,EAAAA,UAG1B6H,EAAiB,CAAEpH,IAAKoH,EAAgBI,UAAWH,GAAyB,OAE/EI,EAAYC,IAAiBnI,EAAAA,EAAAA,UAAS,KACtCoI,EAAaC,IAAkBrI,EAAAA,EAAAA,UAAS,IACxCsI,EAAcC,IAAmBvI,EAAAA,EAAAA,UAAmB,KACpDwI,EAAYC,IAAiBzI,EAAAA,EAAAA,UAAwB,MAWtD0I,GAAaC,EAAAA,EAAAA,UAAQ,IACpBZ,EAEE,IAAI9F,GAAM2G,MAAK,CAACC,EAAGC,KACxB,MAAMC,EAASF,EAAEd,EAAWtH,KACtBuI,EAASF,EAAEf,EAAWtH,KAG5B,OAAc,MAAVsI,GAA4B,MAAVC,EAAuB,EAC/B,MAAVD,EAAgD,QAAzBhB,EAAWE,WAAuB,EAAI,EACnD,MAAVe,EAAgD,QAAzBjB,EAAWE,UAAsB,GAAK,EAG3C,kBAAXc,GAAyC,kBAAXC,EACP,QAAzBjB,EAAWE,UACdc,EAAOE,cAAcD,GACrBA,EAAOC,cAAcF,GAGvBA,EAASC,EACqB,QAAzBjB,EAAWE,WAAuB,EAAI,EAE3Cc,EAASC,EACqB,QAAzBjB,EAAWE,UAAsB,GAAK,EAExC,CAAC,IAxBchG,GA0BvB,CAACA,EAAM8F,IAGJmB,GAAeP,EAAAA,EAAAA,UAAQ,IACtBT,EAEEQ,EAAWS,QAAQC,GACxBlN,OAAOmN,QAAQD,GAAKE,MAAKC,IAAoB,IAAlBC,EAAMvL,GAAMsL,EAErC,OAAc,OAAVtL,QAA4ByH,IAAVzH,IACD,kBAAVA,GAEJwL,OAAOxL,GAAO6I,cAAc4C,SAASxB,EAAWpB,eAAc,MARjD4B,GAWvB,CAACA,EAAYR,IAGVyB,EAAaC,KAAKC,KAAKX,EAAa9N,OAASgH,GAC7C0H,GAAgBnB,EAAAA,EAAAA,UAAQ,KAC5B,MAAMoB,GAAc3B,EAAc,GAAKhG,EACvC,OAAO8G,EAAapL,MAAMiM,EAAYA,EAAa3H,EAAS,GAC3D,CAAC8G,EAAcd,EAAahG,IAEzB4H,EAAoBC,IACxB5B,EAAe4B,EAAK,EA0ChBC,EAAqBzM,IACzB,IAAI0M,EAAU,4BAEd,GAAsB,kBAAX1M,EAAqB,CAC9B,MAAM2M,EAAc3M,EAAOqJ,cAEvBsD,EAAYV,SAAS,WAAaU,EAAYV,SAAS,aACvDU,EAAYV,SAAS,aAAeU,EAAYV,SAAS,cACzDU,EAAYV,SAAS,WACvBS,EAAU,8BACDC,EAAYV,SAAS,YAAcU,EAAYV,SAAS,cACjES,EAAU,gCACDC,EAAYV,SAAS,aAAeU,EAAYV,SAAS,WAC1DU,EAAYV,SAAS,WAAaU,EAAYV,SAAS,SAC/DS,EAAU,0BACDC,EAAYV,SAAS,cAC9BS,EAAU,4BAEd,CAEA,OACEjQ,EAAAA,EAAAA,KAAA,QAAMD,UAAW,2EAA2EkQ,IAAUnQ,SACnGyD,GACI,EAIX,OACEtC,EAAAA,EAAAA,MAAA,OACElB,UAAW,oHAAoHA,IAC/H,cAAaiB,EAAOlB,SAAA,EAGlBc,GAASC,KACTI,EAAAA,EAAAA,MAAA,OAAKlB,UAAW,sCAAsCwN,IAAkBzN,SAAA,CACpD,kBAAVc,GACNZ,EAAAA,EAAAA,KAAA,MAAID,UAAU,sCAAqCD,SAAEc,IAErDA,EAEsB,kBAAhBC,GACNb,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BD,SAAEe,IAE3CA,MAMNI,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,kGAAiGD,SAAA,EAC9GmB,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,kBAAiBD,SAAA,EAC9BE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,uEAAsED,UACnFE,EAAAA,EAAAA,KAACiN,EAAmB,CAAClN,UAAU,6BAEjCC,EAAAA,EAAAA,KAAA,SACEwD,KAAK,OACL2M,YAAY,YACZpQ,UAAU,sMACVgE,MAAOiK,EACP7N,SAAWiQ,IACTnC,EAAcmC,EAAE3L,OAAOV,OACvBoK,EAAe,EAAE,EAEnB,cAAa,GAAGnN,iBAIpBC,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,8BAA6BD,SAAA,CACzCsO,EAAalN,OAAS,IACrBD,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,8BAA6BD,SAAA,EAC1CmB,EAAAA,EAAAA,MAAA,QAAMlB,UAAU,wBAAuBD,SAAA,CAAEsO,EAAalN,OAAO,gBAC7DlB,EAAAA,EAAAA,KAAA,UACED,UAAU,uGACVS,QAASA,KACP6N,EAAgB,IACZf,GAAmBA,EAAkB,GAAG,EAE9C,cAAa,GAAGtM,oBAAyBlB,SAC1C,aAKJgB,SAKLd,EAAAA,EAAAA,KAAA,OAAKD,UAAW,mBAAmByN,IAAgB1N,SAChDiL,GACC/K,EAAAA,EAAAA,KAAA,OAAKD,UAAU,yCAAwCD,UACrDE,EAAAA,EAAAA,KAAC6K,EAAAA,EAAc,CAACxH,KAAK,KAAKgE,QAAQ,eAGpCpG,EAAAA,EAAAA,MAAA,SAAOlB,UAAU,sCAAqCD,SAAA,EACpDE,EAAAA,EAAAA,KAAA,SAAOD,UAAU,aAAYD,UAC3BmB,EAAAA,EAAAA,MAAA,MAAAnB,SAAA,CACGuN,IACCrN,EAAAA,EAAAA,KAAA,MAAID,UAAU,iBAAgBD,UAC5BE,EAAAA,EAAAA,KAAA,SACEwD,KAAK,WACLzD,UAAU,kEACVI,SAtHKkQ,IACvB,MAAMC,EAAkBD,EAAM5L,OAAO8L,QACjCC,MAAMC,KAAK,CAAEvP,OAAQ0O,EAAc1O,SAAU,CAACwP,EAAGC,IAAMA,IACvD,GAIJ,GAFAtC,EAAgBiC,GAEZhD,EAAmB,CACrB,MAAMsD,EAAgBN,EACnBlQ,KAAIyQ,GAAOjB,EAAciB,KACzB5B,QAAQ3N,QAA6BkK,IAATlK,IAC/BgM,EAAkBsD,EACpB,GA2GkBL,QAASnC,EAAalN,SAAW0O,EAAc1O,QAAU0O,EAAc1O,OAAS,EAChF,cAAa,GAAGF,mBAIrBsF,EAAQlG,KAAK0Q,IACZ9Q,EAAAA,EAAAA,KAAA,MAEED,UAAW,kBAAkB+Q,EAAOC,OAAS,qEAAqED,EAAOtK,SAAW,mCAAqC,qCAAqCsK,EAAOE,MAAQF,EAAOE,MAAQ,MAAMF,EAAO/Q,WAAa,KACtQS,QAASA,IAAMsQ,EAAOtK,UAtNpBD,KAClB,IAAIwH,EAA4B,MAC5BF,GAAcA,EAAWtH,MAAQA,GAAgC,QAAzBsH,EAAWE,YACrDA,EAAY,QAEdD,EAAc,CAAEvH,MAAKwH,aAAY,EAiNiBkD,CAAWH,EAAOvK,KACpD,cAAa,GAAGvF,YAAiB8P,EAAOvK,MAAMzG,UAE9CmB,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,8BAA6BD,SAAA,EAC1CE,EAAAA,EAAAA,KAAA,QAAAF,SAAOgR,EAAOpQ,QACboQ,EAAOtK,WACNxG,EAAAA,EAAAA,KAAA,QAAMD,UAAW,oCACL,OAAV8N,QAAU,IAAVA,OAAU,EAAVA,EAAYtH,OAAQuK,EAAOvK,IAAM,eAAiB,iBACjDzG,UACU,OAAV+N,QAAU,IAAVA,OAAU,EAAVA,EAAYtH,OAAQuK,EAAOvK,KAAgC,QAAzBsH,EAAWE,WAC1C/N,EAAAA,EAAAA,KAACkN,EAAa,CAACnN,UAAU,aACf,OAAV8N,QAAU,IAAVA,OAAU,EAAVA,EAAYtH,OAAQuK,EAAOvK,KAAgC,SAAzBsH,EAAWE,WAC3C/N,EAAAA,EAAAA,KAACkR,EAAAA,EAAe,CAACnR,UAAU,aAC3BC,EAAAA,EAAAA,KAAA,QAAMD,UAAU,gBAAeD,SAAC,iBAfvCgR,EAAOvK,aAuBpBvG,EAAAA,EAAAA,KAAA,SAAOD,UAAU,oCAAmCD,SACjD8P,EAAc1O,OAAS,EACtB0O,EAAcxP,KAAI,CAAC8O,EAAK3N,KACtBN,EAAAA,EAAAA,MAAA,MAEElB,UAAW,qCACTiI,EAAa,iBAAmB,MAC9BoG,EAAaoB,SAASjO,GAAS,0BAA4B,2BAC7D+M,IAAe/M,EAAQ,aAAe,2BACtCmM,EAAeA,EAAawB,EAAK3N,GAAS,KAC5Cf,QAASA,IAAMwH,GAAcA,EAAWkH,GACxCiC,aAAcA,IAAM5C,EAAchN,GAClC6P,aAAcA,IAAM7C,EAAc,MAClC,cAAa,GAAGvN,SAAcO,IAAQzB,SAAA,CAErCuN,IACCrN,EAAAA,EAAAA,KAAA,MAAID,UAAU,8BAA6BD,UACzCE,EAAAA,EAAAA,KAAA,SACEwD,KAAK,WACLzD,UAAU,kEACVwQ,QAASnC,EAAaoB,SAASjO,GAC/BpB,SAAUA,OACVK,QAAU4P,GAjMViB,EAAC9P,EAAe8O,KACtCA,EAAMiB,kBAEN,MAAMhB,EAAkB,IAAIlC,GAE5B,GAAIA,EAAaoB,SAASjO,GAAQ,CAChC,MAAMsP,EAAMP,EAAgBiB,QAAQhQ,GACpC+O,EAAgBkB,OAAOX,EAAK,EAC9B,MACEP,EAAgBmB,KAAKlQ,GAKvB,GAFA8M,EAAgBiC,GAEZhD,EAAmB,CACrB,MAAMsD,EAAgBN,EACnBlQ,KAAIyQ,GAAOjB,EAAciB,KACzB5B,QAAQ3N,QAA6BkK,IAATlK,IAC/BgM,EAAkBsD,EACpB,GA8KsCS,CAAgB9P,EAAO6O,GACvC,cAAa,GAAGpP,SAAcO,iBAInC+E,EAAQlG,KAAK0Q,IACZ9Q,EAAAA,EAAAA,KAAA,MAEED,UAAW,oFAAoF+Q,EAAOC,OAAS,UAAUD,EAAO/Q,WAAa,KAC7I,cAAa,GAAGiB,SAAcO,UAAcuP,EAAOvK,MAAMzG,SAExDgR,EAAOrK,OACJqK,EAAOrK,OAAOyI,EAAI4B,EAAOvK,KAAM2I,GAC/B4B,EAAOvK,IAAIqG,cAAc4C,SAAS,UAChCQ,EAAkBd,EAAI4B,EAAOvK,MAC7B2I,EAAI4B,EAAOvK,MARZuK,EAAOvK,SAzBXhF,MAuCTvB,EAAAA,EAAAA,KAAA,MAAAF,UACEE,EAAAA,EAAAA,KAAA,MACE0R,QAASpL,EAAQpF,QAAUmM,EAAa,EAAI,GAC5CtN,UAAU,uCACV,cAAa,GAAGiB,kBAAuBlB,SAEtCqI,aAUdF,GAAcwH,EAAa,IAC1BxO,EAAAA,EAAAA,MAAA,OAAKlB,UAAW,wEAAwE0N,IAAkB3N,SAAA,EACxGmB,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,wBAAuBD,SAAA,CAAC,YAC1BoO,EAAc,GAAKhG,EAAY,EAAE,OAAKwH,KAAKiC,IAAIzD,EAAchG,EAAU8G,EAAa9N,QAAQ,OAAK8N,EAAa9N,OAAO,eAElID,EAAAA,EAAAA,MAAA,OAAKlB,UAAU,iBAAgBD,SAAA,EAC7BE,EAAAA,EAAAA,KAAA,UACEQ,QAASA,IAAMsP,EAAiBJ,KAAKkC,IAAI,EAAG1D,EAAc,IAC1DzN,SAA0B,IAAhByN,EACVnO,UAAW,iCACO,IAAhBmO,EACI,mCACA,mCAEN,cAAa,GAAGlN,oBAAyBlB,SAC1C,aAGA0Q,MAAMC,KAAK,CAAEvP,OAAQwO,KAAKiC,IAAI,EAAGlC,KAAe,CAACiB,EAAGC,KAEnD,IAAIkB,EAWJ,OATEA,EADEpC,GAAc,GAEPvB,GAAe,EADdyC,EAAI,EAGLzC,GAAeuB,EAAa,EAC3BA,EAAa,EAAIkB,EAEjBzC,EAAc,EAAIyC,GAI5B3Q,EAAAA,EAAAA,KAAA,UAEEQ,QAASA,IAAMsP,EAAiB+B,GAChC9R,UAAW,iCACTmO,IAAgB2D,EACZ,wBACA,mCAEN,cAAa,GAAG7Q,gBAAqB6Q,IAAU/R,SAE9C+R,GATIA,EAUE,KAGb7R,EAAAA,EAAAA,KAAA,UACEQ,QAASA,IAAMsP,EAAiBJ,KAAKiC,IAAIlC,EAAYvB,EAAc,IACnEzN,SAAUyN,IAAgBuB,EAC1B1P,UAAW,iCACTmO,IAAgBuB,EACZ,mCACA,mCAEN,cAAa,GAAGzO,oBAAyBlB,SAC1C,iBAQb,CAEA,SAAe4B,EAAAA,EAAAA,MAAKoG,E,6ECvZpB,MAAMM,EAA8BvI,IAiB7B,IAjB8B,OACnCwI,EAAM,QACNC,EAAO,MACP1H,EAAK,SACLd,EAAQ,KACRuD,EAAO,KAAI,OACXkF,EAAM,WACNuJ,GAAa,EAAI,qBACjBC,GAAuB,EAAI,gBAC3BC,GAAkB,EAAI,SACtBC,GAAW,EAAI,UACflS,EAAY,GAAE,cACdyN,EAAgB,GAAE,gBAClBD,EAAkB,GAAE,gBACpBE,EAAkB,GAAE,kBACpByE,EAAoB,GAAE,OACtBlR,GACDnB,EACC,MAAMsS,GAAWrJ,EAAAA,EAAAA,QAAuB,MA2DxC,IAxDAG,EAAAA,EAAAA,YAAU,KACR,MAAMmJ,EAAgBhC,IAChB0B,GAAwB,WAAV1B,EAAE7J,KAClB+B,GACF,EASF,OANID,IACFgK,SAASC,iBAAiB,UAAWF,GAErCC,SAASE,KAAKjG,MAAMkG,SAAW,UAG1B,KACLH,SAASI,oBAAoB,UAAWL,GACxCC,SAASE,KAAKjG,MAAMkG,SAAW,MAAM,CACtC,GACA,CAACnK,EAAQC,EAASwJ,KAGrB7I,EAAAA,EAAAA,YAAU,KACR,IAAKZ,IAAW8J,EAASjJ,QAAS,OAElC,MAAMwJ,EAAoBP,EAASjJ,QAAQyJ,iBACzC,4EAGF,GAAiC,IAA7BD,EAAkBxR,OAAc,OAEpC,MAAM0R,EAAeF,EAAkB,GACjCG,EAAcH,EAAkBA,EAAkBxR,OAAS,GAE3D4R,EAAgB1C,IACN,QAAVA,EAAE7J,MAEF6J,EAAE2C,SACAV,SAASW,gBAAkBJ,IAC7BC,EAAYI,QACZ7C,EAAE8C,kBAGAb,SAASW,gBAAkBH,IAC7BD,EAAaK,QACb7C,EAAE8C,kBAEN,EAMF,OAHAb,SAASC,iBAAiB,UAAWQ,GACrCF,EAAaK,QAEN,KACLZ,SAASI,oBAAoB,UAAWK,EAAa,CACtD,GACA,CAACzK,KAECA,EAAQ,OAAO,KAGpB,MAUM8K,GACJlS,EAAAA,EAAAA,MAACmS,EAAAA,SAAQ,CAAAtT,SAAA,EAEPE,EAAAA,EAAAA,KAAA,OACED,UAAW,gEAAgEmS,IAC3E1R,QAASuR,EAAuBzJ,OAAUkD,EAC1C,cAAa,GAAGxK,gBAIlBhB,EAAAA,EAAAA,KAAA,OAAKD,UAAU,qCAAoCD,UACjDE,EAAAA,EAAAA,KAAA,OAAKD,UAAW,yBAAyBkS,EAAW,SAAW,yCAAyCnS,UACtGmB,EAAAA,EAAAA,MAAA,OACEsB,IAAK4P,EACLpS,UAAW,GAxBD,CAClBsT,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,YACJC,GAAI,YACJC,KAAM,mBAkB4BrQ,2GAA8GtD,IACxIS,QAAU4P,GAAMA,EAAEkB,kBAClB,cAAatQ,EAAOlB,SAAA,EAGpBmB,EAAAA,EAAAA,MAAA,OAAKlB,UAAW,wEAAwEwN,IAAkBzN,SAAA,CACtF,kBAAVc,GACNZ,EAAAA,EAAAA,KAAA,MAAID,UAAU,sCAAqCD,SAAEc,IAErDA,EAEDoR,IACChS,EAAAA,EAAAA,KAAA,UACEwD,KAAK,SACLzD,UAAU,wGACVS,QAAS8H,EACT,aAAW,cACX,cAAa,GAAGtH,iBAAsBlB,UAEtCE,EAAAA,EAAAA,KAAC2T,EAAAA,EAAS,CAAC5T,UAAU,kBAM3BC,EAAAA,EAAAA,KAAA,OAAKD,UAAW,aAAayN,IAAgB1N,SAC1CA,IAIFyI,IACCvI,EAAAA,EAAAA,KAAA,OAAKD,UAAW,4EAA4E0N,IAAkB3N,SAC3GyI,cAUf,OAAOqL,EAAAA,EAAAA,cAAaT,EAAcd,SAASE,KAAK,EAGlD,GAAe7Q,EAAAA,EAAAA,MAAK0G,E,gDClLpB,SAASxB,EAAQ/G,EAId+B,GAAQ,IAJO,MAChBhB,EAAK,QACLiB,KACGC,GACJjC,EACC,OAAoBkC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQlB,EAAqBmB,EAAAA,cAAoB,QAAS,CAC3DxB,GAAIsB,GACHjB,GAAS,KAAmBmB,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,wFAEP,CACA,MACA,EADiCX,EAAAA,WAAiB6E,E,gDCvBlD,SAASiB,EAAQhI,EAId+B,GAAQ,IAJO,MAChBhB,EAAK,QACLiB,KACGC,GACJjC,EACC,OAAoBkC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQlB,EAAqBmB,EAAAA,cAAoB,QAAS,CAC3DxB,GAAIsB,GACHjB,GAAS,KAAmBmB,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,2BAEP,CACA,MACA,EADiCX,EAAAA,WAAiB8F,E,gDCvBlD,SAAS3D,EAAYrE,EAIlB+B,GAAQ,IAJW,MACpBhB,EAAK,QACLiB,KACGC,GACJjC,EACC,OAAoBkC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQlB,EAAqBmB,EAAAA,cAAoB,QAAS,CAC3DxB,GAAIsB,GACHjB,GAAS,KAAmBmB,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,mQAEP,CACA,MACA,EADiCX,EAAAA,WAAiBmC,E,gDCvBlD,SAAS0D,EAAS/H,EAIf+B,GAAQ,IAJQ,MACjBhB,EAAK,QACLiB,KACGC,GACJjC,EACC,OAAoBkC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQlB,EAAqBmB,EAAAA,cAAoB,QAAS,CAC3DxB,GAAIsB,GACHjB,GAAS,KAAmBmB,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,kaAEP,CACA,MACA,EADiCX,EAAAA,WAAiB6F,E,gDCvBlD,SAASL,EAAO1H,EAIb+B,GAAQ,IAJM,MACfhB,EAAK,QACLiB,KACGC,GACJjC,EACC,OAAoBkC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQlB,EAAqBmB,EAAAA,cAAoB,QAAS,CAC3DxB,GAAIsB,GACHjB,GAAS,KAAmBmB,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,6LACYX,EAAAA,cAAoB,OAAQ,CAC3CS,cAAe,QACfC,eAAgB,QAChBC,EAAG,wCAEP,CACA,MACA,EADiCX,EAAAA,WAAiBwF,E,gDC3BlD,SAASlD,EAASxE,EAIf+B,GAAQ,IAJQ,MACjBhB,EAAK,QACLiB,KACGC,GACJjC,EACC,OAAoBkC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQlB,EAAqBmB,EAAAA,cAAoB,QAAS,CAC3DxB,GAAIsB,GACHjB,GAAS,KAAmBmB,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,sWAEP,CACA,MACA,EADiCX,EAAAA,WAAiBsC,E", "sources": ["components/common/DetailList.tsx", "components/common/Tabs.tsx", "components/layout/PageHeader.tsx", "../node_modules/@heroicons/react/24/outline/esm/PencilIcon.js", "../node_modules/@heroicons/react/24/outline/esm/NoSymbolIcon.js", "../node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js", "../node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js", "features/suppliers/components/SupplierPersonalInfo.tsx", "features/suppliers/components/SupplierProducts.tsx", "pages/SupplierProfilePage.tsx", "utils/formatters.ts", "../node_modules/@heroicons/react/24/outline/esm/UserIcon.js", "components/common/StatusBadge.tsx", "components/common/DetailItem.tsx", "../node_modules/@heroicons/react/24/outline/esm/TruckIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js", "components/common/DetailSection.tsx", "../node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ChevronUpIcon.js", "components/common/DataTable.tsx", "components/common/Modal.tsx", "../node_modules/@heroicons/react/24/outline/esm/CubeIcon.js", "../node_modules/@heroicons/react/24/outline/esm/PlusIcon.js", "../node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js", "../node_modules/@heroicons/react/24/outline/esm/TrashIcon.js", "../node_modules/@heroicons/react/24/outline/esm/EyeIcon.js", "../node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js"], "sourcesContent": ["import React from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\ninterface DetailListProps {\r\n  children: ReactNode;\r\n  className?: string;\r\n}\r\n\r\nconst DetailList: React.FC<DetailListProps> = ({\r\n  children,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <dl className={`sm:divide-y sm:divide-gray-200 ${className}`}>\r\n      {children}\r\n    </dl>\r\n  );\r\n};\r\n\r\nexport default DetailList;", "// src/components/common/Tabs.tsx\r\nimport React from 'react';\r\n\r\nexport interface Tab {\r\n  id: string;\r\n  label: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface TabsProps {\r\n  tabs: Tab[];\r\n  activeTab: string;\r\n  onChange: (tabId: string) => void;\r\n  className?: string;\r\n}\r\n\r\nconst Tabs: React.FC<TabsProps> = ({\r\n  tabs,\r\n  activeTab,\r\n  onChange,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <div className={`border-b border-gray-200 ${className}`}>\r\n      <nav className=\"-mb-px flex space-x-8\">\r\n        {tabs.map((tab) => {\r\n          const isActive = tab.id === activeTab;\r\n          \r\n          return (\r\n            <button\r\n              key={tab.id}\r\n              onClick={() => !tab.disabled && onChange(tab.id)}\r\n              className={`\r\n                whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm\r\n                focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2\r\n                ${isActive\r\n                  ? 'border-primary text-primary'\r\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}\r\n                ${tab.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}\r\n              `}\r\n              disabled={tab.disabled}\r\n            >\r\n              {tab.label}\r\n            </button>\r\n          );\r\n        })}\r\n      </nav>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Tabs;", "/**\r\n * PageHeader Component\r\n * \r\n * A consistent header component for pages with title, description, and actions.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';\r\n\r\nexport interface BreadcrumbItem {\r\n  label: string;\r\n  path?: string;\r\n}\r\n\r\nexport interface PageHeaderProps {\r\n  title: string;\r\n  description?: string | ReactNode;\r\n  actions?: ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n  className?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst PageHeader: React.FC<PageHeaderProps> = ({\r\n  title,\r\n  description,\r\n  actions,\r\n  breadcrumbs,\r\n  className = '',\r\n  testId,\r\n}) => {\r\n  return (\r\n    <div \r\n      className={`mb-6 ${className}`}\r\n      data-testid={testId}\r\n    >\r\n      {/* Breadcrumbs */}\r\n      {breadcrumbs && breadcrumbs.length > 0 && (\r\n        <nav className=\"flex mb-4\" aria-label=\"Breadcrumb\">\r\n          <ol className=\"flex items-center space-x-1 text-sm text-gray-500\">\r\n            <li>\r\n              <Link \r\n                to=\"/\" \r\n                className=\"flex items-center hover:text-primary\"\r\n                aria-label=\"Home\"\r\n              >\r\n                <HomeIcon className=\"h-4 w-4\" />\r\n              </Link>\r\n            </li>\r\n            \r\n            {breadcrumbs.map((item, index) => (\r\n              <li key={index} className=\"flex items-center\">\r\n                <ChevronRightIcon className=\"h-4 w-4 mx-1 text-gray-400\" />\r\n                {item.path && index < breadcrumbs.length - 1 ? (\r\n                  <Link \r\n                    to={item.path} \r\n                    className=\"hover:text-primary\"\r\n                  >\r\n                    {item.label}\r\n                  </Link>\r\n                ) : (\r\n                  <span className=\"font-medium text-gray-700\">{item.label}</span>\r\n                )}\r\n              </li>\r\n            ))}\r\n          </ol>\r\n        </nav>\r\n      )}\r\n      \r\n      {/* Header Content */}\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n        <div>\r\n          <h1 className=\"text-2xl font-bold text-gray-800\">{title}</h1>\r\n          {description && typeof description === 'string' ? (\r\n            <p className=\"mt-1 text-sm text-gray-500\">{description}</p>\r\n          ) : (\r\n            description\r\n          )}\r\n        </div>\r\n        \r\n        {actions && (\r\n          <div className=\"flex flex-wrap gap-3 mt-2 sm:mt-0\">\r\n            {actions}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(PageHeader);\r\n", "import * as React from \"react\";\nfunction PencilIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PencilIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction NoSymbolIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M18.364 18.364A9 9 0 0 0 5.636 5.636m12.728 12.728A9 9 0 0 1 5.636 5.636m12.728 12.728L5.636 5.636\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(NoSymbolIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction MapPinIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MapPinIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction GlobeAltIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(GlobeAltIcon);\nexport default ForwardRef;", "/**\r\n * Supplier Personal Information Component\r\n *\r\n * This component displays supplier's basic details using the same design pattern\r\n * as UserDetails component with DetailSection, DetailList, and DetailItem.\r\n */\r\n\r\nimport React from 'react';\r\nimport DetailSection from '../../../components/common/DetailSection';\r\nimport DetailList from '../../../components/common/DetailList';\r\nimport DetailItem from '../../../components/common/DetailItem';\r\nimport StatusBadge from '../../../components/common/StatusBadge';\r\nimport Avatar from '../../../components/common/Avatar';\r\nimport type { Supplier } from '../types';\r\nimport {\r\n  EnvelopeIcon,\r\n  PhoneIcon,\r\n  MapPinIcon,\r\n  GlobeAltIcon,\r\n  UserIcon,\r\n  CheckCircleIcon,\r\n  XCircleIcon,\r\n  ClockIcon\r\n} from '@heroicons/react/24/outline';\r\n\r\ninterface SupplierPersonalInfoProps {\r\n  supplier: Supplier;\r\n}\r\n\r\nconst SupplierPersonalInfo: React.FC<SupplierPersonalInfoProps> = ({ supplier }) => {\r\n  const getVerificationIcon = (status: string) => {\r\n    switch (status) {\r\n      case 'verified':\r\n        return <CheckCircleIcon className=\"w-5 h-5 text-green-500\" />;\r\n      case 'pending':\r\n        return <ClockIcon className=\"w-5 h-5 text-yellow-500\" />;\r\n      case 'rejected':\r\n        return <XCircleIcon className=\"w-5 h-5 text-red-500\" />;\r\n      default:\r\n        return <ClockIcon className=\"w-5 h-5 text-gray-500\" />;\r\n    }\r\n  };\r\n\r\n  const getVerificationText = (status: string) => {\r\n    switch (status) {\r\n      case 'verified':\r\n        return 'Verified';\r\n      case 'pending':\r\n        return 'Pending verification';\r\n      case 'rejected':\r\n        return 'Rejected';\r\n      default:\r\n        return 'Unknown status';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Supplier Header */}\r\n      <DetailSection\r\n        title=\"Supplier Overview\"\r\n        description=\"Basic supplier information and verification status\"\r\n      >\r\n        <div className=\"px-6 py-4\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-4\">\r\n              <Avatar\r\n                {...(supplier.logo && { src: supplier.logo })}\r\n                alt={supplier.name}\r\n                name={supplier.name}\r\n                size=\"xl\"\r\n              />\r\n              <div>\r\n                <h3 className=\"text-lg font-medium text-gray-900\">{supplier.name}</h3>\r\n                <p className=\"text-sm text-gray-500\">ID: {supplier.id}</p>\r\n                <div className=\"mt-2 flex items-center space-x-3\">\r\n                  <StatusBadge status={supplier.status} type=\"supplier\" />\r\n                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\r\n                    supplier.verificationStatus === 'verified'\r\n                      ? 'bg-green-100 text-green-800'\r\n                      : supplier.verificationStatus === 'pending'\r\n                        ? 'bg-yellow-100 text-yellow-800'\r\n                        : 'bg-red-100 text-red-800'\r\n                  }`}>\r\n                    {supplier.verificationStatus ?\r\n                      supplier.verificationStatus.charAt(0).toUpperCase() + supplier.verificationStatus.slice(1) :\r\n                      'Unknown'\r\n                    }\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </DetailSection>\r\n\r\n      {/* Contact Information */}\r\n      <DetailSection\r\n        title=\"Contact Information\"\r\n        description=\"Primary contact details and communication preferences\"\r\n      >\r\n        <DetailList>\r\n          <DetailItem \r\n            label=\"Contact Person\" \r\n            value={\r\n              <div className=\"flex items-center\">\r\n                <UserIcon className=\"w-4 h-4 text-gray-400 mr-2\" />\r\n                {supplier.contactPerson}\r\n              </div>\r\n            } \r\n          />\r\n          <DetailItem \r\n            label=\"Email Address\" \r\n            value={\r\n              <div className=\"flex items-center\">\r\n                <EnvelopeIcon className=\"w-4 h-4 text-gray-400 mr-2\" />\r\n                <a \r\n                  href={`mailto:${supplier.email}`}\r\n                  className=\"text-primary hover:text-primary-dark\"\r\n                >\r\n                  {supplier.email}\r\n                </a>\r\n              </div>\r\n            } \r\n          />\r\n          <DetailItem \r\n            label=\"Phone Number\" \r\n            value={\r\n              <div className=\"flex items-center\">\r\n                <PhoneIcon className=\"w-4 h-4 text-gray-400 mr-2\" />\r\n                <a \r\n                  href={`tel:${supplier.phone}`}\r\n                  className=\"text-primary hover:text-primary-dark\"\r\n                >\r\n                  {supplier.phone}\r\n                </a>\r\n              </div>\r\n            } \r\n          />\r\n          <DetailItem \r\n            label=\"Address\" \r\n            value={\r\n              <div className=\"flex items-start\">\r\n                <MapPinIcon className=\"w-4 h-4 text-gray-400 mr-2 mt-0.5\" />\r\n                <span>{supplier.address}</span>\r\n              </div>\r\n            } \r\n          />\r\n          {supplier.website && (\r\n            <DetailItem \r\n              label=\"Website\" \r\n              value={\r\n                <div className=\"flex items-center\">\r\n                  <GlobeAltIcon className=\"w-4 h-4 text-gray-400 mr-2\" />\r\n                  <a \r\n                    href={supplier.website}\r\n                    target=\"_blank\"\r\n                    rel=\"noopener noreferrer\"\r\n                    className=\"text-primary hover:text-primary-dark\"\r\n                  >\r\n                    {supplier.website}\r\n                  </a>\r\n                </div>\r\n              } \r\n            />\r\n          )}\r\n        </DetailList>\r\n      </DetailSection>\r\n\r\n      {/* Business Information */}\r\n      <DetailSection\r\n        title=\"Business Information\"\r\n        description=\"Business categories and operational details\"\r\n      >\r\n        <DetailList>\r\n\r\n          <DetailItem\r\n            label=\"Business Categories\"\r\n            value={\r\n              <div className=\"flex flex-wrap gap-2\">\r\n                {supplier.categories && supplier.categories.length > 0 ? (\r\n                  supplier.categories.map((category, index) => (\r\n                    <span\r\n                      key={index}\r\n                      className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\"\r\n                    >\r\n                      {category}\r\n                    </span>\r\n                  ))\r\n                ) : (\r\n                  <span className=\"text-gray-500 text-sm\">No categories assigned</span>\r\n                )}\r\n              </div>\r\n            }\r\n          />\r\n          <DetailItem \r\n            label=\"Account Status\" \r\n            value={<StatusBadge status={supplier.status} type=\"supplier\" />} \r\n          />\r\n        </DetailList>\r\n      </DetailSection>\r\n\r\n      {/* Verification Status */}\r\n      <DetailSection\r\n        title=\"Verification Status\"\r\n        description=\"Current verification status and history\"\r\n      >\r\n        <div className=\"px-6 py-4\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            {getVerificationIcon(supplier.verificationStatus || 'pending')}\r\n            <div>\r\n              <div className=\"text-sm font-medium text-gray-900\">\r\n                {supplier.verificationStatus === 'verified' ? 'Verified Supplier' : \r\n                 supplier.verificationStatus === 'pending' ? 'Verification Pending' : \r\n                 'Verification Rejected'}\r\n              </div>\r\n              <div className=\"text-sm text-gray-500\">\r\n                {getVerificationText(supplier.verificationStatus || 'pending')}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </DetailSection>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SupplierPersonalInfo;\r\n", "/**\r\n * Supplier Products Component\r\n *\r\n * This component displays supplier's products in a data table format\r\n * with action icons for view, edit, and delete operations.\r\n */\r\n\r\nimport React, { useState } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport DataTable from '../../../components/common/DataTable';\r\nimport type { Column } from '../../../components/common/DataTable';\r\nimport DetailSection from '../../../components/common/DetailSection';\r\nimport Button from '../../../components/common/Button';\r\nimport StatusBadge from '../../../components/common/StatusBadge';\r\nimport Modal from '../../../components/common/Modal';\r\nimport useNotification from '../../../hooks/useNotification';\r\nimport type { SupplierProduct } from '../types';\r\nimport { ROUTES } from '../../../constants/routes';\r\nimport {\r\n  EyeIcon,\r\n  PencilIcon,\r\n  TrashIcon,\r\n  CubeIcon,\r\n  PlusIcon\r\n} from '@heroicons/react/24/outline';\r\nimport { formatCurrency } from '../../../utils/formatters';\r\n\r\ninterface SupplierProductsProps {\r\n  products: SupplierProduct[];\r\n  supplierId?: string;\r\n  onProductUpdate?: () => void;\r\n}\r\n\r\nconst SupplierProducts: React.FC<SupplierProductsProps> = ({\r\n  products,\r\n  supplierId: _supplierId,\r\n  onProductUpdate\r\n}) => {\r\n  const navigate = useNavigate();\r\n  const { showSuccess, showError, showInfo } = useNotification();\r\n  const [selectedProduct, setSelectedProduct] = useState<SupplierProduct | null>(null);\r\n  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);\r\n\r\n  const getStatusBadgeStatus = (status: string): string => {\r\n    switch (status) {\r\n      case 'active':\r\n        return 'active';\r\n      case 'inactive':\r\n        return 'pending';\r\n      case 'out_of_stock':\r\n        return 'rejected';\r\n      default:\r\n        return 'pending';\r\n    }\r\n  };\r\n\r\n  const getStockStatus = (stock: number, minimumStock: number, status: string) => {\r\n    if (status === 'out_of_stock' || stock === 0) {\r\n      return { text: 'Out of Stock', color: 'text-red-600' };\r\n    } else if (stock <= minimumStock) {\r\n      return { text: 'Low Stock', color: 'text-yellow-600' };\r\n    } else {\r\n      return { text: 'In Stock', color: 'text-green-600' };\r\n    }\r\n  };\r\n\r\n  const handleViewProduct = (product: SupplierProduct) => {\r\n    navigate(ROUTES.getProductDetailsRoute(product.id));\r\n  };\r\n\r\n  const handleEditProduct = (product: SupplierProduct) => {\r\n    // In a real implementation, this would navigate to edit page or open edit modal\r\n    showInfo(`Edit product: ${product.name}`);\r\n    console.log('Edit product:', product);\r\n  };\r\n\r\n  const handleDeleteProduct = (product: SupplierProduct) => {\r\n    setSelectedProduct(product);\r\n    setIsDeleteModalOpen(true);\r\n  };\r\n\r\n  const confirmDeleteProduct = async () => {\r\n    if (!selectedProduct) return;\r\n\r\n    try {\r\n      // In a real implementation, this would call an API\r\n      showSuccess(`Product \"${selectedProduct.name}\" deleted successfully`);\r\n      setIsDeleteModalOpen(false);\r\n      setSelectedProduct(null);\r\n      onProductUpdate?.();\r\n    } catch (error) {\r\n      showError('Failed to delete product');\r\n    }\r\n  };\r\n\r\n  const columns: Column<SupplierProduct>[] = [\r\n    {\r\n      key: 'name',\r\n      label: 'Product Name',\r\n      sortable: true,\r\n      render: (_value: string, product: SupplierProduct) => (\r\n        <div className=\"flex items-center\">\r\n          {product.image ? (\r\n            <img\r\n              src={product.image}\r\n              alt={product.name}\r\n              className=\"h-10 w-10 rounded-lg object-cover mr-3\"\r\n            />\r\n          ) : (\r\n            <div className=\"h-10 w-10 bg-gray-200 rounded-lg flex items-center justify-center mr-3\">\r\n              <CubeIcon className=\"h-5 w-5 text-gray-400\" />\r\n            </div>\r\n          )}\r\n          <div>\r\n            <div className=\"font-medium text-gray-900\">{product.name}</div>\r\n            <div className=\"text-xs text-gray-500\">ID: {product.id}</div>\r\n          </div>\r\n        </div>\r\n      )\r\n    },\r\n    {\r\n      key: 'sku',\r\n      label: 'SKU',\r\n      sortable: true,\r\n      render: (value: string) => (\r\n        <span className=\"font-mono text-sm text-gray-600\">{value}</span>\r\n      )\r\n    },\r\n    {\r\n      key: 'category',\r\n      label: 'Category',\r\n      sortable: true,\r\n      render: (value: string) => (\r\n        <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\r\n          {value}\r\n        </span>\r\n      )\r\n    },\r\n    {\r\n      key: 'price',\r\n      label: 'Price',\r\n      sortable: true,\r\n      render: (value: number) => (\r\n        <span className=\"font-medium text-gray-900\">{formatCurrency(value)}</span>\r\n      )\r\n    },\r\n    {\r\n      key: 'stock',\r\n      label: 'Stock',\r\n      sortable: true,\r\n      render: (value: number, product: SupplierProduct) => {\r\n        const stockStatus = getStockStatus(value, product.minimumStock || 10, product.status);\r\n        return (\r\n          <div>\r\n            <div className=\"font-medium text-gray-900\">{value}</div>\r\n            <div className={`text-xs ${stockStatus.color}`}>{stockStatus.text}</div>\r\n          </div>\r\n        );\r\n      }\r\n    },\r\n    {\r\n      key: 'status',\r\n      label: 'Status',\r\n      sortable: true,\r\n      render: (value: string) => (\r\n        <StatusBadge status={getStatusBadgeStatus(value)} type=\"supplier\" />\r\n      )\r\n    },\r\n    {\r\n      key: 'actions',\r\n      label: 'Actions',\r\n      sortable: false,\r\n      render: (_value: any, product: SupplierProduct) => (\r\n        <div className=\"flex items-center space-x-2\">\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"xs\"\r\n            onClick={() => handleViewProduct(product)}\r\n            icon={<EyeIcon className=\"w-4 h-4\" />}\r\n            title=\"View product details\"\r\n          >\r\n            View\r\n          </Button>\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"xs\"\r\n            onClick={() => handleEditProduct(product)}\r\n            icon={<PencilIcon className=\"w-4 h-4\" />}\r\n            title=\"Edit product\"\r\n          >\r\n            Edit\r\n          </Button>\r\n          <Button\r\n            variant=\"danger\"\r\n            size=\"xs\"\r\n            onClick={() => handleDeleteProduct(product)}\r\n            icon={<TrashIcon className=\"w-4 h-4\" />}\r\n            title=\"Delete product\"\r\n          >\r\n            Delete\r\n          </Button>\r\n        </div>\r\n      )\r\n    }\r\n  ];\r\n\r\n  if (products.length === 0) {\r\n    return (\r\n      <DetailSection\r\n        title=\"Products\"\r\n        description=\"Products offered by this supplier\"\r\n      >\r\n        <div className=\"px-6 py-8 text-center\">\r\n          <CubeIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\r\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No products found</h3>\r\n          <p className=\"mt-1 text-sm text-gray-500\">\r\n            This supplier has not added any products yet.\r\n          </p>\r\n          <div className=\"mt-6\">\r\n            <Button\r\n              variant=\"primary\"\r\n              icon={<PlusIcon className=\"w-4 h-4\" />}\r\n              onClick={() => showInfo('Add product functionality coming soon')}\r\n            >\r\n              Add Product\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </DetailSection>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <DetailSection\r\n        title=\"Products\"\r\n        description={`${products.length} products offered by this supplier`}\r\n      >\r\n        <DataTable<SupplierProduct>\r\n          columns={columns}\r\n          data={products}\r\n          onRowClick={handleViewProduct}\r\n          pagination={true}\r\n          pageSize={10}\r\n          emptyMessage=\"No products found\"\r\n          className=\"border-0\"\r\n        />\r\n      </DetailSection>\r\n\r\n      {/* Delete Confirmation Modal */}\r\n      {selectedProduct && (\r\n        <Modal\r\n          isOpen={isDeleteModalOpen}\r\n          onClose={() => setIsDeleteModalOpen(false)}\r\n          title=\"Delete Product\"\r\n          size=\"sm\"\r\n          footer={\r\n            <>\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={() => setIsDeleteModalOpen(false)}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button\r\n                variant=\"danger\"\r\n                onClick={confirmDeleteProduct}\r\n              >\r\n                Delete Product\r\n              </Button>\r\n            </>\r\n          }\r\n        >\r\n          <div className=\"text-sm text-gray-500\">\r\n            Are you sure you want to delete \"{selectedProduct.name}\"? This action cannot be undone.\r\n          </div>\r\n        </Modal>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SupplierProducts;\r\n", "/**\r\n * Supplier Profile Page\r\n *\r\n * This page displays comprehensive supplier information with multiple tabs/sections,\r\n * following the exact design pattern and styling used in UserEditPage and UserDetails.\r\n */\r\n\r\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\r\nimport { useParams, useNavigate } from 'react-router-dom';\r\nimport PageHeader from '../components/layout/PageHeader';\r\nimport Tabs from '../components/common/Tabs';\r\nimport LoadingSpinner from '../components/common/LoadingSpinner';\r\nimport Button from '../components/common/Button';\r\nimport Modal from '../components/common/Modal';\r\nimport useNotification from '../hooks/useNotification';\r\nimport { TrashIcon, NoSymbolIcon, CheckCircleIcon } from '@heroicons/react/24/outline';\r\nimport SupplierPersonalInfo from '../features/suppliers/components/SupplierPersonalInfo';\r\n// TEMPORARILY DISABLED: Documents and Analytics tabs\r\n// import SupplierDocuments from '../features/suppliers/components/SupplierDocuments';\r\nimport SupplierProducts from '../features/suppliers/components/SupplierProducts';\r\n// import SupplierAnalytics from '../features/suppliers/components/SupplierAnalytics';\r\nimport { useSuppliers } from '../features/suppliers/hooks/useSuppliers';\r\nimport type {\r\n  Supplier,\r\n  SupplierProduct,\r\n  // TEMPORARILY DISABLED: Documents and Analytics tabs\r\n  // SupplierDocument,\r\n  // SupplierAnalyticsData\r\n} from '../features/suppliers/types';\r\nimport { ROUTES } from '../constants/routes';\r\n\r\n\r\n\r\nconst SupplierProfilePage: React.FC = () => {\r\n  const { id: supplierId } = useParams<{ id: string }>();\r\n  const navigate = useNavigate();\r\n  const { showError, showSuccess } = useNotification();\r\n\r\n  // Use refs to store stable references to notification functions\r\n  const showErrorRef = useRef(showError);\r\n  const showSuccessRef = useRef(showSuccess);\r\n  const isMountedRef = useRef(true);\r\n\r\n  // Update refs when functions change\r\n  useEffect(() => {\r\n    showErrorRef.current = showError;\r\n    showSuccessRef.current = showSuccess;\r\n  }, [showError, showSuccess]);\r\n\r\n  // Cleanup on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      isMountedRef.current = false;\r\n    };\r\n  }, []);\r\n  \r\n  // State\r\n  // TEMPORARILY DISABLED: Documents and Analytics tabs - simplified tab state\r\n  const [activeTab, setActiveTab] = useState<'personal' | 'products'>('personal');\r\n  const [supplier, setSupplier] = useState<Supplier | null>(null);\r\n  const [supplierProducts, setSupplierProducts] = useState<SupplierProduct[]>([]);\r\n  // TEMPORARILY DISABLED: Documents and Analytics tabs\r\n  // const [supplierDocuments, setSupplierDocuments] = useState<SupplierDocument[]>([]);\r\n  // const [supplierAnalytics, setSupplierAnalytics] = useState<SupplierAnalyticsData | null>(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n  const [isBanModalOpen, setIsBanModalOpen] = useState(false);\r\n  const [isBanning, setIsBanning] = useState(false);\r\n  // TEMPORARILY DISABLED: Documents and Analytics tabs\r\n  // const [documentsAvailable, setDocumentsAvailable] = useState(true);\r\n  // const [analyticsAvailable, setAnalyticsAvailable] = useState(true);\r\n\r\n  // Use the useSuppliers hook for API integration\r\n  const {\r\n    getSupplierById,\r\n    getSupplierProducts,\r\n    // TEMPORARILY DISABLED: Documents and Analytics tabs\r\n    // getSupplierDocuments,\r\n    // getSupplierAnalytics,\r\n    deleteSupplier,\r\n    banSupplier,\r\n    unbanSupplier,\r\n    isLoading: hookLoading\r\n  } = useSuppliers();\r\n\r\n\r\n\r\n  // Fetch supplier data - optimized to prevent duplicate requests\r\n  const fetchSupplierData = useCallback(async () => {\r\n    if (!supplierId) {\r\n      setError('No supplier ID provided');\r\n      setIsLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      setError(null);\r\n\r\n      // Fetch supplier data from API (required)\r\n      const supplierData = await getSupplierById(supplierId);\r\n\r\n      if (!isMountedRef.current) return; // Prevent state updates if component unmounted\r\n      setSupplier(supplierData);\r\n\r\n      // Fetch products data (required)\r\n      const products = await getSupplierProducts(supplierId);\r\n\r\n      if (!isMountedRef.current) return;\r\n      setSupplierProducts(products);\r\n\r\n      // TEMPORARILY DISABLED: Documents and Analytics tabs\r\n      // Fetch optional data (documents and analytics) - these may return empty/null for 404s\r\n      // const [documents, analytics] = await Promise.all([\r\n      //   getSupplierDocuments(supplierId),\r\n      //   getSupplierAnalytics(supplierId)\r\n      // ]);\r\n\r\n      // if (!isMountedRef.current) return;\r\n\r\n      // Handle documents response\r\n      // setSupplierDocuments(documents || []);\r\n      // setDocumentsAvailable(documents && documents.length > 0);\r\n\r\n      // Handle analytics response\r\n      // setSupplierAnalytics(analytics);\r\n      // setAnalyticsAvailable(analytics !== null);\r\n\r\n    } catch (error) {\r\n      if (!isMountedRef.current) return;\r\n\r\n      console.error('Error fetching supplier data:', error);\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch supplier data';\r\n      setError(errorMessage);\r\n      showErrorRef.current('Failed to load supplier data');\r\n    } finally {\r\n      if (isMountedRef.current) {\r\n        setIsLoading(false);\r\n      }\r\n    }\r\n  }, [supplierId, getSupplierById, getSupplierProducts]); // TEMPORARILY DISABLED: getSupplierDocuments, getSupplierAnalytics\r\n\r\n  // Effect to fetch data when supplierId changes\r\n  useEffect(() => {\r\n    fetchSupplierData();\r\n  }, [fetchSupplierData]);\r\n\r\n  // TEMPORARILY DISABLED: Documents and Analytics tabs\r\n  // Effect to handle tab switching when endpoints become unavailable\r\n  // useEffect(() => {\r\n  //   if (activeTab === 'documents' && !documentsAvailable) {\r\n  //     setActiveTab('personal');\r\n  //   } else if (activeTab === 'analytics' && !analyticsAvailable) {\r\n  //     setActiveTab('personal');\r\n  //   }\r\n  // }, [activeTab, documentsAvailable, analyticsAvailable]);\r\n\r\n\r\n\r\n  // Handle supplier deletion\r\n  const handleDeleteSupplier = async () => {\r\n    if (!supplier) return;\r\n\r\n    try {\r\n      setIsDeleting(true);\r\n\r\n      await deleteSupplier(supplier.id);\r\n\r\n      showSuccessRef.current(`Supplier \"${supplier.name}\" has been deleted successfully`);\r\n      setIsDeleteModalOpen(false);\r\n\r\n      // Navigate back to suppliers list\r\n      navigate(ROUTES.SUPPLIERS);\r\n\r\n    } catch (error) {\r\n      console.error('Error deleting supplier:', error);\r\n      showErrorRef.current('Failed to delete supplier');\r\n    } finally {\r\n      setIsDeleting(false);\r\n    }\r\n  };\r\n\r\n  // Handle supplier ban\r\n  const handleBanSupplier = async () => {\r\n    if (!supplier) return;\r\n\r\n    try {\r\n      setIsBanning(true);\r\n\r\n      await banSupplier(supplier.id);\r\n\r\n      showSuccessRef.current(`Supplier \"${supplier.name}\" has been banned successfully`);\r\n      setIsBanModalOpen(false);\r\n\r\n      // Refresh supplier data to show updated status\r\n      await fetchSupplierData();\r\n\r\n    } catch (error) {\r\n      console.error('Error banning supplier:', error);\r\n      showErrorRef.current('Failed to ban supplier');\r\n    } finally {\r\n      setIsBanning(false);\r\n    }\r\n  };\r\n\r\n  // Handle supplier unban\r\n  const handleUnbanSupplier = async () => {\r\n    if (!supplier) return;\r\n\r\n    try {\r\n      setIsBanning(true);\r\n\r\n      await unbanSupplier(supplier.id);\r\n\r\n      showSuccessRef.current(`Supplier \"${supplier.name}\" has been unbanned successfully`);\r\n\r\n      // Refresh supplier data to show updated status\r\n      await fetchSupplierData();\r\n\r\n    } catch (error) {\r\n      console.error('Error unbanning supplier:', error);\r\n      showErrorRef.current('Failed to unban supplier');\r\n    } finally {\r\n      setIsBanning(false);\r\n    }\r\n  };\r\n\r\n  // Handle tab change\r\n  const handleTabChange = (tabId: string) => {\r\n    // TEMPORARILY DISABLED: Documents and Analytics tabs - simplified tab handling\r\n    const typedTabId = tabId as 'personal' | 'products';\r\n\r\n    // TEMPORARILY DISABLED: Documents and Analytics tabs\r\n    // Prevent switching to disabled tabs\r\n    // if (typedTabId === 'documents' && !documentsAvailable) return;\r\n    // if (typedTabId === 'analytics' && !analyticsAvailable) return;\r\n\r\n    setActiveTab(typedTabId);\r\n  };\r\n\r\n  // Loading state\r\n  if (isLoading || hookLoading) {\r\n    return (\r\n      <div className=\"flex justify-center items-center min-h-screen\">\r\n        <LoadingSpinner size=\"lg\" />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Error state\r\n  if (error || !supplier) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <PageHeader\r\n          title=\"Supplier Profile\"\r\n          description=\"Supplier not found\"\r\n          breadcrumbs={[\r\n            { label: 'Suppliers', path: ROUTES.SUPPLIERS },\r\n            { label: 'Profile' }\r\n          ]}\r\n        />\r\n        <div className=\"text-center py-12\">\r\n          <p className=\"text-gray-500\">{error || 'Supplier not found'}</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <PageHeader\r\n        title={`Supplier: ${supplier.name}`}\r\n        description=\"Comprehensive supplier profile and management\"\r\n        breadcrumbs={[\r\n          { label: 'Suppliers', path: ROUTES.SUPPLIERS },\r\n          { label: supplier.name }\r\n        ]}\r\n        actions={\r\n          <div className=\"flex gap-2\">\r\n            {supplier.status === 'banned' ? (\r\n              <Button\r\n                variant=\"success\"\r\n                size=\"sm\"\r\n                onClick={handleUnbanSupplier}\r\n                icon={<CheckCircleIcon className=\"h-4 w-4\" />}\r\n                disabled={isLoading || isBanning || isDeleting}\r\n                loading={isBanning}\r\n              >\r\n                Unban Supplier\r\n              </Button>\r\n            ) : (\r\n              <Button\r\n                variant=\"secondary\"\r\n                size=\"sm\"\r\n                onClick={() => setIsBanModalOpen(true)}\r\n                icon={<NoSymbolIcon className=\"h-4 w-4\" />}\r\n                disabled={isLoading || isBanning || isDeleting}\r\n              >\r\n                Ban Supplier\r\n              </Button>\r\n            )}\r\n            <Button\r\n              variant=\"danger\"\r\n              size=\"sm\"\r\n              onClick={() => setIsDeleteModalOpen(true)}\r\n              icon={<TrashIcon className=\"h-4 w-4\" />}\r\n              disabled={isLoading || isDeleting || isBanning}\r\n            >\r\n              Delete Supplier\r\n            </Button>\r\n          </div>\r\n        }\r\n      />\r\n      \r\n      <Tabs\r\n        tabs={[\r\n          { id: 'personal', label: 'Personal Information' },\r\n          // TEMPORARILY DISABLED: Documents and Analytics tabs\r\n          // {\r\n          //   id: 'documents',\r\n          //   label: documentsAvailable ? 'Verification Documents' : 'Documents (Coming Soon)',\r\n          //   disabled: !documentsAvailable\r\n          // },\r\n          { id: 'products', label: 'Products' },\r\n          // {\r\n          //   id: 'analytics',\r\n          //   label: analyticsAvailable ? 'Analytics' : 'Analytics (Coming Soon)',\r\n          //   disabled: !analyticsAvailable\r\n          // }\r\n        ]}\r\n        activeTab={activeTab}\r\n        onChange={handleTabChange}\r\n      />\r\n      \r\n      {activeTab === 'personal' && (\r\n        <SupplierPersonalInfo supplier={supplier} />\r\n      )}\r\n\r\n      {/* TEMPORARILY DISABLED: Documents tab content - removed for maintenance */}\r\n\r\n      {activeTab === 'products' && (\r\n        <SupplierProducts\r\n          products={supplierProducts}\r\n          supplierId={supplier.id}\r\n          onProductUpdate={fetchSupplierData}\r\n        />\r\n      )}\r\n\r\n      {/* TEMPORARILY DISABLED: Analytics tab content - removed for maintenance */}\r\n\r\n      {/* Ban Confirmation Modal */}\r\n      <Modal\r\n        isOpen={isBanModalOpen}\r\n        onClose={() => setIsBanModalOpen(false)}\r\n        title=\"Ban Supplier\"\r\n        size=\"sm\"\r\n        footer={\r\n          <>\r\n            <Button\r\n              variant=\"outline\"\r\n              onClick={() => setIsBanModalOpen(false)}\r\n              disabled={isBanning}\r\n            >\r\n              Cancel\r\n            </Button>\r\n            <Button\r\n              variant=\"secondary\"\r\n              onClick={handleBanSupplier}\r\n              loading={isBanning}\r\n              icon={<NoSymbolIcon className=\"h-4 w-4\" />}\r\n            >\r\n              Ban Supplier\r\n            </Button>\r\n          </>\r\n        }\r\n      >\r\n        <div className=\"text-sm text-gray-500\">\r\n          <p className=\"mb-3\">\r\n            Are you sure you want to ban <strong>\"{supplier.name}\"</strong>?\r\n          </p>\r\n          <p className=\"text-orange-600 font-medium\">\r\n            This action will:\r\n          </p>\r\n          <ul className=\"mt-2 list-disc list-inside text-orange-600\">\r\n            <li>Change the supplier's status to 'banned'</li>\r\n            <li>Prevent them from receiving new orders</li>\r\n            <li>Restrict their access to the platform</li>\r\n            <li>Allow for potential future reactivation</li>\r\n          </ul>\r\n          <p className=\"mt-3 text-gray-600\">\r\n            Unlike deletion, this action can be reversed by changing the supplier's status back to 'active'.\r\n          </p>\r\n        </div>\r\n      </Modal>\r\n\r\n      {/* Delete Confirmation Modal */}\r\n      <Modal\r\n        isOpen={isDeleteModalOpen}\r\n        onClose={() => setIsDeleteModalOpen(false)}\r\n        title=\"Delete Supplier\"\r\n        size=\"sm\"\r\n        footer={\r\n          <>\r\n            <Button\r\n              variant=\"outline\"\r\n              onClick={() => setIsDeleteModalOpen(false)}\r\n              disabled={isDeleting}\r\n            >\r\n              Cancel\r\n            </Button>\r\n            <Button\r\n              variant=\"danger\"\r\n              onClick={handleDeleteSupplier}\r\n              loading={isDeleting}\r\n              icon={<TrashIcon className=\"h-4 w-4\" />}\r\n            >\r\n              Delete Supplier\r\n            </Button>\r\n          </>\r\n        }\r\n      >\r\n        <div className=\"text-sm text-gray-500\">\r\n          <p className=\"mb-3\">\r\n            Are you sure you want to delete <strong>\"{supplier.name}\"</strong>?\r\n          </p>\r\n          <p className=\"text-red-600 font-medium\">\r\n            This action cannot be undone and will permanently remove:\r\n          </p>\r\n          <ul className=\"mt-2 list-disc list-inside text-red-600\">\r\n            <li>All supplier information</li>\r\n            <li>Associated products and documents</li>\r\n            <li>Order history and analytics</li>\r\n          </ul>\r\n        </div>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SupplierProfilePage;\r\n", "/**\r\n * Formatters\r\n * \r\n * This file contains utility functions for formatting data.\r\n */\r\n\r\n/**\r\n * Format a date string to a human-readable format\r\n */\r\nexport const formatDate = (dateString: string, options: Intl.DateTimeFormatOptions = {}): string => {\r\n  if (!dateString) return '-';\r\n  \r\n  try {\r\n    const date = new Date(dateString);\r\n    \r\n    // Default options\r\n    const defaultOptions: Intl.DateTimeFormatOptions = {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      ...options\r\n    };\r\n    \r\n    return new Intl.DateTimeFormat('en-US', defaultOptions).format(date);\r\n  } catch (error) {\r\n    console.error('Error formatting date:', error);\r\n    return dateString;\r\n  }\r\n};\r\n\r\n/**\r\n * Format a date string to include time\r\n */\r\nexport const formatDateTime = (dateString: string): string => {\r\n  return formatDate(dateString, {\r\n    year: 'numeric',\r\n    month: 'short',\r\n    day: 'numeric',\r\n    hour: '2-digit',\r\n    minute: '2-digit'\r\n  });\r\n};\r\n\r\n/**\r\n * Format a number as currency\r\n */\r\nexport const formatCurrency = (\r\n  amount: number,\r\n  currency: string = 'USD',\r\n  locale: string = 'en-US'\r\n): string => {\r\n  try {\r\n    return new Intl.NumberFormat(locale, {\r\n      style: 'currency',\r\n      currency,\r\n      minimumFractionDigits: 2,\r\n      maximumFractionDigits: 2\r\n    }).format(amount);\r\n  } catch (error) {\r\n    console.error('Error formatting currency:', error);\r\n    return `${currency} ${amount.toFixed(2)}`;\r\n  }\r\n};\r\n\r\n/**\r\n * Format a number with commas\r\n */\r\nexport const formatNumber = (\r\n  number: number,\r\n  options: Intl.NumberFormatOptions = {}\r\n): string => {\r\n  try {\r\n    return new Intl.NumberFormat('en-US', options).format(number);\r\n  } catch (error) {\r\n    console.error('Error formatting number:', error);\r\n    return number.toString();\r\n  }\r\n};\r\n\r\n/**\r\n * Format a phone number\r\n */\r\nexport const formatPhoneNumber = (phoneNumber: string): string => {\r\n  if (!phoneNumber) return '-';\r\n  \r\n  // Remove all non-numeric characters\r\n  const cleaned = phoneNumber.replace(/\\D/g, '');\r\n  \r\n  // Format based on length\r\n  if (cleaned.length === 10) {\r\n    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;\r\n  } else if (cleaned.length === 11 && cleaned.startsWith('1')) {\r\n    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;\r\n  }\r\n  \r\n  // If it doesn't match expected formats, return as is\r\n  return phoneNumber;\r\n};\r\n\r\n/**\r\n * Truncate text with ellipsis\r\n */\r\nexport const truncateText = (text: string, maxLength: number): string => {\r\n  if (!text) return '';\r\n  if (text.length <= maxLength) return text;\r\n  \r\n  return `${text.slice(0, maxLength)}...`;\r\n};\r\n\r\n/**\r\n * Format file size\r\n */\r\nexport const formatFileSize = (bytes: number): string => {\r\n  if (bytes === 0) return '0 Bytes';\r\n  \r\n  const k = 1024;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n  \r\n  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;\r\n};\r\n\r\n/**\r\n * Format a duration in milliseconds to a human-readable format\r\n */\r\nexport const formatDuration = (milliseconds: number): string => {\r\n  const seconds = Math.floor(milliseconds / 1000);\r\n  const minutes = Math.floor(seconds / 60);\r\n  const hours = Math.floor(minutes / 60);\r\n  const days = Math.floor(hours / 24);\r\n  \r\n  if (days > 0) {\r\n    return `${days} day${days > 1 ? 's' : ''}`;\r\n  } else if (hours > 0) {\r\n    return `${hours} hour${hours > 1 ? 's' : ''}`;\r\n  } else if (minutes > 0) {\r\n    return `${minutes} minute${minutes > 1 ? 's' : ''}`;\r\n  } else {\r\n    return `${seconds} second${seconds !== 1 ? 's' : ''}`;\r\n  }\r\n};\r\n", "import * as React from \"react\";\nfunction UserIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(UserIcon);\nexport default ForwardRef;", "import React from 'react';\r\nimport {\r\n  CheckCircleIcon,\r\n  XCircleIcon,\r\n  ClockIcon,\r\n  TruckIcon,\r\n  ExclamationCircleIcon\r\n} from '@heroicons/react/24/outline';\r\n\r\nexport type StatusType = 'user' | 'supplier' | 'order' | 'verification' | 'category';\r\n\r\ninterface StatusBadgeProps {\r\n  status: string;\r\n  type?: StatusType;\r\n  className?: string;\r\n}\r\n\r\nconst StatusBadge: React.FC<StatusBadgeProps> = ({\r\n  status,\r\n  type: _type = 'user',\r\n  className = ''\r\n}) => {\r\n  // Handle undefined or null status\r\n  if (!status) {\r\n    return (\r\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 ${className}`}>\r\n        Unknown\r\n      </span>\r\n    );\r\n  }\r\n\r\n  const statusKey = status.toLowerCase();\r\n  let colorClass = '';\r\n  let icon = null;\r\n  \r\n  // Common statuses across entity types\r\n  if (statusKey === 'active' || statusKey === 'verified' || statusKey === 'completed') {\r\n    colorClass = 'bg-green-100 text-green-800';\r\n    icon = <CheckCircleIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'pending' || statusKey === 'processing') {\r\n    colorClass = 'bg-blue-100 text-blue-800';\r\n    icon = <ClockIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'banned' || statusKey === 'rejected') {\r\n    colorClass = 'bg-red-100 text-red-800';\r\n    icon = <XCircleIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'shipped') {\r\n    colorClass = 'bg-purple-100 text-purple-800';\r\n    icon = <TruckIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'warning') {\r\n    colorClass = 'bg-yellow-100 text-yellow-800';\r\n    icon = <ExclamationCircleIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else {\r\n    colorClass = 'bg-gray-100 text-gray-800';\r\n  }\r\n  \r\n  // Format the status text (capitalize first letter)\r\n  const formattedStatus = status ? status.charAt(0).toUpperCase() + status.slice(1) : 'Unknown';\r\n  \r\n  return (\r\n    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass} ${className}`}>\r\n      {icon}\r\n      {formattedStatus}\r\n    </span>\r\n  );\r\n};\r\n\r\nexport default StatusBadge;\r\n", "import React from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\ninterface DetailItemProps {\r\n  label: string;\r\n  value: ReactNode;\r\n  className?: string;\r\n}\r\n\r\nconst DetailItem: React.FC<DetailItemProps> = ({\r\n  label,\r\n  value,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <div className={`py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 ${className}`}>\r\n      <dt className=\"text-sm font-medium text-gray-500\">{label}</dt>\r\n      <dd className=\"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\">{value}</dd>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DetailItem;", "import * as React from \"react\";\nfunction TruckIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(TruckIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ChevronRightIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChevronRightIcon);\nexport default ForwardRef;", "import React from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\ninterface DetailSectionProps {\r\n  title: string;\r\n  description?: string;\r\n  children: ReactNode;\r\n  className?: string;\r\n}\r\n\r\nconst DetailSection: React.FC<DetailSectionProps> = ({\r\n  title,\r\n  description,\r\n  children,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <div className={`bg-white shadow overflow-hidden sm:rounded-lg ${className}`}>\r\n      <div className=\"px-4 py-5 sm:px-6\">\r\n        <h3 className=\"text-lg leading-6 font-medium text-gray-900\">{title}</h3>\r\n        {description && (\r\n          <p className=\"mt-1 max-w-2xl text-sm text-gray-500\">{description}</p>\r\n        )}\r\n      </div>\r\n      <div className=\"border-t border-gray-200\">\r\n        {children}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DetailSection;", "import * as React from \"react\";\nfunction MagnifyingGlassIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MagnifyingGlassIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ChevronUpIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m4.5 15.75 7.5-7.5 7.5 7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChevronUpIcon);\nexport default ForwardRef;", "/**\r\n * DataTable Component\r\n *\r\n * A reusable data table component with sorting, filtering, pagination, and row selection.\r\n */\r\n\r\nimport React, { useState, useMemo, memo } from 'react';\r\nimport { MagnifyingGlassIcon, ChevronUpIcon, ChevronDownIcon } from '@heroicons/react/24/outline';\r\nimport LoadingSpinner from './LoadingSpinner';\r\nimport { CONFIG } from '../../constants/config';\r\n\r\nexport interface Column<T = Record<string, any>> {\r\n  key: string;\r\n  label: string;\r\n  sortable?: boolean;\r\n  render?: (value: any, row: T) => React.ReactNode;\r\n  width?: string;\r\n  align?: 'left' | 'center' | 'right';\r\n  className?: string;\r\n}\r\n\r\nexport interface DataTableProps<T = Record<string, any>> {\r\n  columns: Column<T>[];\r\n  data: T[];\r\n  onRowClick?: ((row: T) => void) | undefined;\r\n  title?: string | React.ReactNode;\r\n  description?: string | React.ReactNode;\r\n  loading?: boolean;\r\n  pagination?: boolean;\r\n  pageSize?: number;\r\n  selectable?: boolean;\r\n  onSelectionChange?: (selectedRows: T[]) => void;\r\n  actions?: React.ReactNode;\r\n  emptyMessage?: string;\r\n  className?: string;\r\n  headerClassName?: string;\r\n  bodyClassName?: string;\r\n  footerClassName?: string;\r\n  rowClassName?: (row: T, index: number) => string;\r\n  initialSortKey?: string;\r\n  initialSortDirection?: 'asc' | 'desc';\r\n  testId?: string;\r\n}\r\n\r\nfunction DataTable<T extends Record<string, any>>({\r\n  columns,\r\n  data,\r\n  onRowClick,\r\n  title,\r\n  description,\r\n  loading = false,\r\n  pagination = true,\r\n  pageSize = CONFIG.DEFAULT_PAGE_SIZE,\r\n  selectable = true,\r\n  onSelectionChange,\r\n  actions,\r\n  emptyMessage = 'No results found',\r\n  className = '',\r\n  headerClassName = '',\r\n  bodyClassName = '',\r\n  footerClassName = '',\r\n  rowClassName,\r\n  initialSortKey,\r\n  initialSortDirection = 'asc',\r\n  testId,\r\n}: DataTableProps<T>) {\r\n  // State\r\n  const [sortConfig, setSortConfig] = useState<{\r\n    key: string;\r\n    direction: 'asc' | 'desc';\r\n  } | null>(initialSortKey ? { key: initialSortKey, direction: initialSortDirection } : null);\r\n\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [selectedRows, setSelectedRows] = useState<number[]>([]);\r\n  const [hoveredRow, setHoveredRow] = useState<number | null>(null);\r\n\r\n  // Sorting\r\n  const handleSort = (key: string) => {\r\n    let direction: 'asc' | 'desc' = 'asc';\r\n    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {\r\n      direction = 'desc';\r\n    }\r\n    setSortConfig({ key, direction });\r\n  };\r\n\r\n  const sortedData = useMemo(() => {\r\n    if (!sortConfig) return data;\r\n\r\n    return [...data].sort((a, b) => {\r\n      const aValue = a[sortConfig.key];\r\n      const bValue = b[sortConfig.key];\r\n\r\n      // Handle null or undefined values\r\n      if (aValue == null && bValue == null) return 0;\r\n      if (aValue == null) return sortConfig.direction === 'asc' ? -1 : 1;\r\n      if (bValue == null) return sortConfig.direction === 'asc' ? 1 : -1;\r\n\r\n      // Handle different data types\r\n      if (typeof aValue === 'string' && typeof bValue === 'string') {\r\n        return sortConfig.direction === 'asc'\r\n          ? aValue.localeCompare(bValue)\r\n          : bValue.localeCompare(aValue);\r\n      }\r\n\r\n      if (aValue < bValue) {\r\n        return sortConfig.direction === 'asc' ? -1 : 1;\r\n      }\r\n      if (aValue > bValue) {\r\n        return sortConfig.direction === 'asc' ? 1 : -1;\r\n      }\r\n      return 0;\r\n    });\r\n  }, [data, sortConfig]);\r\n\r\n  // Filtering\r\n  const filteredData = useMemo(() => {\r\n    if (!searchTerm) return sortedData;\r\n\r\n    return sortedData.filter((row) =>\r\n      Object.entries(row).some(([_key, value]) => {\r\n        // Skip filtering on complex objects\r\n        if (value === null || value === undefined) return false;\r\n        if (typeof value === 'object') return false;\r\n\r\n        return String(value).toLowerCase().includes(searchTerm.toLowerCase());\r\n      })\r\n    );\r\n  }, [sortedData, searchTerm]);\r\n\r\n  // Pagination\r\n  const totalPages = Math.ceil(filteredData.length / pageSize);\r\n  const paginatedData = useMemo(() => {\r\n    const startIndex = (currentPage - 1) * pageSize;\r\n    return filteredData.slice(startIndex, startIndex + pageSize);\r\n  }, [filteredData, currentPage, pageSize]);\r\n\r\n  const handlePageChange = (page: number) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  // Row selection\r\n  const handleRowSelect = (index: number, event: React.MouseEvent) => {\r\n    event.stopPropagation();\r\n\r\n    const newSelectedRows = [...selectedRows];\r\n\r\n    if (selectedRows.includes(index)) {\r\n      const idx = newSelectedRows.indexOf(index);\r\n      newSelectedRows.splice(idx, 1);\r\n    } else {\r\n      newSelectedRows.push(index);\r\n    }\r\n\r\n    setSelectedRows(newSelectedRows);\r\n\r\n    if (onSelectionChange) {\r\n      const selectedItems = newSelectedRows\r\n        .map(idx => paginatedData[idx])\r\n        .filter((item): item is T => item !== undefined);\r\n      onSelectionChange(selectedItems);\r\n    }\r\n  };\r\n\r\n  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const newSelectedRows = event.target.checked\r\n      ? Array.from({ length: paginatedData.length }, (_, i) => i)\r\n      : [];\r\n\r\n    setSelectedRows(newSelectedRows);\r\n\r\n    if (onSelectionChange) {\r\n      const selectedItems = newSelectedRows\r\n        .map(idx => paginatedData[idx])\r\n        .filter((item): item is T => item !== undefined);\r\n      onSelectionChange(selectedItems);\r\n    }\r\n  };\r\n\r\n  // Status badge renderer\r\n  const renderStatusBadge = (status: string) => {\r\n    let bgColor = 'bg-gray-100 text-gray-800';\r\n\r\n    if (typeof status === 'string') {\r\n      const statusLower = status.toLowerCase();\r\n\r\n      if (statusLower.includes('active') || statusLower.includes('approved') ||\r\n          statusLower.includes('verified') || statusLower.includes('completed') ||\r\n          statusLower.includes('success')) {\r\n        bgColor = 'bg-green-100 text-green-800';\r\n      } else if (statusLower.includes('pending') || statusLower.includes('processing')) {\r\n        bgColor = 'bg-yellow-100 text-yellow-800';\r\n      } else if (statusLower.includes('rejected') || statusLower.includes('banned') ||\r\n                statusLower.includes('failed') || statusLower.includes('error')) {\r\n        bgColor = 'bg-red-100 text-red-800';\r\n      } else if (statusLower.includes('inactive')) {\r\n        bgColor = 'bg-gray-100 text-gray-800';\r\n      }\r\n    }\r\n\r\n    return (\r\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${bgColor}`}>\r\n        {status}\r\n      </span>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-md ${className}`}\r\n      data-testid={testId}\r\n    >\r\n      {/* Header */}\r\n      {(title || description) && (\r\n        <div className={`px-6 py-4 border-b border-gray-100 ${headerClassName}`}>\r\n          {typeof title === 'string' ? (\r\n            <h3 className=\"text-lg font-semibold text-gray-800\">{title}</h3>\r\n          ) : (\r\n            title\r\n          )}\r\n          {typeof description === 'string' ? (\r\n            <p className=\"mt-1 text-sm text-gray-500\">{description}</p>\r\n          ) : (\r\n            description\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      {/* Search and Actions */}\r\n      <div className=\"p-4 border-b border-gray-100 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\r\n        <div className=\"relative flex-1\">\r\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n            <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\r\n          </div>\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"Search...\"\r\n            className=\"block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200\"\r\n            value={searchTerm}\r\n            onChange={(e) => {\r\n              setSearchTerm(e.target.value);\r\n              setCurrentPage(1); // Reset to first page on search\r\n            }}\r\n            data-testid={`${testId}-search`}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-2\">\r\n          {selectedRows.length > 0 && (\r\n            <div className=\"flex items-center space-x-2\">\r\n              <span className=\"text-sm text-gray-500\">{selectedRows.length} selected</span>\r\n              <button\r\n                className=\"px-3 py-1.5 bg-red-50 text-red-600 rounded-md text-sm font-medium hover:bg-red-100 transition-colors\"\r\n                onClick={() => {\r\n                  setSelectedRows([]);\r\n                  if (onSelectionChange) onSelectionChange([]);\r\n                }}\r\n                data-testid={`${testId}-clear-selection`}\r\n              >\r\n                Clear\r\n              </button>\r\n            </div>\r\n          )}\r\n          {actions}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Table */}\r\n      <div className={`overflow-x-auto ${bodyClassName}`}>\r\n        {loading ? (\r\n          <div className=\"flex justify-center items-center py-20\">\r\n            <LoadingSpinner size=\"lg\" variant=\"spinner\" />\r\n          </div>\r\n        ) : (\r\n          <table className=\"min-w-full divide-y divide-gray-100\">\r\n            <thead className=\"bg-gray-50\">\r\n              <tr>\r\n                {selectable && (\r\n                  <th className=\"w-12 px-6 py-3\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      className=\"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary\"\r\n                      onChange={handleSelectAll}\r\n                      checked={selectedRows.length === paginatedData.length && paginatedData.length > 0}\r\n                      data-testid={`${testId}-select-all`}\r\n                    />\r\n                  </th>\r\n                )}\r\n                {columns.map((column) => (\r\n                  <th\r\n                    key={column.key}\r\n                    className={`px-6 py-3 text-${column.align || 'left'} text-xs font-medium text-gray-500 uppercase tracking-wider ${column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''} transition-colors duration-200 ${column.width ? column.width : ''} ${column.className || ''}`}\r\n                    onClick={() => column.sortable && handleSort(column.key)}\r\n                    data-testid={`${testId}-column-${column.key}`}\r\n                  >\r\n                    <div className=\"flex items-center space-x-1\">\r\n                      <span>{column.label}</span>\r\n                      {column.sortable && (\r\n                        <span className={`transition-colors duration-200 ${\r\n                          sortConfig?.key === column.key ? 'text-primary' : 'text-gray-400'\r\n                        }`}>\r\n                          {sortConfig?.key === column.key && sortConfig.direction === 'asc'\r\n                            ? <ChevronUpIcon className=\"h-4 w-4\" />\r\n                            : sortConfig?.key === column.key && sortConfig.direction === 'desc'\r\n                              ? <ChevronDownIcon className=\"h-4 w-4\" />\r\n                              : <span className=\"text-gray-300\">↕</span>}\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                  </th>\r\n                ))}\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"bg-white divide-y divide-gray-100\">\r\n              {paginatedData.length > 0 ? (\r\n                paginatedData.map((row, index) => (\r\n                  <tr\r\n                    key={index}\r\n                    className={`group transition-all duration-200 ${\r\n                      onRowClick ? 'cursor-pointer' : ''\r\n                    } ${selectedRows.includes(index) ? 'bg-primary bg-opacity-5' : ''}\r\n                    ${hoveredRow === index ? 'bg-gray-50' : ''}\r\n                    ${rowClassName ? rowClassName(row, index) : ''}`}\r\n                    onClick={() => onRowClick && onRowClick(row)}\r\n                    onMouseEnter={() => setHoveredRow(index)}\r\n                    onMouseLeave={() => setHoveredRow(null)}\r\n                    data-testid={`${testId}-row-${index}`}\r\n                  >\r\n                    {selectable && (\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          className=\"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary\"\r\n                          checked={selectedRows.includes(index)}\r\n                          onChange={() => {}} // Empty handler to avoid React warning about controlled component\r\n                          onClick={(e) => handleRowSelect(index, e)}\r\n                          data-testid={`${testId}-row-${index}-checkbox`}\r\n                        />\r\n                      </td>\r\n                    )}\r\n                    {columns.map((column) => (\r\n                      <td\r\n                        key={column.key}\r\n                        className={`px-6 py-4 whitespace-nowrap text-sm text-gray-600 group-hover:text-gray-900 text-${column.align || 'left'} ${column.className || ''}`}\r\n                        data-testid={`${testId}-row-${index}-cell-${column.key}`}\r\n                      >\r\n                        {column.render\r\n                          ? column.render(row[column.key], row)\r\n                          : column.key.toLowerCase().includes('status')\r\n                            ? renderStatusBadge(row[column.key])\r\n                            : row[column.key]}\r\n                      </td>\r\n                    ))}\r\n                  </tr>\r\n                ))\r\n              ) : (\r\n                <tr>\r\n                  <td\r\n                    colSpan={columns.length + (selectable ? 1 : 0)}\r\n                    className=\"px-6 py-10 text-center text-gray-500\"\r\n                    data-testid={`${testId}-empty-message`}\r\n                  >\r\n                    {emptyMessage}\r\n                  </td>\r\n                </tr>\r\n              )}\r\n            </tbody>\r\n          </table>\r\n        )}\r\n      </div>\r\n\r\n      {/* Pagination */}\r\n      {pagination && totalPages > 1 && (\r\n        <div className={`px-6 py-4 border-t border-gray-100 flex items-center justify-between ${footerClassName}`}>\r\n          <div className=\"text-sm text-gray-500\">\r\n            Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, filteredData.length)} of {filteredData.length} entries\r\n          </div>\r\n          <div className=\"flex space-x-1\">\r\n            <button\r\n              onClick={() => handlePageChange(Math.max(1, currentPage - 1))}\r\n              disabled={currentPage === 1}\r\n              className={`px-3 py-1 rounded-md text-sm ${\r\n                currentPage === 1\r\n                  ? 'text-gray-400 cursor-not-allowed'\r\n                  : 'text-gray-700 hover:bg-gray-100'\r\n              }`}\r\n              data-testid={`${testId}-pagination-prev`}\r\n            >\r\n              Previous\r\n            </button>\r\n            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\r\n              // Show pages around current page\r\n              let pageNum: number;\r\n              if (totalPages <= 5) {\r\n                pageNum = i + 1;\r\n              } else if (currentPage <= 3) {\r\n                pageNum = i + 1;\r\n              } else if (currentPage >= totalPages - 2) {\r\n                pageNum = totalPages - 4 + i;\r\n              } else {\r\n                pageNum = currentPage - 2 + i;\r\n              }\r\n\r\n              return (\r\n                <button\r\n                  key={pageNum}\r\n                  onClick={() => handlePageChange(pageNum)}\r\n                  className={`px-3 py-1 rounded-md text-sm ${\r\n                    currentPage === pageNum\r\n                      ? 'bg-primary text-white'\r\n                      : 'text-gray-700 hover:bg-gray-100'\r\n                  }`}\r\n                  data-testid={`${testId}-pagination-${pageNum}`}\r\n                >\r\n                  {pageNum}\r\n                </button>\r\n              );\r\n            })}\r\n            <button\r\n              onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}\r\n              disabled={currentPage === totalPages}\r\n              className={`px-3 py-1 rounded-md text-sm ${\r\n                currentPage === totalPages\r\n                  ? 'text-gray-400 cursor-not-allowed'\r\n                  : 'text-gray-700 hover:bg-gray-100'\r\n              }`}\r\n              data-testid={`${testId}-pagination-next`}\r\n            >\r\n              Next\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default memo(DataTable) as typeof DataTable;\r\n\r\n\r\n\r\n\r\n", "/**\r\n * Modal Component\r\n * \r\n * A reusable modal dialog component.\r\n */\r\n\r\nimport React, { Fragment, useEffect, useRef, memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\nimport { XMarkIcon } from '@heroicons/react/24/outline';\r\nimport { createPortal } from 'react-dom';\r\n\r\nexport interface ModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title: string | ReactNode;\r\n  children: ReactNode;\r\n  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full';\r\n  footer?: ReactNode;\r\n  closeOnEsc?: boolean;\r\n  closeOnBackdropClick?: boolean;\r\n  showCloseButton?: boolean;\r\n  centered?: boolean;\r\n  className?: string;\r\n  bodyClassName?: string;\r\n  headerClassName?: string;\r\n  footerClassName?: string;\r\n  backdropClassName?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst Modal: React.FC<ModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  children,\r\n  size = 'md',\r\n  footer,\r\n  closeOnEsc = true,\r\n  closeOnBackdropClick = true,\r\n  showCloseButton = true,\r\n  centered = true,\r\n  className = '',\r\n  bodyClassName = '',\r\n  headerClassName = '',\r\n  footerClassName = '',\r\n  backdropClassName = '',\r\n  testId,\r\n}) => {\r\n  const modalRef = useRef<HTMLDivElement>(null);\r\n  \r\n  // Handle Escape key press\r\n  useEffect(() => {\r\n    const handleEscape = (e: KeyboardEvent) => {\r\n      if (closeOnEsc && e.key === 'Escape') {\r\n        onClose();\r\n      }\r\n    };\r\n\r\n    if (isOpen) {\r\n      document.addEventListener('keydown', handleEscape);\r\n      // Prevent scrolling on the body when modal is open\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener('keydown', handleEscape);\r\n      document.body.style.overflow = 'auto';\r\n    };\r\n  }, [isOpen, onClose, closeOnEsc]);\r\n  \r\n  // Focus trap inside modal\r\n  useEffect(() => {\r\n    if (!isOpen || !modalRef.current) return;\r\n    \r\n    const focusableElements = modalRef.current.querySelectorAll(\r\n      'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\r\n    );\r\n    \r\n    if (focusableElements.length === 0) return;\r\n    \r\n    const firstElement = focusableElements[0] as HTMLElement;\r\n    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;\r\n    \r\n    const handleTabKey = (e: KeyboardEvent) => {\r\n      if (e.key !== 'Tab') return;\r\n      \r\n      if (e.shiftKey) {\r\n        if (document.activeElement === firstElement) {\r\n          lastElement.focus();\r\n          e.preventDefault();\r\n        }\r\n      } else {\r\n        if (document.activeElement === lastElement) {\r\n          firstElement.focus();\r\n          e.preventDefault();\r\n        }\r\n      }\r\n    };\r\n    \r\n    document.addEventListener('keydown', handleTabKey);\r\n    firstElement.focus();\r\n    \r\n    return () => {\r\n      document.removeEventListener('keydown', handleTabKey);\r\n    };\r\n  }, [isOpen]);\r\n\r\n  if (!isOpen) return null;\r\n  \r\n  // Size classes\r\n  const sizeClasses = {\r\n    xs: 'max-w-xs',\r\n    sm: 'max-w-md',\r\n    md: 'max-w-lg',\r\n    lg: 'max-w-2xl',\r\n    xl: 'max-w-4xl',\r\n    full: 'max-w-full mx-4',\r\n  };\r\n  \r\n  // Modal content\r\n  const modalContent = (\r\n    <Fragment>\r\n      {/* Backdrop */}\r\n      <div \r\n        className={`fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity ${backdropClassName}`}\r\n        onClick={closeOnBackdropClick ? onClose : undefined}\r\n        data-testid={`${testId}-backdrop`}\r\n      />\r\n\r\n      {/* Modal */}\r\n      <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n        <div className={`flex min-h-full items-${centered ? 'center' : 'start'} justify-center p-4 text-center`}>\r\n          <div \r\n            ref={modalRef}\r\n            className={`${sizeClasses[size]} w-full transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all ${className}`}\r\n            onClick={(e) => e.stopPropagation()}\r\n            data-testid={testId}\r\n          >\r\n            {/* Header */}\r\n            <div className={`flex items-center justify-between px-6 py-4 border-b border-gray-100 ${headerClassName}`}>\r\n              {typeof title === 'string' ? (\r\n                <h3 className=\"text-lg font-semibold text-gray-800\">{title}</h3>\r\n              ) : (\r\n                title\r\n              )}\r\n              {showCloseButton && (\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary rounded-full p-1\"\r\n                  onClick={onClose}\r\n                  aria-label=\"Close modal\"\r\n                  data-testid={`${testId}-close-button`}\r\n                >\r\n                  <XMarkIcon className=\"h-6 w-6\" />\r\n                </button>\r\n              )}\r\n            </div>\r\n\r\n            {/* Content */}\r\n            <div className={`px-6 py-4 ${bodyClassName}`}>\r\n              {children}\r\n            </div>\r\n\r\n            {/* Footer */}\r\n            {footer && (\r\n              <div className={`px-6 py-4 bg-gray-50 border-t border-gray-100 flex justify-end space-x-3 ${footerClassName}`}>\r\n                {footer}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Fragment>\r\n  );\r\n  \r\n  // Use portal to render modal at the end of the document body\r\n  return createPortal(modalContent, document.body);\r\n};\r\n\r\nexport default memo(Modal);\r\n", "import * as React from \"react\";\nfunction CubeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m21 7.5-9-5.25L3 7.5m18 0-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CubeIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction PlusIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 4.5v15m7.5-7.5h-15\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PlusIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction EnvelopeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EnvelopeIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction TrashIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(TrashIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction EyeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EyeIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction PhoneIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PhoneIcon);\nexport default ForwardRef;"], "names": ["_ref", "children", "className", "_jsx", "tabs", "activeTab", "onChange", "map", "tab", "isActive", "id", "onClick", "disabled", "label", "<PERSON><PERSON><PERSON><PERSON>", "title", "description", "actions", "breadcrumbs", "testId", "_jsxs", "length", "Link", "to", "HomeIcon", "item", "index", "ChevronRightIcon", "path", "memo", "PencilIcon", "svgRef", "titleId", "props", "React", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "strokeLinecap", "strokeLinejoin", "d", "NoSymbolIcon", "MapPinIcon", "GlobeAltIcon", "supplier", "DetailSection", "Avatar", "logo", "src", "alt", "name", "size", "StatusBadge", "status", "type", "verificationStatus", "char<PERSON>t", "toUpperCase", "slice", "DetailList", "DetailItem", "value", "UserIcon", "<PERSON><PERSON><PERSON>", "EnvelopeIcon", "href", "email", "PhoneIcon", "phone", "address", "website", "target", "rel", "categories", "category", "CheckCircleIcon", "ClockIcon", "XCircleIcon", "getVerificationIcon", "getVerificationText", "products", "supplierId", "_supplierId", "onProductUpdate", "navigate", "useNavigate", "showSuccess", "showError", "showInfo", "useNotification", "selectedProduct", "setSelectedProduct", "useState", "isDeleteModalOpen", "setIsDeleteModalOpen", "getStatusBadgeStatus", "handleViewProduct", "product", "ROUTES", "getProductDetailsRoute", "columns", "key", "sortable", "render", "_value", "image", "CubeIcon", "formatCurrency", "stockStatus", "stock", "minimumStock", "text", "color", "getStockStatus", "<PERSON><PERSON>", "variant", "icon", "EyeIcon", "console", "log", "handleEditProduct", "handleDeleteProduct", "TrashIcon", "PlusIcon", "DataTable", "data", "onRowClick", "pagination", "pageSize", "emptyMessage", "Modal", "isOpen", "onClose", "footer", "_Fragment", "async", "error", "SupplierProfilePage", "useParams", "showErrorRef", "useRef", "showSuccessRef", "isMountedRef", "useEffect", "current", "setActiveTab", "setSupplier", "supplierProducts", "setSupplierProducts", "isLoading", "setIsLoading", "setError", "isDeleting", "setIsDeleting", "isBanModalOpen", "setIsBanModalOpen", "isBanning", "setIsBanning", "getSupplierById", "getSupplierProducts", "deleteSupplier", "banSupplier", "unbanSupplier", "hookLoading", "useSuppliers", "fetchSupplierData", "useCallback", "supplierData", "errorMessage", "Error", "message", "LoadingSpinner", "SUPPLIERS", "loading", "Tabs", "tabId", "SupplierPersonalInfo", "SupplierProducts", "formatDate", "dateString", "options", "arguments", "undefined", "date", "Date", "defaultOptions", "year", "month", "day", "Intl", "DateTimeFormat", "format", "amount", "currency", "locale", "NumberFormat", "style", "minimumFractionDigits", "maximumFractionDigits", "toFixed", "_type", "statusKey", "toLowerCase", "colorClass", "TruckIcon", "ExclamationCircleIcon", "formattedStatus", "MagnifyingGlassIcon", "ChevronUpIcon", "CONFIG", "DEFAULT_PAGE_SIZE", "selectable", "onSelectionChange", "headerClassName", "bodyClassName", "footerClassName", "rowClassName", "initialSortKey", "initialSortDirection", "sortConfig", "setSortConfig", "direction", "searchTerm", "setSearchTerm", "currentPage", "setCurrentPage", "selectedRows", "setSelectedRows", "hoveredRow", "setHoveredRow", "sortedData", "useMemo", "sort", "a", "b", "aValue", "bValue", "localeCompare", "filteredData", "filter", "row", "entries", "some", "_ref2", "_key", "String", "includes", "totalPages", "Math", "ceil", "paginatedData", "startIndex", "handlePageChange", "page", "renderStatusBadge", "bgColor", "statusLower", "placeholder", "e", "event", "newSelectedRows", "checked", "Array", "from", "_", "i", "selectedItems", "idx", "column", "align", "width", "handleSort", "ChevronDownIcon", "onMouseEnter", "onMouseLeave", "handleRowSelect", "stopPropagation", "indexOf", "splice", "push", "colSpan", "min", "max", "pageNum", "closeOnEsc", "closeOnBackdropClick", "showCloseButton", "centered", "backdropClassName", "modalRef", "handleEscape", "document", "addEventListener", "body", "overflow", "removeEventListener", "focusableElements", "querySelectorAll", "firstElement", "lastElement", "handleTabKey", "shift<PERSON>ey", "activeElement", "focus", "preventDefault", "modalContent", "Fragment", "xs", "sm", "md", "lg", "xl", "full", "XMarkIcon", "createPortal"], "sourceRoot": ""}