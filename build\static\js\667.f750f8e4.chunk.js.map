{"version": 3, "file": "static/js/667.f750f8e4.chunk.js", "mappings": "yNACA,SAASA,EAAUC,EAIhBC,GAAQ,IAJS,MAClBC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,gTAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBN,G,+DCPlD,MAAMmB,EAA0ClB,IAA2B,IAA1B,QAAEmB,EAAO,QAAEC,GAASpB,EAKnE,OACEqB,EAAAA,EAAAA,MAAA,OACED,QANgBE,KAClBF,EAAQD,EAAQL,GAAG,EAMjBS,UAAU,kGAAiGC,SAAA,EAG3GC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6FAA4FC,SACxGL,EAAQO,OACPD,EAAAA,EAAAA,KAAA,OACEE,IAAKR,EAAQO,MACbE,IAAKT,EAAQU,KACbN,UAAU,gCAGZE,EAAAA,EAAAA,KAACK,EAAAA,EAAQ,CAACP,UAAU,6BAKxBF,EAAAA,EAAAA,MAAA,OAAKE,UAAU,YAAWC,SAAA,EACxBH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6CAA6CrB,MAAOiB,EAAQU,KAAKL,SAC5EL,EAAQU,QAEXR,EAAAA,EAAAA,MAAA,KAAGE,UAAU,iCAAiCrB,MAAOiB,EAAQY,IAAIP,SAAA,CAAC,QAC1DL,EAAQY,WAIlBV,EAAAA,EAAAA,MAAA,OAAKE,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sCAAqCC,UACjDQ,EAAAA,EAAAA,IAAeb,EAAQc,UAE1BR,EAAAA,EAAAA,KAACS,EAAAA,EAAW,CAACC,OAAQhB,EAAQgB,aAG/Bd,EAAAA,EAAAA,MAAA,OAAKE,UAAU,wBAAuBC,SAAA,CAAC,UAC7BL,EAAQiB,cAGhB,EAIV,GAAeC,EAAAA,EAAAA,MAAKnB,G,wBCjEpB,SAASoB,EAAUtC,EAIhBC,GAAQ,IAJS,MAClBC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,wQAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBiC,G,cCvBlD,SAASC,EAAYvC,EAIlBC,GAAQ,IAJW,MACpBC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,iVAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBkC,G,cCGlD,MAAMC,EAAsExC,IAKrE,IALsE,YAC3EyC,EAAW,SACXC,EAAQ,mBACRC,EAAkB,eAClBC,GACD5C,EACC,MAAO6C,EAAYC,IAAiBC,EAAAA,EAAAA,WAAS,GAQvCC,EAAWP,EAAYO,UAAY,GAEzC,OACE3B,EAAAA,EAAAA,MAAA,OAAKE,UAAU,6CAA4CC,SAAA,EAEzDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0CAAyCC,UACtDH,EAAAA,EAAAA,MAAA,OAAKE,UAAU,oCAAmCC,SAAA,EAChDH,EAAAA,EAAAA,MAAA,OAAKE,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,UACEL,QAASA,IAAM0B,GAAeD,GAC9BtB,UAAU,wDAAuDC,SAEhEqB,GACCpB,EAAAA,EAAAA,KAACwB,EAAAA,EAAe,CAAC1B,UAAU,2BAE3BE,EAAAA,EAAAA,KAACyB,EAAAA,EAAgB,CAAC3B,UAAU,6BAIhCF,EAAAA,EAAAA,MAAA,OAAKE,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAACa,EAAU,CAACf,UAAU,2BACtBF,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4BAA2BC,SAAEiB,EAAYZ,QACvDJ,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wBAAuBC,SAAEiB,EAAYU,wBAKxD9B,EAAAA,EAAAA,MAAA,OAAKE,UAAU,8BAA6BC,SAAA,EAE1CC,EAAAA,EAAAA,KAACS,EAAAA,EAAW,CAACC,OAAQM,EAAYN,UAGjCd,EAAAA,EAAAA,MAAA,OAAKE,UAAU,wBAAuBC,SAAA,CACnCiB,EAAYW,aAAa,gBAI5B/B,EAAAA,EAAAA,MAAA,OAAKE,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,UACEL,QAASA,IAAMuB,EAAmBF,EAAY3B,GAAI,YAClDS,UAAW,yCACTkB,EAAYY,qBACR,iDACA,+CAENnD,OAAUuC,EAAYY,qBAAuB,YAAc,WAApD,gBAA6E7B,SAEnFiB,EAAYY,sBACX5B,EAAAA,EAAAA,KAAC6B,EAAAA,EAAO,CAAC/B,UAAU,aAEnBE,EAAAA,EAAAA,KAACc,EAAY,CAAChB,UAAU,eAI5BE,EAAAA,EAAAA,KAAA,UACEL,QAASA,IAAMuB,EAAmBF,EAAY3B,GAAI,YAClDS,UAAW,yCACTkB,EAAYc,qBACR,8CACA,+CAENrD,OAAUuC,EAAYc,qBAAuB,YAAc,WAApD,gBAA6E/B,SAEnFiB,EAAYc,sBACX9B,EAAAA,EAAAA,KAAC6B,EAAAA,EAAO,CAAC/B,UAAU,aAEnBE,EAAAA,EAAAA,KAACc,EAAY,CAAChB,UAAU,kBAM9BE,EAAAA,EAAAA,KAAC+B,EAAAA,EAAM,CACLC,QAAQ,UACRC,KAAK,KACLC,MAAMlC,EAAAA,EAAAA,KAACmC,EAAAA,EAAS,CAACrC,UAAU,YAC3BH,QAnFSyC,KACfC,OAAOC,QAAQ,oDAAoDtB,EAAYZ,yDACjFa,EAASD,EAAY3B,GACvB,EAiFUS,UAAU,wDAAuDC,SAClE,mBAQNqB,IACCpB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,MAAKC,SACG,IAApBwB,EAASgB,QACRvC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iCAAgCC,UAC7CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,UAASC,SAAC,uCAG3BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sEAAqEC,SACjFwB,EAASiB,KAAK9C,IACbM,EAAAA,EAAAA,KAACP,EAAW,CAEVC,QAASA,EACTC,QAASwB,GAFJzB,EAAQL,YASrB,EAIV,GAAeuB,EAAAA,EAAAA,MAAKG,G,aCvHpB,MAAM0B,EAAgElE,IAQ/D,IARgE,SACrEmE,EAAQ,SACRzB,EAAQ,oBACR0B,EAAmB,mBACnBzB,EAAkB,8BAClB0B,EAA6B,eAC7BzB,EAAc,iBACd0B,GACDtE,EACC,MAAO6C,EAAYC,IAAiBC,EAAAA,EAAAA,WAAS,GAQvCwB,EAAgBJ,EAASI,eAAiB,GAEhD,OACElD,EAAAA,EAAAA,MAACmD,EAAAA,EAAI,CAACjD,UAAU,kBAAiBC,SAAA,EAE/BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0CAAyCC,UACtDH,EAAAA,EAAAA,MAAA,OAAKE,UAAU,oCAAmCC,SAAA,EAChDH,EAAAA,EAAAA,MAAA,OAAKE,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,UACEL,QAASA,IAAM0B,GAAeD,GAC9BtB,UAAU,wDAAuDC,SAEhEqB,GACCpB,EAAAA,EAAAA,KAACwB,EAAAA,EAAe,CAAC1B,UAAU,2BAE3BE,EAAAA,EAAAA,KAACyB,EAAAA,EAAgB,CAAC3B,UAAU,6BAIhCF,EAAAA,EAAAA,MAAA,OAAKE,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAACgD,EAAAA,EAAO,CAAClD,UAAU,0BACnBF,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sCAAqCC,SAAE2C,EAAStC,QAC9DJ,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wBAAuBC,SAAE2C,EAAShB,wBAKrD9B,EAAAA,EAAAA,MAAA,OAAKE,UAAU,8BAA6BC,SAAA,EAE1CC,EAAAA,EAAAA,KAACS,EAAAA,EAAW,CAACC,OAAQgC,EAAShC,UAG9Bd,EAAAA,EAAAA,MAAA,OAAKE,UAAU,wBAAuBC,SAAA,EACpCH,EAAAA,EAAAA,MAAA,QAAAG,SAAA,CAAO2C,EAASO,iBAAiB,qBACjCjD,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAC,YACvBH,EAAAA,EAAAA,MAAA,QAAAG,SAAA,CAAO2C,EAASf,aAAa,mBAI/B/B,EAAAA,EAAAA,MAAA,OAAKE,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,UACEL,QAASA,IAAMuB,EAAmBwB,EAASrD,GAAI,YAC/CS,UAAW,uCACT4C,EAASd,qBACL,iDACA,+CAENnD,OAAUiE,EAASd,qBAAuB,YAAc,WAAjD,gBAA0E7B,SAEhF2C,EAASd,sBACR5B,EAAAA,EAAAA,KAAC6B,EAAAA,EAAO,CAAC/B,UAAU,aAEnBE,EAAAA,EAAAA,KAACc,EAAY,CAAChB,UAAU,eAI5BE,EAAAA,EAAAA,KAAA,UACEL,QAASA,IAAMuB,EAAmBwB,EAASrD,GAAI,YAC/CS,UAAW,uCACT4C,EAASZ,qBACL,8CACA,+CAENrD,OAAUiE,EAASZ,qBAAuB,YAAc,WAAjD,gBAA0E/B,SAEhF2C,EAASZ,sBACR9B,EAAAA,EAAAA,KAAC6B,EAAAA,EAAO,CAAC/B,UAAU,aAEnBE,EAAAA,EAAAA,KAACc,EAAY,CAAChB,UAAU,kBAM9BF,EAAAA,EAAAA,MAAA,OAAKE,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAC+B,EAAAA,EAAM,CACLC,QAAQ,UACRC,KAAK,KACLC,MAAMlC,EAAAA,EAAAA,KAACkD,EAAAA,EAAQ,CAACpD,UAAU,YAC1BH,QAASA,IAAMkD,EAAiBH,GAAU3C,SAC3C,qBAIDC,EAAAA,EAAAA,KAAC+B,EAAAA,EAAM,CACLC,QAAQ,UACRC,KAAK,KACLC,MAAMlC,EAAAA,EAAAA,KAACmC,EAAAA,EAAS,CAACrC,UAAU,YAC3BH,QA/FOyC,KACfC,OAAOC,QAAQ,iDAAiDI,EAAStC,2EAC3Ea,EAASyB,EAASrD,GACpB,EA6FYS,UAAU,8CAA6CC,SACxD,sBASRqB,IACCpB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,MAAKC,SACQ,IAAzB+C,EAAcP,QACb3C,EAAAA,EAAAA,MAAA,OAAKE,UAAU,iCAAgCC,SAAA,EAC7CC,EAAAA,EAAAA,KAACgD,EAAAA,EAAO,CAAClD,UAAU,0CACnBE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2BAA0BC,SAAC,0BACxCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,UAASC,SAAC,iEACvBC,EAAAA,EAAAA,KAAC+B,EAAAA,EAAM,CACLC,QAAQ,UACRC,KAAK,KACLC,MAAMlC,EAAAA,EAAAA,KAACkD,EAAAA,EAAQ,CAACpD,UAAU,YAC1BH,QAASA,IAAMkD,EAAiBH,GAChC5C,UAAU,OAAMC,SACjB,8BAKHC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB+C,EAAcN,KAAKxB,IAClBhB,EAAAA,EAAAA,KAACe,EAAyB,CAExBC,YAAaA,EACbC,SAAU0B,EACVzB,mBAAoB0B,EACpBzB,eAAgBA,GAJXH,EAAY3B,YAWxB,EAIX,GAAeuB,EAAAA,EAAAA,MAAK6B,GCrKdU,EAA8D5E,IAQ7D,IAR8D,WACnE6E,EAAU,iBACVC,EAAgB,oBAChBV,EAAmB,2BACnBW,EAA0B,8BAC1BV,EAA6B,eAC7BzB,EAAc,iBACd0B,GACDtE,EACC,OAA0B,IAAtB6E,EAAWb,QAEX3C,EAAAA,EAAAA,MAAA,OAAKE,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6BAA4BC,SAAC,yBAC5CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wBAAuBC,SAAC,gFAQ3CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBqD,EAAWZ,KAAKE,IACf1C,EAAAA,EAAAA,KAACyC,EAAsB,CAErBC,SAAUA,EACVzB,SAAUoC,EACVV,oBAAqBA,EACrBzB,mBAAoBoC,EACpBV,8BAA+BA,EAC/BzB,eAAgBA,EAChB0B,iBAAkBA,GAPbH,EAASrD,OAUd,EAIV,GAAeuB,EAAAA,EAAAA,MAAKuC,G,cCxCpB,MAuJA,EAvJ8D5E,IAKvD,IALwD,WAC7DgF,EAAU,SACVC,EAAQ,SACRC,EAAQ,UACRC,GAAY,GACbnF,EACC,MAAOoF,EAAUC,IAAetC,EAAAA,EAAAA,UAA8B,CAC5DlB,KAAM,GACNsB,YAAa,GACbhB,OAAQ,SACR6C,aACA3B,sBAAsB,EACtBE,sBAAsB,KAGjB+B,EAAQC,IAAaxC,EAAAA,EAAAA,UAAiC,CAAC,GAExDyC,EAAgBC,IACpB,MAAM,KAAE5D,EAAI,MAAE6D,GAAUD,EAAEE,OAC1BN,GAAYO,IAAI,IAAUA,EAAM,CAAC/D,GAAO6D,MAGpCJ,EAAOzD,IACT0D,GAAUK,IAAI,IAAUA,EAAM,CAAC/D,GAAO,MACxC,EAwBF,OACER,EAAAA,EAAAA,MAAA,QAAM4D,SATcQ,IACpBA,EAAEI,iBAdqBC,MACvB,MAAMC,GAAmBC,EAAAA,EAAAA,GAAa,CACpCnE,KAAMuD,EAASvD,KACfsB,YAAaiC,EAASjC,aACrB,CACDtB,KAAM,CAACoE,EAAAA,GAAgBC,SAAS,iCAChC/C,YAAa,CAAC8C,EAAAA,GAAgBC,SAAS,8BAIzC,OADAX,EAAUQ,GACsC,IAAzCzF,OAAO6F,KAAKJ,GAAkB/B,MAAY,EAM7C8B,IACFb,EAASG,EACX,EAI8B7D,UAAU,YAAWC,SAAA,EACjDH,EAAAA,EAAAA,MAAA,OAAKE,UAAU,YAAWC,SAAA,EACxBH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEH,EAAAA,EAAAA,MAAA,SAAO+E,QAAQ,OAAO7E,UAAU,0CAAyCC,SAAA,CAAC,qBACvDC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAElDC,EAAAA,EAAAA,KAAA,SACE4E,KAAK,OACLvF,GAAG,OACHe,KAAK,OACL6D,MAAON,EAASvD,KAChByE,SAAUd,EACVjE,UAAW,8GACT+D,EAAOzD,KAAO,iBAAmB,IAEnC0E,YAAY,2BAEbjB,EAAOzD,OAAQJ,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAE8D,EAAOzD,WAGnER,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEH,EAAAA,EAAAA,MAAA,SAAO+E,QAAQ,cAAc7E,UAAU,0CAAyCC,SAAA,CAAC,gBACnEC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAE7CC,EAAAA,EAAAA,KAAA,YACEX,GAAG,cACHe,KAAK,cACL2E,KAAM,EACNd,MAAON,EAASjC,YAChBmD,SAAUd,EACVjE,UAAW,8GACT+D,EAAOnC,YAAc,iBAAmB,IAE1CoD,YAAY,kCAEbjB,EAAOnC,cAAe1B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAE8D,EAAOnC,kBAG1E9B,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAO2E,QAAQ,SAAS7E,UAAU,0CAAyCC,SAAC,YAG5EH,EAAAA,EAAAA,MAAA,UACEP,GAAG,SACHe,KAAK,SACL6D,MAAON,EAASjD,OAChBmE,SAAUd,EACVjE,UAAU,4GAA2GC,SAAA,EAErHC,EAAAA,EAAAA,KAAA,UAAQiE,MAAM,SAAQlE,SAAC,YACvBC,EAAAA,EAAAA,KAAA,UAAQiE,MAAM,WAAUlE,SAAC,oBAI7BH,EAAAA,EAAAA,MAAA,OAAKE,UAAU,yBAAwBC,SAAA,EACrCC,EAAAA,EAAAA,KAAA,OAAAD,UACEH,EAAAA,EAAAA,MAAA,SAAOE,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACE4E,KAAK,WACLxE,KAAK,uBACL4E,QAASrB,EAAS/B,qBAClBiD,SAAWb,GAAMJ,GAAYO,IAAI,IAAUA,EAAMvC,qBAAsBoC,EAAEE,OAAOc,YAChFlF,UAAU,6DAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,6BAA4BC,SAAC,kCAGjDC,EAAAA,EAAAA,KAAA,OAAAD,UACEH,EAAAA,EAAAA,MAAA,SAAOE,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACE4E,KAAK,WACLxE,KAAK,uBACL4E,QAASrB,EAAS7B,qBAClB+C,SAAWb,GAAMJ,GAAYO,IAAI,IAAUA,EAAMrC,qBAAsBkC,EAAEE,OAAOc,YAChFlF,UAAU,6DAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,6BAA4BC,SAAC,wCAMrDH,EAAAA,EAAAA,MAAA,OAAKE,UAAU,6BAA4BC,SAAA,EACzCC,EAAAA,EAAAA,KAAC+B,EAAAA,EAAM,CACL6C,KAAK,SACL5C,QAAQ,UACRrC,QAAS8D,EACTwB,SAAUvB,EAAU3D,SACrB,YAGDC,EAAAA,EAAAA,KAAC+B,EAAAA,EAAM,CACL6C,KAAK,SACLM,QAASxB,EAAU3D,SACpB,yBAIE,E,uBC1IX,MAAMoF,EAAmCA,KACvC,MAAMC,GAAWC,EAAAA,EAAAA,OACX,WAAEjC,EAAU,UAAEM,EAAS,gBAAE4B,IAAoBC,EAAAA,EAAAA,OAC7C,iBAAEC,IAAqBC,EAAAA,EAAAA,MAGtBC,EAAwBC,IAA6BrE,EAAAA,EAAAA,WAAS,IAC9DsE,EAA2BC,IAAgCvE,EAAAA,EAAAA,WAAS,IACpEwE,EAAgCC,IAAqCzE,EAAAA,EAAAA,UAA0B,OAG/F0E,EAAcC,IAAmB3E,EAAAA,EAAAA,UAAwC,QACzE4E,EAAkBC,IAAuB7E,EAAAA,EAAAA,UAA0C,OAGpF8E,GAAqBC,EAAAA,EAAAA,UAAQ,IAC1BjD,EAAWkD,QAAO5D,IAEF,QAAjBsD,GAA0BtD,EAAShC,SAAWsF,OAKzB,aAArBE,IAAoCxD,EAASd,yBAGxB,aAArBsE,IAAoCxD,EAASZ,0BAMlD,CAACsB,EAAY4C,EAAcE,IAGxBK,GAAoBC,EAAAA,EAAAA,cAAYC,UACpC,IAEEC,QAAQC,IAAI,mBAAoBC,GAChCjB,GAA0B,SACpBL,IACNE,EAAiB,CACfZ,KAAM,UACNnG,MAAO,UACPoI,QAAS,+BAEb,CAAE,MAAOC,GACPtB,EAAiB,CACfZ,KAAM,QACNnG,MAAO,QACPoI,QAAS,0BAEb,IACC,CAACvB,EAAiBE,IAEfuB,GAAuBP,EAAAA,EAAAA,cAAYC,UACvC,IAEEC,QAAQC,IAAI,sBAAuBK,GACnCnB,GAA6B,GAC7BE,EAAkC,YAC5BT,IACNE,EAAiB,CACfZ,KAAM,UACNnG,MAAO,UACPoI,QAAS,kCAEb,CAAE,MAAOC,GACPtB,EAAiB,CACfZ,KAAM,QACNnG,MAAO,QACPoI,QAAS,6BAEb,IACC,CAACvB,EAAiBE,IAEfyB,GAAuBT,EAAAA,EAAAA,cAAYC,UACvC,IAEEC,QAAQC,IAAI,qBAAsBpD,SAC5B+B,IACNE,EAAiB,CACfZ,KAAM,UACNnG,MAAO,UACPoI,QAAS,iCAEb,CAAE,MAAOC,GACPtB,EAAiB,CACfZ,KAAM,QACNnG,MAAO,QACPoI,QAAS,6BAEb,IACC,CAACvB,EAAiBE,IAEf0B,GAA0BV,EAAAA,EAAAA,cAAYC,UAC1C,IAEEC,QAAQC,IAAI,wBAAyBQ,SAC/B7B,IACNE,EAAiB,CACfZ,KAAM,UACNnG,MAAO,UACPoI,QAAS,oCAEb,CAAE,MAAOC,GACPtB,EAAiB,CACfZ,KAAM,QACNnG,MAAO,QACPoI,QAAS,gCAEb,IACC,CAACvB,EAAiBE,IAEf4B,GAAiCZ,EAAAA,EAAAA,cAAYC,MAAOlD,EAAoB8D,KAC5E,IAEEX,QAAQC,IAAI,gCAAiCpD,EAAY8D,SACnD/B,IACNE,EAAiB,CACfZ,KAAM,UACNnG,MAAO,UACPoI,QAAS,+BAEb,CAAE,MAAOC,GACPtB,EAAiB,CACfZ,KAAM,QACNnG,MAAO,QACPoI,QAAS,wCAEb,IACC,CAACvB,EAAiBE,IAEf8B,GAAoCd,EAAAA,EAAAA,cAAYC,MAAOU,EAAuBE,KAClF,IAEEX,QAAQC,IAAI,mCAAoCQ,EAAeE,SACzD/B,IACNE,EAAiB,CACfZ,KAAM,UACNnG,MAAO,UACPoI,QAAS,kCAEb,CAAE,MAAOC,GACPtB,EAAiB,CACfZ,KAAM,QACNnG,MAAO,QACPoI,QAAS,2CAEb,IACC,CAACvB,EAAiBE,IAEf+B,GAAqBf,EAAAA,EAAAA,cAAagB,IACtCpC,EAASqC,EAAAA,EAAOC,uBAAuBF,GAAW,GACjD,CAACpC,IAEEuC,GAA8BnB,EAAAA,EAAAA,cAAa9D,IAC/CqD,EAAkCrD,GAClCmD,GAA6B,EAAK,GACjC,IAEH,OAAInC,GAEA1D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,UACpDC,EAAAA,EAAAA,KAAC4H,EAAAA,EAAc,CAAC3F,KAAK,UAMzBrC,EAAAA,EAAAA,MAAA,OAAKE,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAC6H,EAAAA,EAAU,CACTpJ,MAAM,sBACNiD,YAAY,uFACZoG,SACElI,EAAAA,EAAAA,MAAA,OAAKE,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,KAAC+B,EAAAA,EAAM,CACLC,QAAQ,UACRE,MAAMlC,EAAAA,EAAAA,KAAC1B,EAAU,CAACwB,UAAU,YAC5BH,QAASA,OAA2CI,SACrD,aAGDC,EAAAA,EAAAA,KAAC+B,EAAAA,EAAM,CACLG,MAAMlC,EAAAA,EAAAA,KAACkD,EAAAA,EAAQ,CAACpD,UAAU,YAC1BH,QAASA,IAAMgG,GAA0B,GAAM5F,SAChD,uBAQPC,EAAAA,EAAAA,KAAC+C,EAAAA,EAAI,CAAAhD,UACHH,EAAAA,EAAAA,MAAA,OAAKE,UAAU,2BAA0BC,SAAA,EACvCH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,+CAA8CC,SAAC,YAChEH,EAAAA,EAAAA,MAAA,UACEqE,MAAO+B,EACPnB,SAAWb,GAAMiC,EAAgBjC,EAAEE,OAAOD,OAC1CnE,UAAU,0FAAyFC,SAAA,EAEnGC,EAAAA,EAAAA,KAAA,UAAQiE,MAAM,MAAKlE,SAAC,gBACpBC,EAAAA,EAAAA,KAAA,UAAQiE,MAAM,SAAQlE,SAAC,YACvBC,EAAAA,EAAAA,KAAA,UAAQiE,MAAM,WAAUlE,SAAC,oBAG7BH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,+CAA8CC,SAAC,gBAChEH,EAAAA,EAAAA,MAAA,UACEqE,MAAOiC,EACPrB,SAAWb,GAAMmC,EAAoBnC,EAAEE,OAAOD,OAC9CnE,UAAU,0FAAyFC,SAAA,EAEnGC,EAAAA,EAAAA,KAAA,UAAQiE,MAAM,MAAKlE,SAAC,cACpBC,EAAAA,EAAAA,KAAA,UAAQiE,MAAM,WAAUlE,SAAC,uBACzBC,EAAAA,EAAAA,KAAA,UAAQiE,MAAM,WAAUlE,SAAC,kCAOjCC,EAAAA,EAAAA,KAACmD,EAAqB,CACpBC,WAAYgD,EACZ/C,iBAAkB4D,EAClBtE,oBAAqBuE,EACrB5D,2BAA4B8D,EAC5BxE,8BAA+B0E,EAC/BnG,eAAgBoG,EAChB1E,iBAAkB8E,KAIpB3H,EAAAA,EAAAA,KAAC+H,EAAAA,EAAK,CACJC,OAAQtC,EACRuC,QAASA,IAAMtC,GAA0B,GACzClH,MAAM,eAAcsB,UAEpBC,EAAAA,EAAAA,KAACkI,EAAAA,GAAe,CACd1E,SAAU+C,EACV9C,SAAUA,IAAMkC,GAA0B,QAK9C3F,EAAAA,EAAAA,KAAC+H,EAAAA,EAAK,CACJC,OAAQpC,EACRqC,QAASA,KACPpC,GAA6B,GAC7BE,EAAkC,KAAK,EAEzCtH,MAAO,uBAAoD,OAA9BqH,QAA8B,IAA9BA,OAA8B,EAA9BA,EAAgC1F,OAAQ,KAAKL,SAEzE+F,IACC9F,EAAAA,EAAAA,KAACmI,EAAkB,CACjB5E,WAAYuC,EAA+BzG,GAC3CmE,SAAUuD,EACVtD,SAAUA,KACRoC,GAA6B,GAC7BE,EAAkC,KAAK,QAK3C,EAIV,GAAenF,EAAAA,EAAAA,MAAKuE,E,4CChSb,MAAMiD,EAAa,SAACC,GAA0E,IAAtDC,EAAmCC,UAAAhG,OAAA,QAAAiG,IAAAD,UAAA,GAAAA,UAAA,GAAG,CAAC,EACpF,IAAKF,EAAY,MAAO,IAExB,IACE,MAAMI,EAAO,IAAIC,KAAKL,GAGhBM,EAA6C,CACjDC,KAAM,UACNC,MAAO,QACPC,IAAK,aACFR,GAGL,OAAO,IAAIS,KAAKC,eAAe,QAASL,GAAgBM,OAAOR,EACjE,CAAE,MAAO3B,GAEP,OADAJ,QAAQI,MAAM,yBAA0BA,GACjCuB,CACT,CACF,EAkBa9H,EAAiB,SAC5B2I,GAGY,IAFZC,EAAgBZ,UAAAhG,OAAA,QAAAiG,IAAAD,UAAA,GAAAA,UAAA,GAAG,MACnBa,EAAcb,UAAAhG,OAAA,QAAAiG,IAAAD,UAAA,GAAAA,UAAA,GAAG,QAEjB,IACE,OAAO,IAAIQ,KAAKM,aAAaD,EAAQ,CACnCE,MAAO,WACPH,WACAI,sBAAuB,EACvBC,sBAAuB,IACtBP,OAAOC,EACZ,CAAE,MAAOpC,GAEP,OADAJ,QAAQI,MAAM,6BAA8BA,GACrC,GAAGqC,KAAYD,EAAOO,QAAQ,IACvC,CACF,C,yGC7CA,MAiDA,EAjDgDlL,IAIzC,IAJ0C,OAC/CmC,EACAkE,KAAM8E,EAAQ,OAAM,UACpB5J,EAAY,IACbvB,EAEC,IAAKmC,EACH,OACEV,EAAAA,EAAAA,KAAA,QAAMF,UAAW,qGAAqGA,IAAYC,SAAC,YAMvI,MAAM4J,EAAYjJ,EAAOkJ,cACzB,IAAIC,EAAa,GACb3H,EAAO,KAGO,WAAdyH,GAAwC,aAAdA,GAA0C,cAAdA,GACxDE,EAAa,8BACb3H,GAAOlC,EAAAA,EAAAA,KAAC8J,EAAAA,EAAe,CAAChK,UAAU,kBACX,YAAd6J,GAAyC,eAAdA,GACpCE,EAAa,4BACb3H,GAAOlC,EAAAA,EAAAA,KAAC+J,EAAAA,EAAS,CAACjK,UAAU,kBACL,WAAd6J,GAAwC,aAAdA,GACnCE,EAAa,0BACb3H,GAAOlC,EAAAA,EAAAA,KAACgK,EAAAA,EAAW,CAAClK,UAAU,kBACP,YAAd6J,GACTE,EAAa,gCACb3H,GAAOlC,EAAAA,EAAAA,KAACiK,EAAAA,EAAS,CAACnK,UAAU,kBACL,YAAd6J,GACTE,EAAa,gCACb3H,GAAOlC,EAAAA,EAAAA,KAACkK,EAAAA,EAAqB,CAACpK,UAAU,kBAExC+J,EAAa,4BAIf,MAAMM,EAAkBzJ,EAASA,EAAO0J,OAAO,GAAGC,cAAgB3J,EAAO4J,MAAM,GAAK,UAEpF,OACE1K,EAAAA,EAAAA,MAAA,QAAME,UAAW,2EAA2E+J,KAAc/J,IAAYC,SAAA,CACnHmC,EACAiI,IACI,C,gDC7DX,SAASF,EAAS1L,EAIfC,GAAQ,IAJQ,MACjBC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,saAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBqL,E,gDCvBlD,SAASF,EAASxL,EAIfC,GAAQ,IAJQ,MACjBC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,qDAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBmL,E,gDCvBlD,SAAS1J,EAAQ9B,EAIdC,GAAQ,IAJO,MAChBC,EAAK,QACLC,KACGC,GACJJ,EACC,OAAoBK,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,wFAEP,CACA,MACA,EADiCZ,EAAAA,WAAiByB,E", "sources": ["../node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js", "components/categories/ProductCard.tsx", "../node_modules/@heroicons/react/24/outline/esm/FolderIcon.js", "../node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js", "components/categories/SubcategoryManagementCard.tsx", "components/categories/CategoryManagementCard.tsx", "components/categories/CategoryHierarchyView.tsx", "components/categories/AddSubcategoryForm.tsx", "pages/CategoryManagementPage.tsx", "utils/formatters.ts", "components/common/StatusBadge.tsx", "../node_modules/@heroicons/react/24/outline/esm/TruckIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ClockIcon.js", "../node_modules/@heroicons/react/24/outline/esm/CubeIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction FunnelIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(FunnelIcon);\nexport default ForwardRef;", "/**\n * Product Card Component\n *\n * Displays a product within the category hierarchy\n */\n\nimport React, { memo } from 'react';\nimport StatusBadge from '../common/StatusBadge';\nimport { formatCurrency } from '../../utils/formatters';\nimport { CubeIcon } from '@heroicons/react/24/outline';\nimport type { Product } from '../../features/categories/types';\n\ninterface ProductCardProps {\n  product: Product;\n  onClick: (productId: string) => void;\n}\n\nconst ProductCard: React.FC<ProductCardProps> = ({ product, onClick }) => {\n  const handleClick = () => {\n    onClick(product.id);\n  };\n\n  return (\n    <div\n      onClick={handleClick}\n      className=\"border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer bg-white\"\n    >\n      {/* Product Image */}\n      <div className=\"aspect-square bg-gray-100 rounded-md mb-3 flex items-center justify-center overflow-hidden\">\n        {product.image ? (\n          <img\n            src={product.image}\n            alt={product.name}\n            className=\"w-full h-full object-cover\"\n          />\n        ) : (\n          <CubeIcon className=\"h-8 w-8 text-gray-400\" />\n        )}\n      </div>\n\n      {/* Product Info */}\n      <div className=\"space-y-2\">\n        <div>\n          <h5 className=\"font-medium text-gray-900 text-sm truncate\" title={product.name}>\n            {product.name}\n          </h5>\n          <p className=\"text-xs text-gray-500 truncate\" title={product.sku}>\n            SKU: {product.sku}\n          </p>\n        </div>\n\n        <div className=\"flex items-center justify-between\">\n          <div className=\"text-sm font-semibold text-gray-900\">\n            {formatCurrency(product.price)}\n          </div>\n          <StatusBadge status={product.status} />\n        </div>\n\n        <div className=\"text-xs text-gray-500\">\n          Stock: {product.stock}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default memo(ProductCard);\n", "import * as React from \"react\";\nfunction FolderIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.25 12.75V12A2.25 2.25 0 0 1 4.5 9.75h15A2.25 2.25 0 0 1 21.75 12v.75m-8.69-6.44-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(FolderIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction EyeSlashIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EyeSlashIcon);\nexport default ForwardRef;", "/**\n * Subcategory Management Card Component\n *\n * Displays a subcategory with its products and management options\n */\n\nimport React, { useState, memo } from 'react';\nimport StatusBadge from '../common/StatusBadge';\nimport Button from '../common/Button';\nimport ProductCard from './ProductCard';\nimport {\n  TrashIcon,\n  ChevronDownIcon,\n  ChevronRightIcon,\n  EyeIcon,\n  EyeSlashIcon,\n  FolderIcon\n} from '@heroicons/react/24/outline';\nimport type { Subcategory } from '../../features/categories/types';\n\ninterface SubcategoryManagementCardProps {\n  subcategory: Subcategory;\n  onDelete: (subcategoryId: string) => void;\n  onToggleVisibility: (subcategoryId: string, app: 'supplier' | 'customer') => void;\n  onProductClick: (productId: string) => void;\n}\n\nconst SubcategoryManagementCard: React.FC<SubcategoryManagementCardProps> = ({\n  subcategory,\n  onDelete,\n  onToggleVisibility,\n  onProductClick\n}) => {\n  const [isExpanded, setIsExpanded] = useState(false);\n\n  const handleDelete = () => {\n    if (window.confirm(`Are you sure you want to delete the subcategory \"${subcategory.name}\"? This will also delete all products within it.`)) {\n      onDelete(subcategory.id);\n    }\n  };\n\n  const products = subcategory.products || [];\n\n  return (\n    <div className=\"border border-gray-200 rounded-lg bg-white\">\n      {/* Subcategory Header */}\n      <div className=\"p-4 border-b border-gray-100 bg-gray-25\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <button\n              onClick={() => setIsExpanded(!isExpanded)}\n              className=\"p-1 rounded-full hover:bg-gray-200 focus:outline-none\"\n            >\n              {isExpanded ? (\n                <ChevronDownIcon className=\"h-4 w-4 text-gray-500\" />\n              ) : (\n                <ChevronRightIcon className=\"h-4 w-4 text-gray-500\" />\n              )}\n            </button>\n            \n            <div className=\"flex items-center space-x-2\">\n              <FolderIcon className=\"h-5 w-5 text-blue-500\" />\n              <div>\n                <h4 className=\"font-medium text-gray-900\">{subcategory.name}</h4>\n                <p className=\"text-sm text-gray-600\">{subcategory.description}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-3\">\n            {/* Status Badge */}\n            <StatusBadge status={subcategory.status} />\n            \n            {/* Product Count */}\n            <div className=\"text-sm text-gray-500\">\n              {subcategory.productCount} products\n            </div>\n\n            {/* Visibility Controls */}\n            <div className=\"flex items-center space-x-1\">\n              <button\n                onClick={() => onToggleVisibility(subcategory.id, 'supplier')}\n                className={`p-1.5 rounded-full transition-colors ${\n                  subcategory.visibleInSupplierApp\n                    ? 'text-green-600 bg-green-100 hover:bg-green-200'\n                    : 'text-gray-400 bg-gray-100 hover:bg-gray-200'\n                }`}\n                title={`${subcategory.visibleInSupplierApp ? 'Hide from' : 'Show in'} Supplier App`}\n              >\n                {subcategory.visibleInSupplierApp ? (\n                  <EyeIcon className=\"h-3 w-3\" />\n                ) : (\n                  <EyeSlashIcon className=\"h-3 w-3\" />\n                )}\n              </button>\n              \n              <button\n                onClick={() => onToggleVisibility(subcategory.id, 'customer')}\n                className={`p-1.5 rounded-full transition-colors ${\n                  subcategory.visibleInCustomerApp\n                    ? 'text-blue-600 bg-blue-100 hover:bg-blue-200'\n                    : 'text-gray-400 bg-gray-100 hover:bg-gray-200'\n                }`}\n                title={`${subcategory.visibleInCustomerApp ? 'Hide from' : 'Show in'} Customer App`}\n              >\n                {subcategory.visibleInCustomerApp ? (\n                  <EyeIcon className=\"h-3 w-3\" />\n                ) : (\n                  <EyeSlashIcon className=\"h-3 w-3\" />\n                )}\n              </button>\n            </div>\n\n            {/* Delete Button */}\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              icon={<TrashIcon className=\"h-3 w-3\" />}\n              onClick={handleDelete}\n              className=\"text-red-600 border-red-300 hover:bg-red-50 px-2 py-1\"\n            >\n              Delete\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Products */}\n      {isExpanded && (\n        <div className=\"p-4\">\n          {products.length === 0 ? (\n            <div className=\"text-center py-6 text-gray-500\">\n              <div className=\"text-sm\">No products in this subcategory</div>\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4\">\n              {products.map((product) => (\n                <ProductCard\n                  key={product.id}\n                  product={product}\n                  onClick={onProductClick}\n                />\n              ))}\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default memo(SubcategoryManagementCard);\n", "/**\n * Category Management Card Component\n *\n * Displays a category with its subcategories and management options\n */\n\nimport React, { useState, memo } from 'react';\nimport Card from '../common/Card';\nimport Button from '../common/Button';\nimport StatusBadge from '../common/StatusBadge';\nimport SubcategoryManagementCard from './SubcategoryManagementCard';\nimport {\n  TrashIcon,\n  PlusIcon,\n  ChevronDownIcon,\n  ChevronRightIcon,\n  EyeIcon,\n  EyeSlashIcon,\n  TagIcon\n} from '@heroicons/react/24/outline';\nimport type { Category } from '../../features/categories/types';\n\ninterface CategoryManagementCardProps {\n  category: Category;\n  onDelete: (categoryId: string) => void;\n  onDeleteSubcategory: (subcategoryId: string) => void;\n  onToggleVisibility: (categoryId: string, app: 'supplier' | 'customer') => void;\n  onToggleSubcategoryVisibility: (subcategoryId: string, app: 'supplier' | 'customer') => void;\n  onProductClick: (productId: string) => void;\n  onAddSubcategory: (category: Category) => void;\n}\n\nconst CategoryManagementCard: React.FC<CategoryManagementCardProps> = ({\n  category,\n  onDelete,\n  onDeleteSubcategory,\n  onToggleVisibility,\n  onToggleSubcategoryVisibility,\n  onProductClick,\n  onAddSubcategory\n}) => {\n  const [isExpanded, setIsExpanded] = useState(true);\n\n  const handleDelete = () => {\n    if (window.confirm(`Are you sure you want to delete the category \"${category.name}\"? This will also delete all subcategories and products within it.`)) {\n      onDelete(category.id);\n    }\n  };\n\n  const subcategories = category.subcategories || [];\n\n  return (\n    <Card className=\"overflow-hidden\">\n      {/* Category Header */}\n      <div className=\"p-6 border-b border-gray-200 bg-gray-50\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <button\n              onClick={() => setIsExpanded(!isExpanded)}\n              className=\"p-1 rounded-full hover:bg-gray-200 focus:outline-none\"\n            >\n              {isExpanded ? (\n                <ChevronDownIcon className=\"h-5 w-5 text-gray-500\" />\n              ) : (\n                <ChevronRightIcon className=\"h-5 w-5 text-gray-500\" />\n              )}\n            </button>\n            \n            <div className=\"flex items-center space-x-3\">\n              <TagIcon className=\"h-6 w-6 text-primary\" />\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900\">{category.name}</h3>\n                <p className=\"text-sm text-gray-600\">{category.description}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            {/* Status Badge */}\n            <StatusBadge status={category.status} />\n            \n            {/* Stats */}\n            <div className=\"text-sm text-gray-500\">\n              <span>{category.subcategoryCount} subcategories</span>\n              <span className=\"mx-2\">•</span>\n              <span>{category.productCount} products</span>\n            </div>\n\n            {/* Visibility Controls */}\n            <div className=\"flex items-center space-x-2\">\n              <button\n                onClick={() => onToggleVisibility(category.id, 'supplier')}\n                className={`p-2 rounded-full transition-colors ${\n                  category.visibleInSupplierApp\n                    ? 'text-green-600 bg-green-100 hover:bg-green-200'\n                    : 'text-gray-400 bg-gray-100 hover:bg-gray-200'\n                }`}\n                title={`${category.visibleInSupplierApp ? 'Hide from' : 'Show in'} Supplier App`}\n              >\n                {category.visibleInSupplierApp ? (\n                  <EyeIcon className=\"h-4 w-4\" />\n                ) : (\n                  <EyeSlashIcon className=\"h-4 w-4\" />\n                )}\n              </button>\n              \n              <button\n                onClick={() => onToggleVisibility(category.id, 'customer')}\n                className={`p-2 rounded-full transition-colors ${\n                  category.visibleInCustomerApp\n                    ? 'text-blue-600 bg-blue-100 hover:bg-blue-200'\n                    : 'text-gray-400 bg-gray-100 hover:bg-gray-200'\n                }`}\n                title={`${category.visibleInCustomerApp ? 'Hide from' : 'Show in'} Customer App`}\n              >\n                {category.visibleInCustomerApp ? (\n                  <EyeIcon className=\"h-4 w-4\" />\n                ) : (\n                  <EyeSlashIcon className=\"h-4 w-4\" />\n                )}\n              </button>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"flex items-center space-x-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                icon={<PlusIcon className=\"h-4 w-4\" />}\n                onClick={() => onAddSubcategory(category)}\n              >\n                Add Subcategory\n              </Button>\n              \n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                icon={<TrashIcon className=\"h-4 w-4\" />}\n                onClick={handleDelete}\n                className=\"text-red-600 border-red-300 hover:bg-red-50\"\n              >\n                Delete\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Subcategories */}\n      {isExpanded && (\n        <div className=\"p-6\">\n          {subcategories.length === 0 ? (\n            <div className=\"text-center py-8 text-gray-500\">\n              <TagIcon className=\"h-12 w-12 mx-auto mb-3 text-gray-300\" />\n              <p className=\"text-lg font-medium mb-1\">No subcategories yet</p>\n              <p className=\"text-sm\">Add subcategories to organize products within this category</p>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                icon={<PlusIcon className=\"h-4 w-4\" />}\n                onClick={() => onAddSubcategory(category)}\n                className=\"mt-4\"\n              >\n                Add First Subcategory\n              </Button>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {subcategories.map((subcategory) => (\n                <SubcategoryManagementCard\n                  key={subcategory.id}\n                  subcategory={subcategory}\n                  onDelete={onDeleteSubcategory}\n                  onToggleVisibility={onToggleSubcategoryVisibility}\n                  onProductClick={onProductClick}\n                />\n              ))}\n            </div>\n          )}\n        </div>\n      )}\n    </Card>\n  );\n};\n\nexport default memo(CategoryManagementCard);\n", "/**\n * Category Hierarchy View Component\n *\n * Displays the three-level hierarchy: Categories → Subcategories → Products\n */\n\nimport React, { memo } from 'react';\nimport CategoryManagementCard from './CategoryManagementCard';\nimport type { Category } from '../../features/categories/types';\n\ninterface CategoryHierarchyViewProps {\n  categories: Category[];\n  onDeleteCategory: (categoryId: string) => void;\n  onDeleteSubcategory: (subcategoryId: string) => void;\n  onToggleCategoryVisibility: (categoryId: string, app: 'supplier' | 'customer') => void;\n  onToggleSubcategoryVisibility: (subcategoryId: string, app: 'supplier' | 'customer') => void;\n  onProductClick: (productId: string) => void;\n  onAddSubcategory: (category: Category) => void;\n}\n\nconst CategoryHierarchyView: React.FC<CategoryHierarchyViewProps> = ({\n  categories,\n  onDeleteCategory,\n  onDeleteSubcategory,\n  onToggleCategoryVisibility,\n  onToggleSubcategoryVisibility,\n  onProductClick,\n  onAddSubcategory\n}) => {\n  if (categories.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"text-gray-500 text-lg mb-2\">No categories found</div>\n        <div className=\"text-gray-400 text-sm\">\n          Create your first category to get started with organizing your products\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {categories.map((category) => (\n        <CategoryManagementCard\n          key={category.id}\n          category={category}\n          onDelete={onDeleteCategory}\n          onDeleteSubcategory={onDeleteSubcategory}\n          onToggleVisibility={onToggleCategoryVisibility}\n          onToggleSubcategoryVisibility={onToggleSubcategoryVisibility}\n          onProductClick={onProductClick}\n          onAddSubcategory={onAddSubcategory}\n        />\n      ))}\n    </div>\n  );\n};\n\nexport default memo(CategoryHierarchyView);\n", "/**\n * Add Subcategory Form Component\n *\n * Form for adding a new subcategory to a category\n */\n\nimport React, { useState } from 'react';\nimport Button from '../common/Button';\nimport { validateForm, validationRules } from '../../utils/validation';\nimport type { SubcategoryFormData } from '../../features/categories/types';\n\ninterface AddSubcategoryFormProps {\n  categoryId: string;\n  onSubmit: (data: SubcategoryFormData) => void;\n  onCancel: () => void;\n  isLoading?: boolean;\n}\n\nconst AddSubcategoryForm: React.FC<AddSubcategoryFormProps> = ({\n  categoryId,\n  onSubmit,\n  onCancel,\n  isLoading = false\n}) => {\n  const [formData, setFormData] = useState<SubcategoryFormData>({\n    name: '',\n    description: '',\n    status: 'active',\n    categoryId,\n    visibleInSupplierApp: true,\n    visibleInCustomerApp: true\n  });\n\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n    \n    // Clear error when field is edited\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n  };\n\n  const validateFormData = () => {\n    const validationErrors = validateForm({\n      name: formData.name,\n      description: formData.description\n    }, {\n      name: [validationRules.required('Subcategory name is required')],\n      description: [validationRules.required('Description is required')]\n    });\n\n    setErrors(validationErrors);\n    return Object.keys(validationErrors).length === 0;\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (validateFormData()) {\n      onSubmit(formData);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      <div className=\"space-y-4\">\n        <div>\n          <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">\n            Subcategory Name <span className=\"text-red-500\">*</span>\n          </label>\n          <input\n            type=\"text\"\n            id=\"name\"\n            name=\"name\"\n            value={formData.name}\n            onChange={handleChange}\n            className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm ${\n              errors.name ? 'border-red-300' : ''\n            }`}\n            placeholder=\"Enter subcategory name\"\n          />\n          {errors.name && <p className=\"mt-1 text-sm text-red-600\">{errors.name}</p>}\n        </div>\n\n        <div>\n          <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700\">\n            Description <span className=\"text-red-500\">*</span>\n          </label>\n          <textarea\n            id=\"description\"\n            name=\"description\"\n            rows={3}\n            value={formData.description}\n            onChange={handleChange}\n            className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm ${\n              errors.description ? 'border-red-300' : ''\n            }`}\n            placeholder=\"Enter subcategory description\"\n          />\n          {errors.description && <p className=\"mt-1 text-sm text-red-600\">{errors.description}</p>}\n        </div>\n\n        <div>\n          <label htmlFor=\"status\" className=\"block text-sm font-medium text-gray-700\">\n            Status\n          </label>\n          <select\n            id=\"status\"\n            name=\"status\"\n            value={formData.status}\n            onChange={handleChange}\n            className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm\"\n          >\n            <option value=\"active\">Active</option>\n            <option value=\"inactive\">Inactive</option>\n          </select>\n        </div>\n\n        <div className=\"grid grid-cols-2 gap-4\">\n          <div>\n            <label className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                name=\"visibleInSupplierApp\"\n                checked={formData.visibleInSupplierApp}\n                onChange={(e) => setFormData(prev => ({ ...prev, visibleInSupplierApp: e.target.checked }))}\n                className=\"rounded border-gray-300 text-primary focus:ring-primary\"\n              />\n              <span className=\"ml-2 text-sm text-gray-700\">Visible in Supplier App</span>\n            </label>\n          </div>\n          <div>\n            <label className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                name=\"visibleInCustomerApp\"\n                checked={formData.visibleInCustomerApp}\n                onChange={(e) => setFormData(prev => ({ ...prev, visibleInCustomerApp: e.target.checked }))}\n                className=\"rounded border-gray-300 text-primary focus:ring-primary\"\n              />\n              <span className=\"ml-2 text-sm text-gray-700\">Visible in Customer App</span>\n            </label>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex justify-end space-x-3\">\n        <Button \n          type=\"button\" \n          variant=\"outline\" \n          onClick={onCancel}\n          disabled={isLoading}\n        >\n          Cancel\n        </Button>\n        <Button \n          type=\"submit\" \n          loading={isLoading}\n        >\n          Add Subcategory\n        </Button>\n      </div>\n    </form>\n  );\n};\n\nexport default AddSubcategoryForm;\n", "/**\n * Category Management Page\n *\n * This page provides a comprehensive interface for managing the three-level hierarchy:\n * Categories → Subcategories → Products\n */\n\nimport React, { useState, useCallback, memo, useMemo } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport PageHeader from '../components/layout/PageHeader';\nimport Card from '../components/common/Card';\nimport Button from '../components/common/Button';\nimport Modal from '../components/common/Modal';\nimport LoadingSpinner from '../components/common/LoadingSpinner';\nimport { PlusIcon, FunnelIcon } from '@heroicons/react/24/outline';\nimport { \n  useCategories,\n  Category,\n  CategoryFormData,\n  AddCategoryForm\n} from '../features/categories';\nimport CategoryHierarchyView from '../components/categories/CategoryHierarchyView';\nimport AddSubcategoryForm from '../components/categories/AddSubcategoryForm';\nimport { ROUTES } from '../constants/routes';\nimport useNotification from '../hooks/useNotification';\nimport type { SubcategoryFormData } from '../features/categories/types';\n\nconst CategoryManagementPage: React.FC = () => {\n  const navigate = useNavigate();\n  const { categories, isLoading, fetchCategories } = useCategories();\n  const { showNotification } = useNotification();\n  \n  // Modal states\n  const [isAddCategoryModalOpen, setIsAddCategoryModalOpen] = useState(false);\n  const [isAddSubcategoryModalOpen, setIsAddSubcategoryModalOpen] = useState(false);\n  const [selectedCategoryForSubcategory, setSelectedCategoryForSubcategory] = useState<Category | null>(null);\n  \n  // Filter states\n  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');\n  const [visibilityFilter, setVisibilityFilter] = useState<'all' | 'supplier' | 'customer'>('all');\n\n  // Memoized filtered categories\n  const filteredCategories = useMemo(() => {\n    return categories.filter(category => {\n      // Status filter\n      if (statusFilter !== 'all' && category.status !== statusFilter) {\n        return false;\n      }\n      \n      // Visibility filter\n      if (visibilityFilter === 'supplier' && !category.visibleInSupplierApp) {\n        return false;\n      }\n      if (visibilityFilter === 'customer' && !category.visibleInCustomerApp) {\n        return false;\n      }\n      \n      return true;\n    });\n  }, [categories, statusFilter, visibilityFilter]);\n\n  // Event handlers\n  const handleAddCategory = useCallback(async (categoryData: CategoryFormData) => {\n    try {\n      // TODO: Implement actual API call\n      console.log('Adding category:', categoryData);\n      setIsAddCategoryModalOpen(false);\n      await fetchCategories();\n      showNotification({\n        type: 'success',\n        title: 'Success',\n        message: 'Category added successfully'\n      });\n    } catch (error) {\n      showNotification({\n        type: 'error',\n        title: 'Error',\n        message: 'Failed to add category'\n      });\n    }\n  }, [fetchCategories, showNotification]);\n\n  const handleAddSubcategory = useCallback(async (subcategoryData: SubcategoryFormData) => {\n    try {\n      // TODO: Implement actual API call\n      console.log('Adding subcategory:', subcategoryData);\n      setIsAddSubcategoryModalOpen(false);\n      setSelectedCategoryForSubcategory(null);\n      await fetchCategories();\n      showNotification({\n        type: 'success',\n        title: 'Success',\n        message: 'Subcategory added successfully'\n      });\n    } catch (error) {\n      showNotification({\n        type: 'error',\n        title: 'Error',\n        message: 'Failed to add subcategory'\n      });\n    }\n  }, [fetchCategories, showNotification]);\n\n  const handleDeleteCategory = useCallback(async (categoryId: string) => {\n    try {\n      // TODO: Implement actual API call\n      console.log('Deleting category:', categoryId);\n      await fetchCategories();\n      showNotification({\n        type: 'success',\n        title: 'Success',\n        message: 'Category deleted successfully'\n      });\n    } catch (error) {\n      showNotification({\n        type: 'error',\n        title: 'Error',\n        message: 'Failed to delete category'\n      });\n    }\n  }, [fetchCategories, showNotification]);\n\n  const handleDeleteSubcategory = useCallback(async (subcategoryId: string) => {\n    try {\n      // TODO: Implement actual API call\n      console.log('Deleting subcategory:', subcategoryId);\n      await fetchCategories();\n      showNotification({\n        type: 'success',\n        title: 'Success',\n        message: 'Subcategory deleted successfully'\n      });\n    } catch (error) {\n      showNotification({\n        type: 'error',\n        title: 'Error',\n        message: 'Failed to delete subcategory'\n      });\n    }\n  }, [fetchCategories, showNotification]);\n\n  const handleToggleCategoryVisibility = useCallback(async (categoryId: string, app: 'supplier' | 'customer') => {\n    try {\n      // TODO: Implement actual API call\n      console.log('Toggling category visibility:', categoryId, app);\n      await fetchCategories();\n      showNotification({\n        type: 'success',\n        title: 'Success',\n        message: 'Category visibility updated'\n      });\n    } catch (error) {\n      showNotification({\n        type: 'error',\n        title: 'Error',\n        message: 'Failed to update category visibility'\n      });\n    }\n  }, [fetchCategories, showNotification]);\n\n  const handleToggleSubcategoryVisibility = useCallback(async (subcategoryId: string, app: 'supplier' | 'customer') => {\n    try {\n      // TODO: Implement actual API call\n      console.log('Toggling subcategory visibility:', subcategoryId, app);\n      await fetchCategories();\n      showNotification({\n        type: 'success',\n        title: 'Success',\n        message: 'Subcategory visibility updated'\n      });\n    } catch (error) {\n      showNotification({\n        type: 'error',\n        title: 'Error',\n        message: 'Failed to update subcategory visibility'\n      });\n    }\n  }, [fetchCategories, showNotification]);\n\n  const handleProductClick = useCallback((productId: string) => {\n    navigate(ROUTES.getProductDetailsRoute(productId));\n  }, [navigate]);\n\n  const handleRequestAddSubcategory = useCallback((category: Category) => {\n    setSelectedCategoryForSubcategory(category);\n    setIsAddSubcategoryModalOpen(true);\n  }, []);\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <LoadingSpinner size=\"lg\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <PageHeader\n        title=\"Category Management\"\n        description=\"Manage categories, subcategories, and their visibility in supplier and customer apps\"\n        actions={\n          <div className=\"flex space-x-3\">\n            <Button\n              variant=\"outline\"\n              icon={<FunnelIcon className=\"h-5 w-5\" />}\n              onClick={() => {/* TODO: Implement filter modal */}}\n            >\n              Filters\n            </Button>\n            <Button\n              icon={<PlusIcon className=\"h-5 w-5\" />}\n              onClick={() => setIsAddCategoryModalOpen(true)}\n            >\n              Add Category\n            </Button>\n          </div>\n        }\n      />\n\n      {/* Filter Controls */}\n      <Card>\n        <div className=\"flex flex-wrap gap-4 p-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Status</label>\n            <select\n              value={statusFilter}\n              onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}\n              className=\"rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm\"\n            >\n              <option value=\"all\">All Status</option>\n              <option value=\"active\">Active</option>\n              <option value=\"inactive\">Inactive</option>\n            </select>\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Visibility</label>\n            <select\n              value={visibilityFilter}\n              onChange={(e) => setVisibilityFilter(e.target.value as 'all' | 'supplier' | 'customer')}\n              className=\"rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm\"\n            >\n              <option value=\"all\">All Apps</option>\n              <option value=\"supplier\">Supplier App Only</option>\n              <option value=\"customer\">Customer App Only</option>\n            </select>\n          </div>\n        </div>\n      </Card>\n\n      {/* Category Hierarchy */}\n      <CategoryHierarchyView\n        categories={filteredCategories}\n        onDeleteCategory={handleDeleteCategory}\n        onDeleteSubcategory={handleDeleteSubcategory}\n        onToggleCategoryVisibility={handleToggleCategoryVisibility}\n        onToggleSubcategoryVisibility={handleToggleSubcategoryVisibility}\n        onProductClick={handleProductClick}\n        onAddSubcategory={handleRequestAddSubcategory}\n      />\n\n      {/* Add Category Modal */}\n      <Modal\n        isOpen={isAddCategoryModalOpen}\n        onClose={() => setIsAddCategoryModalOpen(false)}\n        title=\"Add Category\"\n      >\n        <AddCategoryForm\n          onSubmit={handleAddCategory}\n          onCancel={() => setIsAddCategoryModalOpen(false)}\n        />\n      </Modal>\n\n      {/* Add Subcategory Modal */}\n      <Modal\n        isOpen={isAddSubcategoryModalOpen}\n        onClose={() => {\n          setIsAddSubcategoryModalOpen(false);\n          setSelectedCategoryForSubcategory(null);\n        }}\n        title={`Add Subcategory to ${selectedCategoryForSubcategory?.name || ''}`}\n      >\n        {selectedCategoryForSubcategory && (\n          <AddSubcategoryForm\n            categoryId={selectedCategoryForSubcategory.id}\n            onSubmit={handleAddSubcategory}\n            onCancel={() => {\n              setIsAddSubcategoryModalOpen(false);\n              setSelectedCategoryForSubcategory(null);\n            }}\n          />\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default memo(CategoryManagementPage);\n", "/**\r\n * Formatters\r\n * \r\n * This file contains utility functions for formatting data.\r\n */\r\n\r\n/**\r\n * Format a date string to a human-readable format\r\n */\r\nexport const formatDate = (dateString: string, options: Intl.DateTimeFormatOptions = {}): string => {\r\n  if (!dateString) return '-';\r\n  \r\n  try {\r\n    const date = new Date(dateString);\r\n    \r\n    // Default options\r\n    const defaultOptions: Intl.DateTimeFormatOptions = {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      ...options\r\n    };\r\n    \r\n    return new Intl.DateTimeFormat('en-US', defaultOptions).format(date);\r\n  } catch (error) {\r\n    console.error('Error formatting date:', error);\r\n    return dateString;\r\n  }\r\n};\r\n\r\n/**\r\n * Format a date string to include time\r\n */\r\nexport const formatDateTime = (dateString: string): string => {\r\n  return formatDate(dateString, {\r\n    year: 'numeric',\r\n    month: 'short',\r\n    day: 'numeric',\r\n    hour: '2-digit',\r\n    minute: '2-digit'\r\n  });\r\n};\r\n\r\n/**\r\n * Format a number as currency\r\n */\r\nexport const formatCurrency = (\r\n  amount: number,\r\n  currency: string = 'USD',\r\n  locale: string = 'en-US'\r\n): string => {\r\n  try {\r\n    return new Intl.NumberFormat(locale, {\r\n      style: 'currency',\r\n      currency,\r\n      minimumFractionDigits: 2,\r\n      maximumFractionDigits: 2\r\n    }).format(amount);\r\n  } catch (error) {\r\n    console.error('Error formatting currency:', error);\r\n    return `${currency} ${amount.toFixed(2)}`;\r\n  }\r\n};\r\n\r\n/**\r\n * Format a number with commas\r\n */\r\nexport const formatNumber = (\r\n  number: number,\r\n  options: Intl.NumberFormatOptions = {}\r\n): string => {\r\n  try {\r\n    return new Intl.NumberFormat('en-US', options).format(number);\r\n  } catch (error) {\r\n    console.error('Error formatting number:', error);\r\n    return number.toString();\r\n  }\r\n};\r\n\r\n/**\r\n * Format a phone number\r\n */\r\nexport const formatPhoneNumber = (phoneNumber: string): string => {\r\n  if (!phoneNumber) return '-';\r\n  \r\n  // Remove all non-numeric characters\r\n  const cleaned = phoneNumber.replace(/\\D/g, '');\r\n  \r\n  // Format based on length\r\n  if (cleaned.length === 10) {\r\n    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;\r\n  } else if (cleaned.length === 11 && cleaned.startsWith('1')) {\r\n    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;\r\n  }\r\n  \r\n  // If it doesn't match expected formats, return as is\r\n  return phoneNumber;\r\n};\r\n\r\n/**\r\n * Truncate text with ellipsis\r\n */\r\nexport const truncateText = (text: string, maxLength: number): string => {\r\n  if (!text) return '';\r\n  if (text.length <= maxLength) return text;\r\n  \r\n  return `${text.slice(0, maxLength)}...`;\r\n};\r\n\r\n/**\r\n * Format file size\r\n */\r\nexport const formatFileSize = (bytes: number): string => {\r\n  if (bytes === 0) return '0 Bytes';\r\n  \r\n  const k = 1024;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n  \r\n  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;\r\n};\r\n\r\n/**\r\n * Format a duration in milliseconds to a human-readable format\r\n */\r\nexport const formatDuration = (milliseconds: number): string => {\r\n  const seconds = Math.floor(milliseconds / 1000);\r\n  const minutes = Math.floor(seconds / 60);\r\n  const hours = Math.floor(minutes / 60);\r\n  const days = Math.floor(hours / 24);\r\n  \r\n  if (days > 0) {\r\n    return `${days} day${days > 1 ? 's' : ''}`;\r\n  } else if (hours > 0) {\r\n    return `${hours} hour${hours > 1 ? 's' : ''}`;\r\n  } else if (minutes > 0) {\r\n    return `${minutes} minute${minutes > 1 ? 's' : ''}`;\r\n  } else {\r\n    return `${seconds} second${seconds !== 1 ? 's' : ''}`;\r\n  }\r\n};\r\n", "import React from 'react';\r\nimport {\r\n  CheckCircleIcon,\r\n  XCircleIcon,\r\n  ClockIcon,\r\n  TruckIcon,\r\n  ExclamationCircleIcon\r\n} from '@heroicons/react/24/outline';\r\n\r\nexport type StatusType = 'user' | 'supplier' | 'order' | 'verification' | 'category';\r\n\r\ninterface StatusBadgeProps {\r\n  status: string;\r\n  type?: StatusType;\r\n  className?: string;\r\n}\r\n\r\nconst StatusBadge: React.FC<StatusBadgeProps> = ({\r\n  status,\r\n  type: _type = 'user',\r\n  className = ''\r\n}) => {\r\n  // Handle undefined or null status\r\n  if (!status) {\r\n    return (\r\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 ${className}`}>\r\n        Unknown\r\n      </span>\r\n    );\r\n  }\r\n\r\n  const statusKey = status.toLowerCase();\r\n  let colorClass = '';\r\n  let icon = null;\r\n  \r\n  // Common statuses across entity types\r\n  if (statusKey === 'active' || statusKey === 'verified' || statusKey === 'completed') {\r\n    colorClass = 'bg-green-100 text-green-800';\r\n    icon = <CheckCircleIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'pending' || statusKey === 'processing') {\r\n    colorClass = 'bg-blue-100 text-blue-800';\r\n    icon = <ClockIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'banned' || statusKey === 'rejected') {\r\n    colorClass = 'bg-red-100 text-red-800';\r\n    icon = <XCircleIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'shipped') {\r\n    colorClass = 'bg-purple-100 text-purple-800';\r\n    icon = <TruckIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'warning') {\r\n    colorClass = 'bg-yellow-100 text-yellow-800';\r\n    icon = <ExclamationCircleIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else {\r\n    colorClass = 'bg-gray-100 text-gray-800';\r\n  }\r\n  \r\n  // Format the status text (capitalize first letter)\r\n  const formattedStatus = status ? status.charAt(0).toUpperCase() + status.slice(1) : 'Unknown';\r\n  \r\n  return (\r\n    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass} ${className}`}>\r\n      {icon}\r\n      {formattedStatus}\r\n    </span>\r\n  );\r\n};\r\n\r\nexport default StatusBadge;\r\n", "import * as React from \"react\";\nfunction TruckIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(TruckIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ClockIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ClockIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction CubeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m21 7.5-9-5.25L3 7.5m18 0-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CubeIcon);\nexport default ForwardRef;"], "names": ["FunnelIcon", "_ref", "svgRef", "title", "titleId", "props", "React", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "id", "strokeLinecap", "strokeLinejoin", "d", "ProductCard", "product", "onClick", "_jsxs", "handleClick", "className", "children", "_jsx", "image", "src", "alt", "name", "CubeIcon", "sku", "formatCurrency", "price", "StatusBadge", "status", "stock", "memo", "FolderIcon", "EyeSlashIcon", "SubcategoryManagementCard", "subcategory", "onDelete", "onToggleVisibility", "onProductClick", "isExpanded", "setIsExpanded", "useState", "products", "ChevronDownIcon", "ChevronRightIcon", "description", "productCount", "visibleInSupplierApp", "EyeIcon", "visibleInCustomerApp", "<PERSON><PERSON>", "variant", "size", "icon", "TrashIcon", "handleDelete", "window", "confirm", "length", "map", "CategoryManagementCard", "category", "onDeleteSubcategory", "onToggleSubcategoryVisibility", "onAddSubcategory", "subcategories", "Card", "TagIcon", "subcategoryCount", "PlusIcon", "CategoryHierarchyView", "categories", "onDeleteCategory", "onToggleCategoryVisibility", "categoryId", "onSubmit", "onCancel", "isLoading", "formData", "setFormData", "errors", "setErrors", "handleChange", "e", "value", "target", "prev", "preventDefault", "validateFormData", "validationErrors", "validateForm", "validationRules", "required", "keys", "htmlFor", "type", "onChange", "placeholder", "rows", "checked", "disabled", "loading", "CategoryManagementPage", "navigate", "useNavigate", "fetchCategories", "useCategories", "showNotification", "useNotification", "isAddCategoryModalOpen", "setIsAddCategoryModalOpen", "isAddSubcategoryModalOpen", "setIsAddSubcategoryModalOpen", "selectedCategoryForSubcategory", "setSelectedCategoryForSubcategory", "statusFilter", "setStatus<PERSON>ilter", "visibilityFilter", "setVisibilityFilter", "filteredCategories", "useMemo", "filter", "handleAddCategory", "useCallback", "async", "console", "log", "categoryData", "message", "error", "handleAddSubcategory", "subcategoryData", "handleDeleteCategory", "handleDeleteSubcategory", "subcategoryId", "handleToggleCategoryVisibility", "app", "handleToggleSubcategoryVisibility", "handleProductClick", "productId", "ROUTES", "getProductDetailsRoute", "handleRequestAddSubcategory", "LoadingSpinner", "<PERSON><PERSON><PERSON><PERSON>", "actions", "Modal", "isOpen", "onClose", "AddCategoryForm", "AddSubcategoryForm", "formatDate", "dateString", "options", "arguments", "undefined", "date", "Date", "defaultOptions", "year", "month", "day", "Intl", "DateTimeFormat", "format", "amount", "currency", "locale", "NumberFormat", "style", "minimumFractionDigits", "maximumFractionDigits", "toFixed", "_type", "statusKey", "toLowerCase", "colorClass", "CheckCircleIcon", "ClockIcon", "XCircleIcon", "TruckIcon", "ExclamationCircleIcon", "formattedStatus", "char<PERSON>t", "toUpperCase", "slice"], "sourceRoot": ""}