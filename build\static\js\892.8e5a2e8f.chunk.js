"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[892],{312:(e,s,t)=>{t.d(s,{A:()=>a});t(5043);var r=t(579);const a=e=>{let{children:s,className:t=""}=e;return(0,r.jsx)("dl",{className:`sm:divide-y sm:divide-gray-200 ${t}`,children:s})}},1602:(e,s,t)=>{t.d(s,{A:()=>i});var r=t(5043);function a(e,s){let{title:t,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},i),t?r.createElement("title",{id:a},t):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"}))}const i=r.forwardRef(a)},5149:(e,s,t)=>{t.d(s,{A:()=>a});t(5043);var r=t(579);const a=e=>{let{label:s,value:t,className:a=""}=e;return(0,r.jsxs)("div",{className:`py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 ${a}`,children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:s}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:t})]})}},5892:(e,s,t)=>{t.d(s,{pC:()=>h,Sk:()=>y,cA:()=>Z,ID:()=>U,Pt:()=>T,w2:()=>D,kp:()=>me});var r=t(5043),a=t(7907),i=t(5901),n=t(7147),l=t(6773),d=t(1568),o=t(4703),c=t(8479);const m={getBusinessTypes:async()=>{try{const e=await d.A.get("/business-types");return c.lg.getList(e,"business types")}catch(e){throw(0,o.hS)(e)}},getBusinessTypeById:async e=>{try{const s=await d.A.get(`/business-types/${e}`);return c.lg.getById(s,"business type",e)}catch(s){throw(0,o.hS)(s)}}},{getBusinessTypes:u,getBusinessTypeById:p}=m;var x=t(579);const h=e=>{let{onSubmit:s,onCancel:t,isLoading:d=!1}=e;const[o,c]=(0,r.useState)({name:"",email:"",type:"customer",phone:"",address:"",businessType:"",password:"",confirmPassword:"",sendInvite:!0,image:null}),[m,p]=(0,r.useState)({}),[h,g]=(0,r.useState)([]),[y,v]=(0,r.useState)(!1);(0,r.useEffect)((()=>{(async()=>{v(!0);try{const e=await u();g(e)}catch(e){console.error("Failed to load business types:",e)}finally{v(!1)}})()}),[]);const b=e=>{const{name:s,value:t,type:r}=e.target;if("checkbox"===r){const t=e.target.checked;c((e=>({...e,[s]:t})))}else c((e=>({...e,[s]:t})));m[s]&&p((e=>({...e,[s]:""})))};return(0,x.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),(()=>{const e={name:[l.tU.required("Name is required")],email:[l.tU.required("Email is required"),l.tU.email()],type:[l.tU.required("User type is required")],password:[l.tU.required("Password is required"),l.tU.password()],confirmPassword:[l.tU.required("Confirm password is required"),l.tU.passwordMatch()],address:[l.tU.required("Address is required")],businessType:[l.tU.required("Business type is required")]},s=(0,l.l)(o,e);return p(s),0===Object.keys(s).length})()){const e={...o,image:o.image};s(e)}},className:"space-y-6",children:[(0,x.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[(0,x.jsx)(i.A,{label:"Full Name",name:"name",value:o.name,onChange:b,error:m.name,required:!0}),(0,x.jsx)(i.A,{label:"Email",name:"email",type:"email",value:o.email,onChange:b,error:m.email,required:!0}),(0,x.jsx)(i.A,{label:"User Type",name:"type",type:"select",value:o.type,onChange:b,error:m.type,required:!0,options:[{value:"customer",label:"Customer"},{value:"supplier",label:"Supplier"}]}),(0,x.jsx)(i.A,{label:"Phone Number",name:"phone",value:o.phone,onChange:b,error:m.phone}),(0,x.jsx)(i.A,{label:"Address",name:"address",type:"textarea",value:o.address,onChange:b,error:m.address,required:!0,placeholder:"Enter full address"}),(0,x.jsx)(i.A,{label:"Business Type",name:"businessType",type:"select",value:o.businessType,onChange:b,error:m.businessType,required:!0,loading:y,options:[{value:"",label:"Select Business Type"},...h.map((e=>({value:e.id,label:e.name})))]}),(0,x.jsx)(i.A,{label:"Password",name:"password",type:"password",value:o.password,onChange:b,error:m.password,required:!0}),(0,x.jsx)(i.A,{label:"Confirm Password",name:"confirmPassword",type:"password",value:o.confirmPassword,onChange:b,error:m.confirmPassword,required:!0})]}),(0,x.jsx)(n.A,{label:"Profile Picture",name:"image",value:o.image,onChange:e=>{c((s=>({...s,image:e}))),m.image&&p((e=>({...e,image:""})))},error:m.image||void 0,maxSize:5242880,allowedTypes:["image/jpeg","image/png","image/gif","image/webp"]}),(0,x.jsx)("div",{className:"flex items-center",children:(0,x.jsx)(i.A,{label:"Send invitation email",name:"sendInvite",type:"checkbox",value:o.sendInvite,onChange:b,className:"flex items-center space-x-2"})}),(0,x.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,x.jsx)(a.A,{variant:"outline",onClick:t,disabled:d,children:"Cancel"}),(0,x.jsx)(a.A,{type:"submit",loading:d,children:"Add User"})]})]})};var g=t(3593);const y=e=>{let{user:s,onSubmit:t,isLoading:i=!1}=e;const[n,d]=(0,r.useState)({name:"",email:"",type:"customer"}),[o,c]=(0,r.useState)({});(0,r.useEffect)((()=>{s&&d({name:s.name,email:s.email,type:s.type})}),[s]);const m=e=>{const{name:s,value:t,type:r}=e.target;if("checkbox"===r){const t=e.target.checked;d((e=>({...e,[s]:t})))}else d((e=>({...e,[s]:t})));o[s]&&c((e=>({...e,[s]:""})))};return(0,x.jsx)(g.A,{children:(0,x.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),(()=>{const e={name:[l.tU.required("Name is required")],email:[l.tU.required("Email is required"),l.tU.email()],type:[l.tU.required("User type is required")]},s=(0,l.l)(n,e);return c(s),0===Object.keys(s).length})())try{await t(n)}catch(s){console.error("Form submission error:",s)}},className:"p-6 space-y-6",children:[(0,x.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[(0,x.jsxs)("div",{children:[(0,x.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:["Full Name ",(0,x.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,x.jsx)("input",{type:"text",id:"name",name:"name",value:n.name,onChange:m,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm "+(o.name?"border-red-300":"")}),o.name&&(0,x.jsx)("p",{className:"mt-1 text-sm text-red-600",children:o.name})]}),(0,x.jsxs)("div",{children:[(0,x.jsxs)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:["Email ",(0,x.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,x.jsx)("input",{type:"email",id:"email",name:"email",value:n.email,onChange:m,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm "+(o.email?"border-red-300":"")}),o.email&&(0,x.jsx)("p",{className:"mt-1 text-sm text-red-600",children:o.email})]}),(0,x.jsxs)("div",{children:[(0,x.jsxs)("label",{htmlFor:"type",className:"block text-sm font-medium text-gray-700",children:["User Type ",(0,x.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,x.jsxs)("select",{id:"type",name:"type",value:n.type,onChange:m,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm "+(o.type?"border-red-300":""),children:[(0,x.jsx)("option",{value:"customer",children:"Customer"}),(0,x.jsx)("option",{value:"supplier",children:"Supplier"})]}),o.type&&(0,x.jsx)("p",{className:"mt-1 text-sm text-red-600",children:o.type})]}),(0,x.jsxs)("div",{children:[(0,x.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"New Password"}),(0,x.jsx)("input",{type:"password",id:"password",name:"password",value:n.password||"",onChange:m,placeholder:"Leave blank to keep current password",className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm "+(o.password?"border-red-300":"")}),o.password&&(0,x.jsx)("p",{className:"mt-1 text-sm text-red-600",children:o.password})]})]}),(0,x.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,x.jsx)(a.A,{variant:"outline",type:"button",onClick:()=>window.history.back(),disabled:i,children:"Cancel"}),(0,x.jsx)(a.A,{type:"submit",loading:i,children:"Save Changes"})]})]})})};var v=t(3216),b=t(6887),j=t(312),f=t(5149),w=t(3893),N=t(4692),A=t(9531);const k=e=>{let{orders:s,title:t="Orders",description:r="Recent orders",onViewOrder:i,emptyMessage:n="No orders found",className:l=""}=e;return(0,x.jsx)(b.A,{title:t,description:r,className:l,children:0===s.length?(0,x.jsx)("div",{className:"px-4 py-5 text-center text-sm text-gray-500",children:n}):(0,x.jsx)("div",{className:"overflow-x-auto",children:(0,x.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,x.jsx)("thead",{className:"bg-gray-50",children:(0,x.jsxs)("tr",{children:[(0,x.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Order ID"}),(0,x.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,x.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,x.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,x.jsx)("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,x.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map((e=>(0,x.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,x.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-primary",children:e.id}),(0,x.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,w.Yq)(e.orderDate)}),(0,x.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,w.vv)(e.totalAmount)}),(0,x.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,x.jsx)(N.A,{status:e.status,type:"order"})}),(0,x.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:i&&(0,x.jsx)(a.A,{variant:"text",size:"sm",onClick:()=>i(e),icon:(0,x.jsx)(A.A,{className:"w-4 h-4 text-black"}),className:"text-black hover:text-gray-700 hover:bg-gray-100",children:"View"})})]},e.id)))})]})})})};var E=t(724);const U=e=>{let{user:s,userOrders:t=[]}=e;const r=(0,v.Zp)();return(0,x.jsxs)("div",{className:"space-y-6",children:[(0,x.jsx)(b.A,{title:"User Information",description:"Personal details and application",children:(0,x.jsxs)(j.A,{children:[(0,x.jsx)(f.A,{label:"Full name",value:s.name}),(0,x.jsx)(f.A,{label:"Email address",value:s.email}),(0,x.jsx)(f.A,{label:"User type",value:s.type}),(0,x.jsx)(f.A,{label:"Status",value:(0,x.jsx)(N.A,{status:s.status,type:"user"})}),(0,x.jsx)(f.A,{label:"Last login",value:s.lastLogin})]})}),(0,x.jsx)(k,{orders:t,title:"User Orders",description:"Orders placed by this user",onViewOrder:e=>{r(E.b.getOrderDetailsRoute(e.id))},emptyMessage:"This user has not placed any orders yet"})]})};var S=t(8300),C=t(4538),F=t(7098);const T=e=>{let{user:s}=e;return(0,x.jsxs)("div",{className:"space-y-6",children:[(0,x.jsx)("div",{className:"flex items-center justify-between",children:(0,x.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,x.jsx)(S.A,{...s.avatar&&{src:s.avatar},alt:s.name,name:s.name,size:"xl"}),(0,x.jsxs)("div",{children:[(0,x.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:s.name}),(0,x.jsxs)("p",{className:"text-sm text-gray-500",children:["ID: ",s.id]}),(0,x.jsx)("div",{className:"mt-1",children:(0,x.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium "+("active"===s.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})})]})]})}),(0,x.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 border-t border-gray-200 pt-4",children:[(0,x.jsxs)("div",{children:[(0,x.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-3",children:"Contact Information"}),(0,x.jsxs)("dl",{className:"space-y-3",children:[(0,x.jsxs)("div",{children:[(0,x.jsx)("dt",{className:"text-xs text-gray-500",children:"Email"}),(0,x.jsx)("dd",{className:"text-sm text-gray-900",children:s.email})]}),s.phone&&(0,x.jsxs)("div",{children:[(0,x.jsx)("dt",{className:"text-xs text-gray-500",children:"Phone"}),(0,x.jsx)("dd",{className:"text-sm text-gray-900",children:s.phone})]}),(0,x.jsxs)("div",{children:[(0,x.jsx)("dt",{className:"text-xs text-gray-500",children:"Last Login"}),(0,x.jsx)("dd",{className:"text-sm text-gray-900",children:s.lastLogin})]})]})]}),s.address&&(0,x.jsxs)("div",{children:[(0,x.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-3",children:"Address"}),(0,x.jsx)("address",{className:"not-italic text-sm text-gray-900",children:s.address})]})]}),s.businessType&&(0,x.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,x.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Business Type"}),(0,x.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:s.businessType})]}),(0,x.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,x.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Account Status"}),(0,x.jsxs)("div",{className:"flex items-center space-x-2",children:["active"===s.status?(0,x.jsx)(C.A,{className:"w-5 h-5 text-green-500"}):(0,x.jsx)(F.A,{className:"w-5 h-5 text-red-500"}),(0,x.jsx)("span",{className:"text-sm text-gray-700",children:"active"===s.status?`Active since ${s.lastLogin}`:"Account is banned"})]})]})]})};var L=t(5445),O=t(8682),B=t(2811),q=t(9248);const D=e=>{let{users:s,onViewUser:t,onEditUser:r,onDeleteUser:a,onUserClick:i,title:n="Users",loading:l=!1}=e;const d=(0,v.Zp)(),o=[{key:"name",label:"Name",sortable:!0,render:(e,s)=>(0,x.jsxs)("div",{className:"flex items-center",children:[s.avatar?(0,x.jsx)("img",{src:s.avatar,alt:s.name,className:"w-8 h-8 rounded-full mr-3 object-cover"}):(0,x.jsx)("div",{className:"w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-3",children:s.name.charAt(0)}),(0,x.jsxs)("div",{children:[(0,x.jsx)("div",{className:"font-medium text-gray-900",children:s.name}),(0,x.jsxs)("div",{className:"text-xs text-gray-500",children:["ID: ",s.id]})]})]})},{key:"email",label:"Email",sortable:!0,render:e=>(0,x.jsxs)("div",{className:"flex items-center",children:[(0,x.jsx)(O.A,{className:"w-4 h-4 text-gray-400 mr-2"}),(0,x.jsx)("span",{children:e})]})},{key:"type",label:"Type",sortable:!0},{key:"status",label:"Status",sortable:!0},{key:"lastLogin",label:"Last Login",sortable:!0},{key:"actions",label:"Actions",render:(e,s)=>(0,x.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,x.jsx)("button",{className:"p-1 text-gray-500 hover:text-primary rounded-full hover:bg-gray-100",onClick:e=>{e.stopPropagation(),t(s)},children:(0,x.jsx)(A.A,{className:"w-5 h-5"})}),(0,x.jsx)("button",{className:"p-1 text-gray-500 hover:text-blue-600 rounded-full hover:bg-gray-100",onClick:e=>{e.stopPropagation(),d(E.b.getUserEditRoute(s.id))},children:(0,x.jsx)(B.A,{className:"w-5 h-5"})}),(0,x.jsx)("button",{className:"p-1 text-gray-500 hover:text-red-600 rounded-full hover:bg-gray-100",onClick:e=>{e.stopPropagation(),a(s)},children:(0,x.jsx)(q.A,{className:"w-5 h-5"})})]})}];return(0,x.jsx)(L.A,{data:s,columns:o,onRowClick:e=>{d(E.b.getUserEditRoute(e.id))},title:n,pagination:!0,loading:l,emptyMessage:"No users found"})};var I=t(3488),P=t(2683),R=t(7012),$=t(6058);const H=e=>{if(!r.isValidElement(e))return"bg-primary bg-opacity-10";const s=(e.props.className||"").match(/text-([a-z]+)-/);if(s){return`bg-${s[1]}-50`}return"bg-primary bg-opacity-10"},V=e=>{let{title:s,data:t,icon:a,formatValue:i=e=>e.toString()}=e;return(0,x.jsx)(g.A,{children:(0,x.jsxs)("div",{className:"flex items-center",children:[(0,x.jsx)("div",{className:`p-3 rounded-full ${H(a)}`,children:r.isValidElement(a)?(()=>{const e=a,s=(e.props.className||"").match(/text-[a-z0-9-]+/),t=s?s[0]:"text-gray-600";return r.cloneElement(e,{className:`h-6 w-6 ${t}`})})():a}),(0,x.jsxs)("div",{className:"ml-4 flex-1",children:[(0,x.jsx)("p",{className:"text-sm font-medium text-gray-500",children:s}),(0,x.jsxs)("div",{className:"flex items-baseline",children:[(0,x.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:i(t.total)}),void 0!==t.growth&&(0,x.jsxs)("p",{className:"ml-2 flex items-baseline text-sm font-semibold "+(t.growth>=0?"text-green-600":"text-red-600"),children:[t.growth>=0?"+":"",t.growth.toFixed(1),"%"]})]})]})]})})},M=e=>{let{metrics:s,className:t=""}=e;return(0,x.jsx)("div",{className:`grid grid-cols-1 md:grid-cols-3 gap-6 ${t}`,children:s.map(((e,s)=>(0,x.jsx)(V,{title:e.title,data:{total:"string"===typeof e.value?parseFloat(e.value)||0:e.value,growth:e.change||0},icon:e.icon},s)))})};var z=t(7174);const Z=e=>{var s,t,r;let{userId:a,userData:i}=e;const n={totalOrders:(null===i||void 0===i?void 0:i.totalOrders)||0,totalSpent:(null===i||void 0===i?void 0:i.totalSpent)||0,averageOrderValue:(null===i||void 0===i?void 0:i.averageOrderValue)||0,orderFrequency:(null===i||void 0===i?void 0:i.orderFrequency)||0,orderHistory:(null===i||void 0===i?void 0:i.orderHistory)||[]},l=[{title:"Total Orders",value:n.totalOrders,icon:(0,x.jsx)(I.A,{className:"w-6 h-6 text-blue-500"})},{title:"Total Spent",value:(0,w.vv)(n.totalSpent),icon:(0,x.jsx)(P.A,{className:"w-6 h-6 text-green-500"})},{title:"Average Order",value:(0,w.vv)(n.averageOrderValue),icon:(0,x.jsx)(R.A,{className:"w-6 h-6 text-purple-500"})}],d=e=>{try{return new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"2-digit"})}catch(s){return e}},o=(()=>{if(0===n.orderHistory.length)return{labels:["No Data"],datasets:[{label:"Spending Trend",data:[0],borderColor:"#F28B22",backgroundColor:"rgba(242, 139, 34, 0.1)",fill:!0,tension:.4}]};const e=n.orderHistory.filter((e=>e&&e.date&&"number"===typeof e.amount)).sort(((e,s)=>new Date(e.date).getTime()-new Date(s.date).getTime()));let s=0;const t=e.map((e=>(s+=e.amount,{label:d(e.date),value:s})));return{labels:t.map((e=>e.label)),datasets:[{label:"Cumulative Spending",data:t.map((e=>e.value)),borderColor:"#F28B22",backgroundColor:"rgba(242, 139, 34, 0.1)",fill:!0,tension:.4,pointBackgroundColor:"#F28B22",pointBorderColor:"#ffffff",pointBorderWidth:2,pointRadius:4}]}})();return(0,x.jsxs)("div",{className:"space-y-6",children:[(0,x.jsx)(M,{metrics:l}),(0,x.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,x.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:[(0,x.jsxs)("div",{className:"mb-4",children:[(0,x.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Spending Trend"}),(0,x.jsx)("p",{className:"text-sm text-gray-500",children:n.orderHistory.length>0?"Cumulative spending over time":"No spending data available"})]}),n.orderHistory.length>0?(0,x.jsx)("div",{className:"h-80",children:(0,x.jsx)($.N1,{data:o,options:{...z.OL,plugins:{...z.OL.plugins,title:{display:!1},legend:{display:!1}},scales:{...z.OL.scales,y:{...null===(s=z.OL.scales)||void 0===s?void 0:s.y,ticks:{...null===(t=z.OL.scales)||void 0===t||null===(r=t.y)||void 0===r?void 0:r.ticks,callback:function(e){return(0,w.vv)(e)}}}}}})}):(0,x.jsx)("div",{className:"h-80 flex items-center justify-center",children:(0,x.jsxs)("div",{className:"text-center",children:[(0,x.jsx)("div",{className:"text-gray-400 text-4xl mb-4",children:"\ufffd"}),(0,x.jsx)("p",{className:"text-gray-500 text-lg font-medium",children:"No Spending Data"}),(0,x.jsx)("p",{className:"text-gray-400 text-sm mt-2",children:"Spending trends will appear here once the user places orders"})]})})]}),(0,x.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:[(0,x.jsxs)("div",{className:"mb-4",children:[(0,x.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Order Frequency"}),(0,x.jsx)("p",{className:"text-sm text-gray-500",children:"Order patterns and frequency analysis"})]}),(0,x.jsx)("div",{className:"space-y-4",children:n.orderHistory.length>0?(0,x.jsxs)(x.Fragment,{children:[(0,x.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,x.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Most Recent Order"}),(0,x.jsx)("span",{className:"text-sm text-gray-900",children:(()=>{const e=n.orderHistory[n.orderHistory.length-1];return null!==e&&void 0!==e&&e.date?d(e.date):"N/A"})()})]}),(0,x.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,x.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"First Order"}),(0,x.jsx)("span",{className:"text-sm text-gray-900",children:(()=>{const e=n.orderHistory[0];return null!==e&&void 0!==e&&e.date?d(e.date):"N/A"})()})]}),(0,x.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,x.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Largest Order"}),(0,x.jsx)("span",{className:"text-sm text-gray-900",children:(0,w.vv)(Math.max(...n.orderHistory.map((e=>e.amount))))})]}),(0,x.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,x.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Smallest Order"}),(0,x.jsx)("span",{className:"text-sm text-gray-900",children:(0,w.vv)(Math.min(...n.orderHistory.map((e=>e.amount))))})]})]}):(0,x.jsxs)("div",{className:"text-center py-8",children:[(0,x.jsx)("div",{className:"text-gray-400 text-3xl mb-3",children:"\ud83d\udcca"}),(0,x.jsx)("p",{className:"text-gray-500 font-medium",children:"No Order Data"}),(0,x.jsx)("p",{className:"text-gray-400 text-sm mt-1",children:"Order frequency analysis will appear here"})]})})]})]})]})};var G=t(233);const W={BASE:"/users",DETAILS:e=>`/users/${e}`,STATUS:e=>`/users/${e}/status`,BAN:e=>`/users/${e}/ban`,UNBAN:e=>`/users/${e}/unban`,UPLOAD_IMAGE:"/users/upload-image"},_=e=>{const s={Name:e.name,Email:e.email,verificationStatus:e.verificationStatus||"pending"};return void 0!==e.password&&(s.password=e.password),void 0!==e.phone&&(s.PhoneNumber=e.phone),void 0!==e.address&&(s.Address=e.address),void 0!==e.businessType&&(s.BusinessType=e.businessType),s},J=e=>{const s={id:e.id,name:e.name,email:e.email,type:e.type,status:e.status,verificationStatus:e.verificationStatus||"pending"};return void 0!==e.avatar&&(s.avatar=e.avatar),void 0!==e.phone&&(s.phone=e.phone),void 0!==e.address&&(s.address=e.address),void 0!==e.businessType&&(s.businessType=e.businessType),void 0!==e.lastLogin&&(s.lastLogin=e.lastLogin),s},Y=e=>{if(!e)throw new Error("Empty response received");if("object"!==typeof e)throw new Error("Invalid response format");if(!("success"in e))throw new Error("Response missing success field");if(!1===e.success)throw new Error(e.message||"Request failed");if(!("data"in e))throw new Error("Response missing data field");return e},K=e=>{const s=Y(e),t=J(s.data);return{success:s.success,message:s.message,data:t}},Q=e=>"string"===typeof e?e:null!==e&&void 0!==e&&e.message?e.message:null!==e&&void 0!==e&&e.error?e.error:"An unexpected error occurred",X={getUsers:async e=>{try{const s=e?(e=>{const s={};return void 0!==e.page&&(s.page=e.page),void 0!==e.limit&&(s.limit=e.limit),void 0!==e.search&&(s.search=e.search),void 0!==e.status&&(s.status=e.status),void 0!==e.sort&&(s.sort=e.sort),void 0!==e.order&&(s.order=e.order),s})(e):{},t=await d.A.get(W.BASE,{params:s});if(t.error)throw new Error(t.error);if(!t.data)throw new Error("No response data received");return(e=>{const s=Y(e),t=s.data.map(J),r={success:s.success,message:s.message,data:t};return void 0!==s.pagination&&(r.pagination=s.pagination),r})(t.data)}catch(t){var s;if(null!==(s=t.response)&&void 0!==s&&s.data){const e=Q(t.response.data);throw new Error(e)}throw(0,o.hS)(t)}},getUserById:async e=>{try{const s=await d.A.get(W.DETAILS(e));if(s.error)throw new Error(s.error);if(!s.data)throw new Error("No response data received");return K(s.data).data}catch(t){var s;if(null!==(s=t.response)&&void 0!==s&&s.data){const e=Q(t.response.data);throw new Error(e)}throw(0,o.hS)(t)}},createUser:async e=>{try{const s=_(e),t=await d.A.post(W.BASE,s);if(t.error)throw new Error(t.error);if(!t.data)throw new Error("No response data received");return K(t.data).data}catch(t){var s;if(null!==(s=t.response)&&void 0!==s&&s.data){const e=Q(t.response.data);throw new Error(e)}throw(0,o.hS)(t)}},updateUser:async(e,s)=>{try{const t=_(s),r=await d.A.put(W.DETAILS(e),t);if(r.error)throw new Error(r.error);if(!r.data)throw new Error("No response data received");return K(r.data).data}catch(r){var t;if(null!==(t=r.response)&&void 0!==t&&t.data){const e=Q(r.response.data);throw new Error(e)}throw(0,o.hS)(r)}},deleteUser:async e=>{try{const s=await d.A.delete(W.DETAILS(e));if(s.error)throw new Error(s.error);if(s.data&&"object"===typeof s.data&&"success"in s.data&&!s.data.success){const e="message"in s.data?s.data.message:"Delete operation failed";throw new Error(e)}}catch(t){var s;if(null!==(s=t.response)&&void 0!==s&&s.data){const e=Q(t.response.data);throw new Error(e)}throw(0,o.hS)(t)}},updateUserStatus:async(e,s)=>{try{const t={status:s},r=await d.A.put(W.STATUS(e),t);if(r.error)throw new Error(r.error);if(r.data&&"object"===typeof r.data&&"success"in r.data&&!r.data.success){const e="message"in r.data?r.data.message:"Status update failed";throw new Error(e)}}catch(r){var t;if(null!==(t=r.response)&&void 0!==t&&t.data){const e=Q(r.response.data);throw new Error(e)}throw(0,o.hS)(r)}},searchUsers:async(e,s)=>{try{const t={...s,search:e};return await X.getUsers(t)}catch(t){throw(0,o.hS)(t)}},getUsersByType:async(e,s)=>{try{const e={...s};return await X.getUsers(e)}catch(t){throw(0,o.hS)(t)}},uploadUserImage:async e=>{try{const s=new FormData;s.append("image",e);const t=await d.A.post(W.UPLOAD_IMAGE,s,{headers:{"Content-Type":"multipart/form-data"}});if(t.error)throw new Error(t.error);if(!t.data)throw new Error("No response data received");return Y(t.data).data}catch(t){var s;if(null!==(s=t.response)&&void 0!==s&&s.data){const e=Q(t.response.data);throw new Error(e)}throw(0,o.hS)(t)}}},{getUsers:ee,getUserById:se,createUser:te,updateUser:re,deleteUser:ae,updateUserStatus:ie,searchUsers:ne,getUsersByType:le,uploadUserImage:de}=X,oe=X;var ce=t(9705);const me=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{initialFetch:!0};(0,r.useEffect)((()=>{d.A.clearCache()}),[]);const s={getAll:async()=>(await oe.getUsers()).data,getById:oe.getUserById,create:oe.createUser,update:oe.updateUser,delete:oe.deleteUser},t=(0,G.B)(s,{entityName:"users",initialFetch:e.initialFetch}),{showNotification:a}=(0,ce.A)(),i=(0,r.useRef)(a);(0,r.useEffect)((()=>{i.current=a}));const n=(0,r.useCallback)((async(e,s)=>{try{await oe.updateUserStatus(e,s);const r=t.entities.map((t=>t.id===e?{...t,status:s}:t));t.setEntities(r),i.current({type:"success",title:"Success",message:`User ${"active"===s?"activated":"banned"} successfully`})}catch(r){throw i.current({type:"error",title:"Error",message:`Failed to ${"active"===s?"activate":"ban"} user`}),r}}),[t]),l=(0,r.useCallback)((async(e,s)=>{try{const r=await oe.updateUser(e,s),a=t.entities.map((s=>s.id===e?r:s));return t.setEntities(a),i.current({type:"success",title:"Success",message:"User updated successfully"}),r}catch(r){throw i.current({type:"error",title:"Error",message:"Failed to update user"}),r}}),[t]),o=(0,r.useCallback)((async(e,s)=>{try{return await oe.searchUsers(e,s)}catch(t){throw i.current({type:"error",title:"Error",message:"Failed to search users"}),t}}),[]),c=(0,r.useCallback)((async e=>{try{return await oe.getUsers(e)}catch(s){throw i.current({type:"error",title:"Error",message:"Failed to fetch users"}),s}}),[]);return{...t,users:t.entities,fetchUsers:t.fetchEntities,getUserById:t.getEntityById,createEntity:t.createEntity,deleteEntity:t.deleteEntity,updateUserStatus:n,updateUser:l,searchUsers:o,getUsersWithPagination:c}};t(6953)},5901:(e,s,t)=>{t.d(s,{A:()=>a});t(5043);var r=t(579);const a=e=>{let{label:s,name:t,type:a="text",value:i,onChange:n,error:l,required:d=!1,placeholder:o="",options:c=[],className:m="",disabled:u=!1,loading:p=!1}=e;const x="mt-1 block w-full rounded-md shadow-sm sm:text-sm "+(l?"border-red-300 focus:border-red-500 focus:ring-red-500":"border-gray-300 focus:border-primary focus:ring-primary");return(0,r.jsxs)("div",{className:`${m}`,children:[(0,r.jsxs)("label",{htmlFor:t,className:"block text-sm font-medium text-gray-700",children:[s," ",d&&(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(()=>{switch(a){case"textarea":return(0,r.jsx)("textarea",{id:t,name:t,value:i,onChange:n,className:x,placeholder:o,disabled:u});case"select":return(0,r.jsx)("select",{id:t,name:t,value:i,onChange:n,className:x,disabled:u||p,children:p?(0,r.jsx)("option",{value:"",children:"Loading..."}):c.map((e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value)))});case"checkbox":return(0,r.jsx)("input",{type:"checkbox",id:t,name:t,checked:i,onChange:n,className:"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary",disabled:u});default:return(0,r.jsx)("input",{type:a,id:t,name:t,value:i,onChange:n,className:x,placeholder:o,disabled:u})}})(),l&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:l})]})}},6887:(e,s,t)=>{t.d(s,{A:()=>a});t(5043);var r=t(579);const a=e=>{let{title:s,description:t,children:a,className:i=""}=e;return(0,r.jsxs)("div",{className:`bg-white shadow overflow-hidden sm:rounded-lg ${i}`,children:[(0,r.jsxs)("div",{className:"px-4 py-5 sm:px-6",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:s}),t&&(0,r.jsx)("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:t})]}),(0,r.jsx)("div",{className:"border-t border-gray-200",children:a})]})}},7147:(e,s,t)=>{t.d(s,{A:()=>d});var r=t(5043),a=t(7591),i=t(1602),n=t(4703),l=t(579);const d=e=>{let{label:s,name:t,value:d,onChange:o,error:c,required:m=!1,disabled:u=!1,maxSize:p=5242880,allowedTypes:x=["image/jpeg","image/png","image/gif","image/webp"],className:h=""}=e;const[g,y]=(0,r.useState)(!1),[v,b]=(0,r.useState)(null),j=(0,r.useRef)(null);r.useEffect((()=>{if(d instanceof File){const e=URL.createObjectURL(d);return b(e),()=>URL.revokeObjectURL(e)}return"string"===typeof d&&d?void b(d):void b(null)}),[d]);const f=(0,r.useCallback)((e=>{const s=(0,n.nJ)(e,{maxSize:p,allowedTypes:x});s.valid?o(e):console.error("File validation failed:",s.error)}),[p,x,o]);return(0,l.jsxs)("div",{className:h,children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[s," ",m&&(0,l.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,l.jsxs)("div",{className:`\n          relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors\n          ${g?"border-primary bg-primary bg-opacity-5":"border-gray-300"}\n          ${c?"border-red-300":""}\n          ${u?"opacity-50 cursor-not-allowed":"hover:border-primary hover:bg-gray-50"}\n        `,onDragOver:e=>{e.preventDefault(),u||y(!0)},onDragLeave:e=>{e.preventDefault(),y(!1)},onDrop:e=>{var s;if(e.preventDefault(),y(!1),u)return;const t=null===(s=e.dataTransfer.files)||void 0===s?void 0:s[0];t&&f(t)},onClick:()=>{!u&&j.current&&j.current.click()},children:[(0,l.jsx)("input",{ref:j,type:"file",name:t,accept:x.join(","),onChange:e=>{var s;const t=null===(s=e.target.files)||void 0===s?void 0:s[0];t&&f(t)},className:"hidden",disabled:u}),v?(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("img",{src:v,alt:"Preview",className:"mx-auto h-32 w-32 object-cover rounded-lg"}),!u&&(0,l.jsx)("button",{type:"button",onClick:e=>{e.stopPropagation(),o(null),j.current&&(j.current.value="")},className:"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors",children:(0,l.jsx)(a.A,{className:"h-4 w-4"})})]}):(0,l.jsxs)("div",{children:[(0,l.jsx)(i.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,l.jsxs)("div",{className:"mt-4",children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:g?"Drop image here":"Click to upload or drag and drop"}),(0,l.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["PNG, JPG, GIF up to ",Math.round(p/1024/1024),"MB"]})]})]})]}),c&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:c})]})}},7174:(e,s,t)=>{t.d(s,{HW:()=>n,O1:()=>i,OL:()=>a});var r=t(7304);r.t1.register(r.PP,r.kc,r.FN,r.No,r.E8,r.Bs,r.pr,r.hE,r.m_,r.s$,r.dN);const a={responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,color:"#000000",font:{size:16,weight:"bold"}},legend:{display:!1,labels:{color:"#000000"}},tooltip:{backgroundColor:"#F28B22",titleColor:"#FFFFFF",bodyColor:"#FFFFFF",padding:12,displayColors:!1}},scales:{x:{grid:{display:!1},ticks:{color:"#000000"}},y:{beginAtZero:!0,grid:{display:!0,color:"#E5E7EB"},ticks:{color:"#000000"}}}},i={responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,color:"#000000",font:{size:16,weight:"bold"}},legend:{position:"bottom",labels:{color:"#000000",font:{size:12}}},tooltip:{backgroundColor:"#F28B22",titleColor:"#FFFFFF",bodyColor:"#FFFFFF",padding:12}}},n=e=>{e&&e.destroy()}},8682:(e,s,t)=>{t.d(s,{A:()=>i});var r=t(5043);function a(e,s){let{title:t,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},i),t?r.createElement("title",{id:a},t):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))}const i=r.forwardRef(a)}}]);
//# sourceMappingURL=892.8e5a2e8f.chunk.js.map