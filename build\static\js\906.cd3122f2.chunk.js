"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[906],{6906:(e,t,s)=>{s.d(t,{getMockOrderById:()=>a,getMockOrders:()=>n,mapMockOrderToOrder:()=>i});var r=s(4624);const d=e=>({id:e.id,name:e.productName,quantity:e.quantity,unitPrice:e.unitPrice,description:`Product ID: ${e.productId}`,sku:e.productId}),i=e=>{const t=e=>e?new Date(e).toISOString():(new Date).toISOString();return{id:e.id,customerName:e.customerName,supplierName:e.supplierName,totalAmount:e.totalAmount,status:"cancelled"===e.status?"rejected":e.status,orderDate:t(e.orderDate),deliveryDate:t(e.deliveryDate),items:e.items?e.items.map(d):[],notes:e.notes,...e.shippingAddress&&{shippingAddress:{street:e.shippingAddress.street,city:e.shippingAddress.city,state:e.shippingAddress.state,postalCode:e.shippingAddress.zipCode,country:e.shippingAddress.country}}}},n=()=>r.W.map(i),a=e=>{const t=String(e),s=r.W.find((e=>e.orderNumber===t||e.id===t||String(e.id)===t));if(s)return i(s)}}}]);
//# sourceMappingURL=906.cd3122f2.chunk.js.map