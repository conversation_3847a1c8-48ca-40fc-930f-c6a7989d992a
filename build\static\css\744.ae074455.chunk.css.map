{"version": 3, "file": "static/css/744.ae074455.chunk.css", "mappings": "AAGA,uBACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CAGA,sBACE,UAEE,UAAY,CADZ,mBAEF,CACA,IAEE,SAAU,CADV,oBAEF,CACF,CAGA,wBACE,MACE,UAAY,CACZ,mBACF,CACA,IACE,SAAU,CACV,kBACF,CACF,CAGA,kBACE,GAEE,SAAU,CADV,mBAEF,CACA,GAEE,SAAU,CADV,kBAEF,CACF,CAGA,gBACE,wCACF,CAEA,kBACE,mDACF,CAEA,8BAAiC,qBAAyB,CAC1D,+BAAiC,qBAAyB,CAC1D,+BAAiC,kBAAqB,CAEtD,cACE,8CACF,CAEA,eACE,iBACF,CAEA,sBASE,uCAAwC,CADxC,gBAA8B,CAD9B,iBAAkB,CADlB,QAAS,CALT,UAAW,CAGX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAOF", "sources": ["components/common/LoadingSpinner.css"], "sourcesContent": ["/* Loading Spinner Animations */\n\n/* Smooth spinner rotation */\n@keyframes spin-smooth {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n/* Bouncing dots with staggered timing */\n@keyframes bounce-dot {\n  0%, 80%, 100% {\n    transform: scale(0.8);\n    opacity: 0.5;\n  }\n  40% {\n    transform: scale(1.2);\n    opacity: 1;\n  }\n}\n\n/* Smooth pulse animation */\n@keyframes pulse-smooth {\n  0%, 100% {\n    opacity: 0.4;\n    transform: scale(0.8);\n  }\n  50% {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n\n/* Ripple effect */\n@keyframes ripple {\n  0% {\n    transform: scale(0.8);\n    opacity: 1;\n  }\n  100% {\n    transform: scale(2);\n    opacity: 0;\n  }\n}\n\n/* Apply custom animations */\n.spinner-smooth {\n  animation: spin-smooth 1s linear infinite;\n}\n\n.dots-bounce .dot {\n  animation: bounce-dot 1.4s ease-in-out infinite both;\n}\n\n.dots-bounce .dot:nth-child(1) { animation-delay: -0.32s; }\n.dots-bounce .dot:nth-child(2) { animation-delay: -0.16s; }\n.dots-bounce .dot:nth-child(3) { animation-delay: 0s; }\n\n.pulse-smooth {\n  animation: pulse-smooth 2s ease-in-out infinite;\n}\n\n.ripple-effect {\n  position: relative;\n}\n\n.ripple-effect::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border-radius: 50%;\n  border: 2px solid currentColor;\n  animation: ripple 1.5s ease-out infinite;\n}\n"], "names": [], "sourceRoot": ""}