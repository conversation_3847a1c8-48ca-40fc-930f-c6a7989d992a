{"version": 3, "file": "static/js/814.5630d42e.chunk.js", "mappings": "oKA+BO,MAuMP,EAvM+B,WAA2C,IAA1CA,EAA+BC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EACjE,MAAM,oBAAEG,GAAsB,EAAI,gBAAEC,GAAkB,EAAI,QAAEC,GAAYN,GAClE,iBAAEO,IAAqBC,EAAAA,EAAAA,MAEtBC,EAAYC,IAAiBC,EAAAA,EAAAA,UAAqB,CACvDC,UAAU,EACVC,MAAO,KACPC,UAAW,OAIPC,GAAaC,EAAAA,EAAAA,cAAY,KAC7BN,EAAc,CACZE,UAAU,EACVC,MAAO,KACPC,UAAW,MACX,GACD,IAGGG,GAA0BD,EAAAA,EAAAA,cAAY,CAACH,EAAYK,KACvD,MAAMC,GAAWC,EAAAA,EAAAA,IACfP,EACAT,EAAuBiB,IACrBd,EAAiB,CACfe,KAAMD,EAAaC,KACnBC,MAAOF,EAAaE,MACpBC,QAASH,EAAaG,SACtB,OACArB,GAkBN,OAfAO,EAAc,CACZE,UAAU,EACVC,MAAOM,EACPL,UAAW,SACPI,GAAW,CAAEA,aAGfb,GAAmBQ,aAAiBY,QACtCC,EAAAA,EAAAA,IAAYb,EAAOK,GAGjBZ,GACFA,EAAQO,EAAOK,GAGVC,CAAQ,GACd,CAACf,EAAqBC,EAAiBE,EAAkBD,IAGtDqB,GAAiCX,EAAAA,EAAAA,cAAY,CACjDY,EACAJ,EACAK,EACAX,KAEA,MAAMY,GAAkBC,EAAAA,EAAAA,IAAsBH,EAAOJ,EAASK,GAqB9D,OAnBAnB,EAAc,CACZE,UAAU,EACVC,MAAOiB,EACPhB,UAAW,gBACPI,GAAW,CAAEA,aAGfd,GACFG,EAAiB,CACfe,KAAM,QACNC,MAAO,mBACPC,QAASM,EAAgBN,UAIzBlB,GACFA,EAAQwB,EAAiBZ,GAGpBY,CAAe,GACrB,CAAC1B,EAAqBG,EAAkBD,IAGrC0B,GAA2BhB,EAAAA,EAAAA,cAAY,CAC3CH,EACAoB,EACAf,MAEAgB,EAAAA,EAAAA,IACErB,EACAoB,EACA7B,EAAuBiB,IACrBd,EAAiB,CACfe,KAAMD,EAAaC,KACnBC,MAAOF,EAAaE,MACpBC,QAASH,EAAaG,SACtB,OACArB,GAGNO,EAAc,CACZE,UAAU,EACVC,QACAC,UAAW,UACPI,GAAW,CAAEA,aAGfb,GAAmBQ,aAAiBY,QACtCC,EAAAA,EAAAA,IAAYb,EAAOK,GAGjBZ,GACFA,EAAQO,EAAOK,EACjB,GACC,CAACd,EAAqBC,EAAiBE,EAAkBD,IAGtD6B,GAAqBnB,EAAAA,EAAAA,cAAY,CAACH,EAAYK,KAClD,MAAMkB,EAAWvB,aAAiBY,MAAQZ,EAAQ,IAAIY,MAAMY,OAAOxB,IA2BnE,OAzBAH,EAAc,CACZE,UAAU,EACVC,MAAOuB,EACPtB,UAAW,aACPI,GAAW,CAAEA,aAGfd,GACFG,EAAiB,CACfe,KAAM,QACNC,MAAO,QACPC,QAASY,EAASZ,UAIlBnB,IACFqB,EAAAA,EAAAA,IAAYU,EAAUlB,IAGxBoB,EAAAA,EAAAA,IAASF,EAAUlB,GAEfZ,GACFA,EAAQO,EAAOK,GAGVkB,CAAQ,GACd,CAAChC,EAAqBC,EAAiBE,EAAkBD,IAGtDiC,GAAoBvB,EAAAA,EAAAA,cAAYwB,MACpCC,EACAvB,KAEA,IAEE,OADAH,UACa0B,GACf,CAAE,MAAO5B,GAEP,OADAI,EAAwBJ,EAAOK,GACxB,IACT,IACC,CAACH,EAAYE,IAGVyB,GAAwB1B,EAAAA,EAAAA,cAAYwB,MACxCC,EACAR,EACAf,KAEA,IAEE,OADAH,UACa0B,GACf,CAAE,MAAO5B,GAEP,OADAmB,EAAyBnB,EAAOoB,EAAef,GACxC,IACT,IACC,CAACH,EAAYiB,IAEhB,MAAO,IAEFvB,EAGHW,eAAgBH,EAChBc,sBAAuBJ,EACvBO,gBAAiBF,EACjBG,qBACApB,aAGAwB,oBACAG,wBAGAC,WAAa9B,GACXA,GAA0B,kBAAVA,GAAsB,WAAYA,EACpD+B,kBAAoB/B,GAClBA,GAA0B,kBAAVA,GAAsB,UAAWA,EAEvD,C,gDCnOA,SAASgC,EAASC,EAIfC,GAAQ,IAJQ,MACjBxB,EAAK,QACLyB,KACGC,GACJH,EACC,OAAoBI,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ1B,EAAqB2B,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHzB,GAAS,KAAmB2B,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,4TAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBL,E,yDCGlD,MAAMkB,EAA4BjB,IAgB3B,IAhB4B,MACjCvB,EAAK,SACLyC,EAAQ,SACRC,EAAQ,UACRC,EAAY,GAAE,cACdC,EAAgB,GAAE,gBAClBC,EAAkB,GAAE,gBACpBC,EAAkB,GAAE,KACpBC,EAAI,OACJC,EAAM,QACNC,EAAO,UACPC,GAAY,EAAK,UACjBC,GAAY,EAAK,SACjBC,GAAW,EAAI,QACfC,GAAU,EAAK,OACfC,GACD/B,EAEC,MAAMgC,EAAc,6BACIH,EAAW,yBAA2B,uDAC1DF,EAAY,uEAAyE,oBACrFD,EAAU,iBAAmB,WAC7BN,QAIEa,EAAgB,mFAElBX,QAIEY,EAAc,SAChBN,EAAY,GAAK,cACjBP,QAIEc,EAAgB,4DAElBZ,QAIJ,OAAIO,GAEAM,EAAAA,EAAAA,MAAA,OAAKhB,UAAWY,EAAa,cAAaD,EAAOZ,SAAA,EAC7C1C,GAASyC,GAAYM,KACrBY,EAAAA,EAAAA,MAAA,OAAKhB,UAAWa,EAAcd,SAAA,EAC5BiB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,SAAQD,SAAA,CACpB1C,IAAS4D,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,gDACxBF,IAAYmB,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,wDAE7BI,IAAQa,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,uDAI5BiB,EAAAA,EAAAA,KAAA,OAAKjB,UAAWc,EAAYf,UAC1BkB,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,6CAGhBK,IACCY,EAAAA,EAAAA,KAAA,OAAKjB,UAAWe,EAAchB,UAC5BkB,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,sDAQvBgB,EAAAA,EAAAA,MAAA,OACEhB,UAAWY,EACXN,QAASA,EACT,cAAaK,EAAOZ,SAAA,EAElB1C,GAASyC,GAAYM,KACrBY,EAAAA,EAAAA,MAAA,OAAKhB,UAAWa,EAAcd,SAAA,EAC5BiB,EAAAA,EAAAA,MAAA,OAAAjB,SAAA,CACoB,kBAAV1C,GACN4D,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,qCAAoCD,SAAE1C,IAEpDA,EAEmB,kBAAbyC,GACNmB,EAAAA,EAAAA,KAAA,KAAGjB,UAAU,6BAA4BD,SAAED,IAE3CA,KAGHM,IAAQa,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,eAAcD,SAAEK,QAI5Ca,EAAAA,EAAAA,KAAA,OAAKjB,UAAWc,EAAYf,SAAEA,IAE7BM,IACCY,EAAAA,EAAAA,KAAA,OAAKjB,UAAWe,EAAchB,SAC3BM,MAGD,EAIV,GAAea,EAAAA,EAAAA,MAAKrB,E,uDCnHpB,MA+FA,EA/F4CjB,IAarC,IAbsC,MAC3CuC,EAAK,KACLC,EAAI,KACJhE,EAAO,OAAM,MACbiE,EAAK,SACLC,EAAQ,MACR3E,EAAK,SACL4E,GAAW,EAAK,YAChBC,EAAc,GAAE,QAChB1F,EAAU,GAAE,UACZkE,EAAY,GAAE,SACdyB,GAAW,EAAK,QAChBf,GAAU,GACX9B,EACC,MAAM8C,EAAe,sDACnB/E,EAAQ,yDAA2D,2DAqErE,OACEqE,EAAAA,EAAAA,MAAA,OAAKhB,UAAW,GAAGA,IAAYD,SAAA,EAC7BiB,EAAAA,EAAAA,MAAA,SAAOW,QAASP,EAAMpB,UAAU,0CAAyCD,SAAA,CACtEoB,EAAM,IAAEI,IAAYN,EAAAA,EAAAA,KAAA,QAAMjB,UAAU,eAAcD,SAAC,SArEtC6B,MAClB,OAAQxE,GACN,IAAK,WACH,OACE6D,EAAAA,EAAAA,KAAA,YACExB,GAAI2B,EACJA,KAAMA,EACNC,MAAOA,EACPC,SAAUA,EACVtB,UAAW0B,EACXF,YAAaA,EACbC,SAAUA,IAIhB,IAAK,SACH,OACER,EAAAA,EAAAA,KAAA,UACExB,GAAI2B,EACJA,KAAMA,EACNC,MAAOA,EACPC,SAAUA,EACVtB,UAAW0B,EACXD,SAAUA,GAAYf,EAAQX,SAE7BW,GACCO,EAAAA,EAAAA,KAAA,UAAQI,MAAM,GAAEtB,SAAC,eAEjBjE,EAAQ+F,KAAIC,IACVb,EAAAA,EAAAA,KAAA,UAA2BI,MAAOS,EAAOT,MAAMtB,SAC5C+B,EAAOX,OADGW,EAAOT,WAQ9B,IAAK,WACH,OACEJ,EAAAA,EAAAA,KAAA,SACE7D,KAAK,WACLqC,GAAI2B,EACJA,KAAMA,EACNW,QAASV,EACTC,SAAUA,EACVtB,UAAU,kEACVyB,SAAUA,IAIhB,QACE,OACER,EAAAA,EAAAA,KAAA,SACE7D,KAAMA,EACNqC,GAAI2B,EACJA,KAAMA,EACNC,MAAOA,EACPC,SAAUA,EACVtB,UAAW0B,EACXF,YAAaA,EACbC,SAAUA,IAGlB,EAQGG,GACAjF,IAASsE,EAAAA,EAAAA,KAAA,KAAGjB,UAAU,4BAA2BD,SAAEpD,MAChD,C,2CC5FH,MAAMqF,EAAgBC,GACR,mDACDC,KAAKD,GAGZE,EAAgBC,GACR,oBACDF,KAAKE,GAGZC,EAAcC,IACzB,IAEE,OADA,IAAIC,IAAID,IACD,CACT,CAAE,MAAO3F,GACP,OAAO,CACT,GAGW6F,EAAcnB,GACX,OAAVA,QAA4BpF,IAAVoF,IACD,kBAAVA,EAA2BA,EAAMoB,OAAOzG,OAAS,GACxD0G,MAAMC,QAAQtB,IAAeA,EAAMrF,OAAS,GAYrC4G,EAAavB,GACjB,WAAWa,KAAKb,GAGZwB,EAAaxB,GACjB,sBAAsBa,KAAKb,GAGvByB,EAAkBzB,GACtB,iBAAiBa,KAAKb,GAGlB0B,EAAeC,IAC1B,MAAMC,EAAO,IAAIC,KAAKF,GACtB,OAAQG,MAAMF,EAAKG,UAAU,EAGlBC,EAAmBA,CAACC,EAAkBC,IAC1CD,IAAaC,EAGTC,EAAoBF,KAE3BA,EAAStH,OAAS,OAGjB,QAAQkG,KAAKoB,OAGb,QAAQpB,KAAKoB,OAGb,QAAQpB,KAAKoB,MAGb,sCAAsCpB,KAAKoB,MAwBrCG,EAAeA,CAC1BC,EACAC,KAEA,MAAMC,EAA2C,CAAC,EAUlD,OARA3E,OAAO4E,QAAQF,GAAiBG,SAAQlF,IAAyB,IAAvBmF,EAAWC,GAAMpF,EACzD,MAAMqF,EAAMF,EACNpH,EA1BmBuH,EAC3BC,EACA9C,EACA2C,EACAI,KAEA,MAAMC,EAAY3B,MAAMC,QAAQqB,GAASA,EAAQ,CAACA,GAElD,IAAK,MAAMM,KAAQD,EACjB,IAAKC,EAAKC,UAAUlD,EAAO+C,GACzB,OAAOE,EAAKhH,QAIhB,MAAO,EAAE,EAYO4G,CAAcH,EAAWL,EAAOO,GAAMD,EAAON,GACvD/G,IACFiH,EAAOK,GAAOtH,EAChB,IAGKiH,CAAM,EAIFD,EAAkB,CAC7BpC,SAAU,WAA2C,MAAsB,CACzEgD,UAAW/B,EACXlF,QAFwBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,yBAG5B,EAEDkG,MAAO,WAAuD,MAAsB,CAClFsC,UAAWvC,EACX1E,QAFqBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,qCAGzB,EAEDqG,MAAO,WAAsD,MAAsB,CACjFmC,UAAWpC,EACX7E,QAFqBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,oCAGzB,EAEDuG,IAAK,WAA6C,MAAsB,CACtEiC,UAAWlC,EACX/E,QAFmBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,2BAGvB,EAEDyI,UAAWA,CAACC,EAAanH,KAAgB,CACvCiH,UAAYlD,GA3GSmD,EAACnD,EAAeoD,IAChCpD,EAAMrF,QAAUyI,EA0GSD,CAAUnD,EAAOoD,GAC/CnH,QAASA,GAAW,oBAAoBmH,iBAG1CC,UAAWA,CAACC,EAAarH,KAAgB,CACvCiH,UAAYlD,GA5GSqD,EAACrD,EAAesD,IAChCtD,EAAMrF,QAAU2I,EA2GSD,CAAUrD,EAAOsD,GAC/CrH,QAASA,GAAW,wBAAwBqH,iBAG9CC,QAAS,WAAiD,MAAsB,CAC9EL,UAAW3B,EACXtF,QAFuBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,+BAG3B,EAED8I,QAAS,WAAwD,MAAsB,CACrFN,UAAW1B,EACXvF,QAFuBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,sCAG3B,EAED+I,aAAc,WAAwD,MAAsB,CAC1FP,UAAWzB,EACXxF,QAF4BvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,sCAGhC,EAEDkH,KAAM,WAA8C,MAAsB,CACxEsB,UAAWxB,EACXzF,QAFoBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,4BAGxB,EAEDuH,SAAU,WAA2H,MAAsB,CACzJiB,UAAWf,EACXlG,QAFwBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,yGAG5B,EAEDgJ,cAAe,WAA2C,MAAsB,CAC9ER,UAAWA,CAAClD,EAAe+C,IAAmBf,EAAiBhC,EAAe,OAAR+C,QAAQ,IAARA,OAAQ,EAARA,EAAUb,iBAChFjG,QAF6BvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,yBAGjC,EAEDiJ,qBAAsB,WAA2C,MAAsB,CACrFT,UAAWA,CAAClD,EAAe+C,IAAmBf,EAAiBhC,EAAe,OAAR+C,QAAQ,IAARA,OAAQ,EAARA,EAAUd,UAChFhG,QAFoCvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,yBAGxC,EAGDkJ,IAAK,WAA6C,MAAsB,CACtEV,UAAYlD,GAAkB,sBAAsBa,KAAKb,GACzD/D,QAFmBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,2BAGvB,EAEDmJ,MAAO,WAA+C,MAAsB,CAC1EX,UAAYlD,GAAkBA,EAAQ,GAAKA,GAAS,OACpD/D,QAFqBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,6BAGzB,EAEDoJ,MAAO,WAAwD,MAAsB,CACnFZ,UAAYlD,GAAkB+D,OAAOC,UAAUhE,IAAUA,GAAS,EAClE/D,QAFqBvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,sCAGzB,EAEDuJ,aAAc,WAA6D,MAAsB,CAC/Ff,UAAYlD,GAAkB+D,OAAOC,UAAUhE,IAAUA,GAAS,EAClE/D,QAF4BvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,2CAGhC,EAEDwJ,iBAAkB,WAAuE,MAAsB,CAC7GhB,UAAWA,CAACe,EAAsBlB,KAC3BA,IAAaA,EAASe,OACpBG,GAAgBlB,EAASe,MAElC7H,QALgCvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,qDAMpC,EAEDyJ,cAAe,WAAkD,MAAsB,CACrFjB,UAAYlD,GAAiBqB,MAAMC,QAAQtB,IAAUA,EAAMrF,OAAS,EACpEsB,QAF6BvB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,gCAGjC,EAED0J,WAAY,eAACC,EAAgB3J,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GAAoB,MAAsB,CACxEwI,UAAYlD,KACLqB,MAAMC,QAAQtB,IACZA,EAAMrF,QAAU0J,EAEzBpI,SALkDvB,UAAAC,OAAA,EAAAD,UAAA,QAAAE,IAK9B,WAAWyJ,mBAChC,E,uFClNH,MA6JA,EA7JgD9G,IAWzC,IAX0C,MAC/CuC,EAAK,KACLC,EAAI,MACJC,EAAK,SACLC,EAAQ,MACR3E,EAAK,SACL4E,GAAW,EAAK,SAChBE,GAAW,EAAK,QAChBkE,EAAU,QAAe,aACzBC,EAAe,CAAC,aAAc,YAAa,YAAa,cAAa,UACrE5F,EAAY,IACbpB,EACC,MAAOiH,EAAYC,IAAiBrJ,EAAAA,EAAAA,WAAS,IACtCsJ,EAASC,IAAcvJ,EAAAA,EAAAA,UAAwB,MAChDwJ,GAAeC,EAAAA,EAAAA,QAAyB,MAG9ClH,EAAAA,WAAgB,KACd,GAAIqC,aAAiB8E,KAAM,CACzB,MAAM7D,EAAMC,IAAI6D,gBAAgB/E,GAEhC,OADA2E,EAAW1D,GACJ,IAAMC,IAAI8D,gBAAgB/D,EACnC,CAAO,MAAqB,kBAAVjB,GAAsBA,OACtC2E,EAAW3E,QAGX2E,EAAW,KAEb,GACC,CAAC3E,IAEJ,MAAMiF,GAAmBxJ,EAAAA,EAAAA,cAAayJ,IACpC,MAAMC,GAAaC,EAAAA,EAAAA,IAAaF,EAAM,CACpCZ,UACAC,iBAGEY,EAAWE,MACbpF,EAASiF,GAGTI,QAAQhK,MAAM,0BAA2B6J,EAAW7J,MACtD,GACC,CAACgJ,EAASC,EAActE,IA8C3B,OACEN,EAAAA,EAAAA,MAAA,OAAKhB,UAAWA,EAAUD,SAAA,EACxBiB,EAAAA,EAAAA,MAAA,SAAOhB,UAAU,+CAA8CD,SAAA,CAC5DoB,EAAM,IAAEI,IAAYN,EAAAA,EAAAA,KAAA,QAAMjB,UAAU,eAAcD,SAAC,UAGtDiB,EAAAA,EAAAA,MAAA,OACEhB,UAAW,sHAEP6F,EAAa,yCAA2C,gCACxDlJ,EAAQ,iBAAmB,iBAC3B8E,EAAW,gCAAkC,oDAEjDmF,WAlDkBC,IACtBA,EAAEC,iBACGrF,GACHqE,GAAc,EAChB,EA+CIiB,YA5CmBF,IACvBA,EAAEC,iBACFhB,GAAc,EAAM,EA2ChBkB,OAxCcH,IAAwB,IAADI,EAIzC,GAHAJ,EAAEC,iBACFhB,GAAc,GAEVrE,EAAU,OAEd,MAAM8E,EAA2B,QAAvBU,EAAGJ,EAAEK,aAAaC,aAAK,IAAAF,OAAA,EAApBA,EAAuB,GAChCV,GACFD,EAAiBC,EACnB,EAgCIjG,QAtBc8G,MACb3F,GAAYwE,EAAaoB,SAC5BpB,EAAaoB,QAAQC,OACvB,EAmByBvH,SAAA,EAErBkB,EAAAA,EAAAA,KAAA,SACEzB,IAAKyG,EACL7I,KAAK,OACLgE,KAAMA,EACNmG,OAAQ3B,EAAa4B,KAAK,KAC1BlG,SAnEuBuF,IAA4C,IAADY,EACxE,MAAMlB,EAAqB,QAAjBkB,EAAGZ,EAAEa,OAAOP,aAAK,IAAAM,OAAA,EAAdA,EAAiB,GAC1BlB,GACFD,EAAiBC,EACnB,EAgEMvG,UAAU,SACVyB,SAAUA,IAGXsE,GACC/E,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,WAAUD,SAAA,EACvBkB,EAAAA,EAAAA,KAAA,OACE0G,IAAK5B,EACL6B,IAAI,UACJ5H,UAAU,+CAEVyB,IACAR,EAAAA,EAAAA,KAAA,UACE7D,KAAK,SACLkD,QAAUuG,IACRA,EAAEgB,kBAnDhBvG,EAAS,MACL2E,EAAaoB,UACfpB,EAAaoB,QAAQhG,MAAQ,GAkDH,EAEhBrB,UAAU,qGAAoGD,UAE9GkB,EAAAA,EAAAA,KAAC6G,EAAAA,EAAS,CAAC9H,UAAU,kBAK3BgB,EAAAA,EAAAA,MAAA,OAAAjB,SAAA,EACEkB,EAAAA,EAAAA,KAACtC,EAAAA,EAAS,CAACqB,UAAU,qCACrBgB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,OAAMD,SAAA,EACnBkB,EAAAA,EAAAA,KAAA,KAAGjB,UAAU,wBAAuBD,SACjC8F,EAAa,kBAAoB,sCAEpC7E,EAAAA,EAAAA,MAAA,KAAGhB,UAAU,6BAA4BD,SAAA,CAAC,uBACnBgI,KAAKC,MAAMrC,EAAU,KAAO,MAAM,iBAOhEhJ,IAASsE,EAAAA,EAAAA,KAAA,KAAGjB,UAAU,4BAA2BD,SAAEpD,MAChD,C,gDC/KV,SAASsL,EAAmBrJ,EAIzBC,GAAQ,IAJkB,MAC3BxB,EAAK,QACLyB,KACGC,GACJH,EACC,OAAoBI,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ1B,EAAqB2B,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHzB,GAAS,KAAmB2B,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,kFAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBiJ,GCvBlD,SAASC,EAAatJ,EAInBC,GAAQ,IAJY,MACrBxB,EAAK,QACLyB,KACGC,GACJH,EACC,OAAoBI,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ1B,EAAqB2B,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHzB,GAAS,KAAmB2B,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,+BAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBkJ,G,2CCoBlD,SAASC,EAASvJ,GAqBK,IArB2B,QAChDwJ,EAAO,KACPC,EAAI,WACJC,EAAU,MACVjL,EAAK,YACLkL,EAAW,QACX7H,GAAU,EAAK,WACf8H,GAAa,EAAI,SACjBC,EAAWC,EAAAA,GAAOC,kBAAiB,WACnCC,GAAa,EAAI,kBACjBC,EAAiB,QACjBC,EAAO,aACPC,EAAe,mBAAkB,UACjC/I,EAAY,GAAE,gBACdE,EAAkB,GAAE,cACpBD,EAAgB,GAAE,gBAClBE,EAAkB,GAAE,aACpB6I,EAAY,eACZC,EAAc,qBACdC,EAAuB,MAAK,OAC5BvI,GACkB/B,EAElB,MAAOuK,EAAYC,IAAiB3M,EAAAA,EAAAA,UAG1BwM,EAAiB,CAAEhF,IAAKgF,EAAgBI,UAAWH,GAAyB,OAE/EI,EAAYC,IAAiB9M,EAAAA,EAAAA,UAAS,KACtC+M,EAAaC,IAAkBhN,EAAAA,EAAAA,UAAS,IACxCiN,EAAcC,IAAmBlN,EAAAA,EAAAA,UAAmB,KACpDmN,EAAYC,IAAiBpN,EAAAA,EAAAA,UAAwB,MAWtDqN,GAAaC,EAAAA,EAAAA,UAAQ,IACpBZ,EAEE,IAAId,GAAM2B,MAAK,CAACC,EAAGC,KACxB,MAAMC,EAASF,EAAEd,EAAWlF,KACtBmG,EAASF,EAAEf,EAAWlF,KAG5B,OAAc,MAAVkG,GAA4B,MAAVC,EAAuB,EAC/B,MAAVD,EAAgD,QAAzBhB,EAAWE,WAAuB,EAAI,EACnD,MAAVe,EAAgD,QAAzBjB,EAAWE,UAAsB,GAAK,EAG3C,kBAAXc,GAAyC,kBAAXC,EACP,QAAzBjB,EAAWE,UACdc,EAAOE,cAAcD,GACrBA,EAAOC,cAAcF,GAGvBA,EAASC,EACqB,QAAzBjB,EAAWE,WAAuB,EAAI,EAE3Cc,EAASC,EACqB,QAAzBjB,EAAWE,UAAsB,GAAK,EAExC,CAAC,IAxBchB,GA0BvB,CAACA,EAAMc,IAGJmB,GAAeP,EAAAA,EAAAA,UAAQ,IACtBT,EAEEQ,EAAWS,QAAQC,GACxBvL,OAAO4E,QAAQ2G,GAAKC,MAAKC,IAAoB,IAAlBC,EAAMtJ,GAAMqJ,EAErC,OAAc,OAAVrJ,QAA4BpF,IAAVoF,IACD,kBAAVA,GAEJlD,OAAOkD,GAAOuJ,cAAcC,SAASvB,EAAWsB,eAAc,MARjDd,GAWvB,CAACA,EAAYR,IAGVwB,EAAa/C,KAAKgD,KAAKT,EAAatO,OAASyM,GAC7CuC,GAAgBjB,EAAAA,EAAAA,UAAQ,KAC5B,MAAMkB,GAAczB,EAAc,GAAKf,EACvC,OAAO6B,EAAaY,MAAMD,EAAYA,EAAaxC,EAAS,GAC3D,CAAC6B,EAAcd,EAAaf,IAEzB0C,EAAoBC,IACxB3B,EAAe2B,EAAK,EA0ChBC,EAAqBC,IACzB,IAAIC,EAAU,4BAEd,GAAsB,kBAAXD,EAAqB,CAC9B,MAAME,EAAcF,EAAOV,cAEvBY,EAAYX,SAAS,WAAaW,EAAYX,SAAS,aACvDW,EAAYX,SAAS,aAAeW,EAAYX,SAAS,cACzDW,EAAYX,SAAS,WACvBU,EAAU,8BACDC,EAAYX,SAAS,YAAcW,EAAYX,SAAS,cACjEU,EAAU,gCACDC,EAAYX,SAAS,aAAeW,EAAYX,SAAS,WAC1DW,EAAYX,SAAS,WAAaW,EAAYX,SAAS,SAC/DU,EAAU,0BACDC,EAAYX,SAAS,cAC9BU,EAAU,4BAEd,CAEA,OACEtK,EAAAA,EAAAA,KAAA,QAAMjB,UAAW,2EAA2EuL,IAAUxL,SACnGuL,GACI,EAIX,OACEtK,EAAAA,EAAAA,MAAA,OACEhB,UAAW,oHAAoHA,IAC/H,cAAaW,EAAOZ,SAAA,EAGlB1C,GAASkL,KACTvH,EAAAA,EAAAA,MAAA,OAAKhB,UAAW,sCAAsCE,IAAkBH,SAAA,CACpD,kBAAV1C,GACN4D,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,sCAAqCD,SAAE1C,IAErDA,EAEsB,kBAAhBkL,GACNtH,EAAAA,EAAAA,KAAA,KAAGjB,UAAU,6BAA4BD,SAAEwI,IAE3CA,MAMNvH,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,kGAAiGD,SAAA,EAC9GiB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,kBAAiBD,SAAA,EAC9BkB,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,uEAAsED,UACnFkB,EAAAA,EAAAA,KAACgH,EAAmB,CAACjI,UAAU,6BAEjCiB,EAAAA,EAAAA,KAAA,SACE7D,KAAK,OACLoE,YAAY,YACZxB,UAAU,sMACVqB,MAAOiI,EACPhI,SAAWuF,IACT0C,EAAc1C,EAAEa,OAAOrG,OACvBoI,EAAe,EAAE,EAEnB,cAAa,GAAG9I,iBAIpBK,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,8BAA6BD,SAAA,CACzC2J,EAAa1N,OAAS,IACrBgF,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,8BAA6BD,SAAA,EAC1CiB,EAAAA,EAAAA,MAAA,QAAMhB,UAAU,wBAAuBD,SAAA,CAAE2J,EAAa1N,OAAO,gBAC7DiF,EAAAA,EAAAA,KAAA,UACEjB,UAAU,uGACVM,QAASA,KACPqJ,EAAgB,IACZd,GAAmBA,EAAkB,GAAG,EAE9C,cAAa,GAAGlI,oBAAyBZ,SAC1C,aAKJ+I,SAKL7H,EAAAA,EAAAA,KAAA,OAAKjB,UAAW,mBAAmBC,IAAgBF,SAChDW,GACCO,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,yCAAwCD,UACrDkB,EAAAA,EAAAA,KAACwK,EAAAA,EAAc,CAACC,KAAK,KAAKC,QAAQ,eAGpC3K,EAAAA,EAAAA,MAAA,SAAOhB,UAAU,sCAAqCD,SAAA,EACpDkB,EAAAA,EAAAA,KAAA,SAAOjB,UAAU,aAAYD,UAC3BiB,EAAAA,EAAAA,MAAA,MAAAjB,SAAA,CACG6I,IACC3H,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,iBAAgBD,UAC5BkB,EAAAA,EAAAA,KAAA,SACE7D,KAAK,WACL4C,UAAU,kEACVsB,SAtHKsK,IACvB,MAAMC,EAAkBD,EAAMlE,OAAO3F,QACjCW,MAAMoJ,KAAK,CAAE9P,OAAQgP,EAAchP,SAAU,CAAC+P,EAAGC,IAAMA,IACvD,GAIJ,GAFArC,EAAgBkC,GAEZhD,EAAmB,CACrB,MAAMoD,EAAgBJ,EACnBhK,KAAIqK,GAAOlB,EAAckB,KACzB3B,QAAQ4B,QAA6BlQ,IAATkQ,IAC/BtD,EAAkBoD,EACpB,GA2GkBlK,QAAS2H,EAAa1N,SAAWgP,EAAchP,QAAUgP,EAAchP,OAAS,EAChF,cAAa,GAAG2E,mBAIrByH,EAAQvG,KAAKuK,IACZnL,EAAAA,EAAAA,KAAA,MAEEjB,UAAW,kBAAkBoM,EAAOC,OAAS,qEAAqED,EAAOE,SAAW,mCAAqC,qCAAqCF,EAAOG,MAAQH,EAAOG,MAAQ,MAAMH,EAAOpM,WAAa,KACtQM,QAASA,IAAM8L,EAAOE,UAtNpBrI,KAClB,IAAIoF,EAA4B,MAC5BF,GAAcA,EAAWlF,MAAQA,GAAgC,QAAzBkF,EAAWE,YACrDA,EAAY,QAEdD,EAAc,CAAEnF,MAAKoF,aAAY,EAiNiBmD,CAAWJ,EAAOnI,KACpD,cAAa,GAAGtD,YAAiByL,EAAOnI,MAAMlE,UAE9CiB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,8BAA6BD,SAAA,EAC1CkB,EAAAA,EAAAA,KAAA,QAAAlB,SAAOqM,EAAOjL,QACbiL,EAAOE,WACNrL,EAAAA,EAAAA,KAAA,QAAMjB,UAAW,oCACL,OAAVmJ,QAAU,IAAVA,OAAU,EAAVA,EAAYlF,OAAQmI,EAAOnI,IAAM,eAAiB,iBACjDlE,UACU,OAAVoJ,QAAU,IAAVA,OAAU,EAAVA,EAAYlF,OAAQmI,EAAOnI,KAAgC,QAAzBkF,EAAWE,WAC1CpI,EAAAA,EAAAA,KAACiH,EAAa,CAAClI,UAAU,aACf,OAAVmJ,QAAU,IAAVA,OAAU,EAAVA,EAAYlF,OAAQmI,EAAOnI,KAAgC,SAAzBkF,EAAWE,WAC3CpI,EAAAA,EAAAA,KAACwL,EAAAA,EAAe,CAACzM,UAAU,aAC3BiB,EAAAA,EAAAA,KAAA,QAAMjB,UAAU,gBAAeD,SAAC,iBAfvCqM,EAAOnI,aAuBpBhD,EAAAA,EAAAA,KAAA,SAAOjB,UAAU,oCAAmCD,SACjDiL,EAAchP,OAAS,EACtBgP,EAAcnJ,KAAI,CAAC2I,EAAKkC,KACtB1L,EAAAA,EAAAA,MAAA,MAEEhB,UAAW,qCACTsI,EAAa,iBAAmB,MAC9BoB,EAAamB,SAAS6B,GAAS,0BAA4B,2BAC7D9C,IAAe8C,EAAQ,aAAe,2BACtC1D,EAAeA,EAAawB,EAAKkC,GAAS,KAC5CpM,QAASA,IAAMgI,GAAcA,EAAWkC,GACxCmC,aAAcA,IAAM9C,EAAc6C,GAClCE,aAAcA,IAAM/C,EAAc,MAClC,cAAa,GAAGlJ,SAAc+L,IAAQ3M,SAAA,CAErC6I,IACC3H,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,8BAA6BD,UACzCkB,EAAAA,EAAAA,KAAA,SACE7D,KAAK,WACL4C,UAAU,kEACV+B,QAAS2H,EAAamB,SAAS6B,GAC/BpL,SAAUA,OACVhB,QAAUuG,GAjMVgG,EAACH,EAAed,KACtCA,EAAM/D,kBAEN,MAAMgE,EAAkB,IAAInC,GAE5B,GAAIA,EAAamB,SAAS6B,GAAQ,CAChC,MAAMR,EAAML,EAAgBiB,QAAQJ,GACpCb,EAAgBkB,OAAOb,EAAK,EAC9B,MACEL,EAAgBmB,KAAKN,GAKvB,GAFA/C,EAAgBkC,GAEZhD,EAAmB,CACrB,MAAMoD,EAAgBJ,EACnBhK,KAAIqK,GAAOlB,EAAckB,KACzB3B,QAAQ4B,QAA6BlQ,IAATkQ,IAC/BtD,EAAkBoD,EACpB,GA8KsCY,CAAgBH,EAAO7F,GACvC,cAAa,GAAGlG,SAAc+L,iBAInCtE,EAAQvG,KAAKuK,IACZnL,EAAAA,EAAAA,KAAA,MAEEjB,UAAW,oFAAoFoM,EAAOC,OAAS,UAAUD,EAAOpM,WAAa,KAC7I,cAAa,GAAGW,SAAc+L,UAAcN,EAAOnI,MAAMlE,SAExDqM,EAAOa,OACJb,EAAOa,OAAOzC,EAAI4B,EAAOnI,KAAMuG,GAC/B4B,EAAOnI,IAAI2G,cAAcC,SAAS,UAChCQ,EAAkBb,EAAI4B,EAAOnI,MAC7BuG,EAAI4B,EAAOnI,MARZmI,EAAOnI,SAzBXyI,MAuCTzL,EAAAA,EAAAA,KAAA,MAAAlB,UACEkB,EAAAA,EAAAA,KAAA,MACEiM,QAAS9E,EAAQpM,QAAU4M,EAAa,EAAI,GAC5C5I,UAAU,uCACV,cAAa,GAAGW,kBAAuBZ,SAEtCgJ,aAUdP,GAAcsC,EAAa,IAC1B9J,EAAAA,EAAAA,MAAA,OAAKhB,UAAW,wEAAwEG,IAAkBJ,SAAA,EACxGiB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,wBAAuBD,SAAA,CAAC,YAC1ByJ,EAAc,GAAKf,EAAY,EAAE,OAAKV,KAAKtD,IAAI+E,EAAcf,EAAU6B,EAAatO,QAAQ,OAAKsO,EAAatO,OAAO,eAElIgF,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,iBAAgBD,SAAA,EAC7BkB,EAAAA,EAAAA,KAAA,UACEX,QAASA,IAAM6K,EAAiBpD,KAAKpD,IAAI,EAAG6E,EAAc,IAC1D/H,SAA0B,IAAhB+H,EACVxJ,UAAW,iCACO,IAAhBwJ,EACI,mCACA,mCAEN,cAAa,GAAG7I,oBAAyBZ,SAC1C,aAGA2C,MAAMoJ,KAAK,CAAE9P,OAAQ+L,KAAKtD,IAAI,EAAGqG,KAAe,CAACiB,EAAGC,KAEnD,IAAImB,EAWJ,OATEA,EADErC,GAAc,GAEPtB,GAAe,EADdwC,EAAI,EAGLxC,GAAesB,EAAa,EAC3BA,EAAa,EAAIkB,EAEjBxC,EAAc,EAAIwC,GAI5B/K,EAAAA,EAAAA,KAAA,UAEEX,QAASA,IAAM6K,EAAiBgC,GAChCnN,UAAW,iCACTwJ,IAAgB2D,EACZ,wBACA,mCAEN,cAAa,GAAGxM,gBAAqBwM,IAAUpN,SAE9CoN,GATIA,EAUE,KAGblM,EAAAA,EAAAA,KAAA,UACEX,QAASA,IAAM6K,EAAiBpD,KAAKtD,IAAIqG,EAAYtB,EAAc,IACnE/H,SAAU+H,IAAgBsB,EAC1B9K,UAAW,iCACTwJ,IAAgBsB,EACZ,mCACA,mCAEN,cAAa,GAAGnK,oBAAyBZ,SAC1C,iBAQb,CAEA,SAAemB,EAAAA,EAAAA,MAAKiH,E,6ECvZpB,MAAMiF,EAA8BxO,IAiB7B,IAjB8B,OACnCyO,EAAM,QACNC,EAAO,MACPjQ,EAAK,SACL0C,EAAQ,KACR2L,EAAO,KAAI,OACXrL,EAAM,WACNkN,GAAa,EAAI,qBACjBC,GAAuB,EAAI,gBAC3BC,GAAkB,EAAI,SACtBC,GAAW,EAAI,UACf1N,EAAY,GAAE,cACdC,EAAgB,GAAE,gBAClBC,EAAkB,GAAE,gBACpBC,EAAkB,GAAE,kBACpBwN,EAAoB,GAAE,OACtBhN,GACD/B,EACC,MAAMgP,GAAW1H,EAAAA,EAAAA,QAAuB,MA2DxC,IAxDA2H,EAAAA,EAAAA,YAAU,KACR,MAAMC,EAAgBjH,IAChB0G,GAAwB,WAAV1G,EAAE5C,KAClBqJ,GACF,EASF,OANID,IACFU,SAASC,iBAAiB,UAAWF,GAErCC,SAASE,KAAKC,MAAMC,SAAW,UAG1B,KACLJ,SAASK,oBAAoB,UAAWN,GACxCC,SAASE,KAAKC,MAAMC,SAAW,MAAM,CACtC,GACA,CAACd,EAAQC,EAASC,KAGrBM,EAAAA,EAAAA,YAAU,KACR,IAAKR,IAAWO,EAASvG,QAAS,OAElC,MAAMgH,EAAoBT,EAASvG,QAAQiH,iBACzC,4EAGF,GAAiC,IAA7BD,EAAkBrS,OAAc,OAEpC,MAAMuS,EAAeF,EAAkB,GACjCG,EAAcH,EAAkBA,EAAkBrS,OAAS,GAE3DyS,EAAgB5H,IACN,QAAVA,EAAE5C,MAEF4C,EAAE6H,SACAX,SAASY,gBAAkBJ,IAC7BC,EAAYI,QACZ/H,EAAEC,kBAGAiH,SAASY,gBAAkBH,IAC7BD,EAAaK,QACb/H,EAAEC,kBAEN,EAMF,OAHAiH,SAASC,iBAAiB,UAAWS,GACrCF,EAAaK,QAEN,KACLb,SAASK,oBAAoB,UAAWK,EAAa,CACtD,GACA,CAACpB,KAECA,EAAQ,OAAO,KAGpB,MAUMwB,GACJ7N,EAAAA,EAAAA,MAAC8N,EAAAA,SAAQ,CAAA/O,SAAA,EAEPkB,EAAAA,EAAAA,KAAA,OACEjB,UAAW,gEAAgE2N,IAC3ErN,QAASkN,EAAuBF,OAAUrR,EAC1C,cAAa,GAAG0E,gBAIlBM,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,qCAAoCD,UACjDkB,EAAAA,EAAAA,KAAA,OAAKjB,UAAW,yBAAyB0N,EAAW,SAAW,yCAAyC3N,UACtGiB,EAAAA,EAAAA,MAAA,OACExB,IAAKoO,EACL5N,UAAW,GAxBD,CAClB+O,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,YACJC,GAAI,YACJC,KAAM,mBAkB4B1D,2GAA8G1L,IACxIM,QAAUuG,GAAMA,EAAEgB,kBAClB,cAAalH,EAAOZ,SAAA,EAGpBiB,EAAAA,EAAAA,MAAA,OAAKhB,UAAW,wEAAwEE,IAAkBH,SAAA,CACtF,kBAAV1C,GACN4D,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,sCAAqCD,SAAE1C,IAErDA,EAEDoQ,IACCxM,EAAAA,EAAAA,KAAA,UACE7D,KAAK,SACL4C,UAAU,wGACVM,QAASgN,EACT,aAAW,cACX,cAAa,GAAG3M,iBAAsBZ,UAEtCkB,EAAAA,EAAAA,KAAC6G,EAAAA,EAAS,CAAC9H,UAAU,kBAM3BiB,EAAAA,EAAAA,KAAA,OAAKjB,UAAW,aAAaC,IAAgBF,SAC1CA,IAIFM,IACCY,EAAAA,EAAAA,KAAA,OAAKjB,UAAW,4EAA4EG,IAAkBJ,SAC3GM,cAUf,OAAOgP,EAAAA,EAAAA,cAAaR,EAAcd,SAASE,KAAK,EAGlD,GAAe/M,EAAAA,EAAAA,MAAKkM,E,gDClLpB,SAASkC,EAAY1Q,EAIlBC,GAAQ,IAJW,MACpBxB,EAAK,QACLyB,KACGC,GACJH,EACC,OAAoBI,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ1B,EAAqB2B,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHzB,GAAS,KAAmB2B,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,mQAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBsQ,E,gDCvBlD,SAASC,EAAS3Q,EAIfC,GAAQ,IAJQ,MACjBxB,EAAK,QACLyB,KACGC,GACJH,EACC,OAAoBI,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ1B,EAAqB2B,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHzB,GAAS,KAAmB2B,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,kaAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBuQ,E,gDCvBlD,SAASC,EAAO5Q,EAIbC,GAAQ,IAJM,MACfxB,EAAK,QACLyB,KACGC,GACJH,EACC,OAAoBI,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ1B,EAAqB2B,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHzB,GAAS,KAAmB2B,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,6LACYZ,EAAAA,cAAoB,OAAQ,CAC3CU,cAAe,QACfC,eAAgB,QAChBC,EAAG,wCAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBwQ,E,gDC3BlD,SAASC,EAAS7Q,EAIfC,GAAQ,IAJQ,MACjBxB,EAAK,QACLyB,KACGC,GACJH,EACC,OAAoBI,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ1B,EAAqB2B,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHzB,GAAS,KAAmB2B,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,sWAEP,CACA,MACA,EADiCZ,EAAAA,WAAiByQ,E,qGCvBlD,SAASC,EAAmB9Q,EAIzBC,GAAQ,IAJkB,MAC3BxB,EAAK,QACLyB,KACGC,GACJH,EACC,OAAoBI,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ1B,EAAqB2B,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHzB,GAAS,KAAmB2B,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,+SAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB0Q,G,0HCMlD,MAwIA,EAxIkD9Q,IAM3C,IAN4C,UACjD+Q,EAAS,eACTC,EAAc,iBACdC,EACAC,gBAAiBC,EAAgB,MACjC1S,EAAQ,aACTuB,EACC,MAAMoR,GAAWC,EAAAA,EAAAA,MAMX7H,EAAU,CACd,CACEnE,IAAK,OACL9C,MAAO,gBACPmL,UAAU,EACVW,OAAQA,CAACiD,EAAgBC,KACvBnP,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,oBAAmBD,SAAA,EAChCkB,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,OAAMD,UACnBkB,EAAAA,EAAAA,KAACmP,EAAAA,EAAM,IACAD,EAASE,MAAQ,CAAE1I,IAAKwI,EAASE,MACtCzI,IAAKuI,EAAS/O,KACdA,KAAM+O,EAAS/O,KACfsK,KAAK,UAGT1K,EAAAA,EAAAA,MAAA,OAAAjB,SAAA,EACEkB,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,4BAA2BD,SAAEoQ,EAAS/O,QACrDJ,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,wBAAuBD,SAAA,CAAC,OAAKoQ,EAAS1Q,aAK7D,CACEwE,IAAK,QACL9C,MAAO,QACPmL,UAAU,EACVW,OAAS5L,IACPL,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,oBAAmBD,SAAA,EAChCkB,EAAAA,EAAAA,KAACqO,EAAAA,EAAY,CAACtP,UAAU,gCACxBiB,EAAAA,EAAAA,KAAA,QAAAlB,SAAOsB,QAIb,CACE4C,IAAK,QACL9C,MAAO,QACPmL,UAAU,EACVW,OAAS5L,IACPL,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,oBAAmBD,SAAA,EAChCkB,EAAAA,EAAAA,KAACwO,EAAAA,EAAS,CAACzP,UAAU,gCACrBiB,EAAAA,EAAAA,KAAA,QAAAlB,SAAOsB,QAIb,CACE4C,IAAK,qBACL9C,MAAO,SACPmL,UAAU,EACVW,OAAS5L,IAEP,IAAKA,EACH,OACEL,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,oBAAmBD,SAAA,EAChCkB,EAAAA,EAAAA,KAACqP,EAAAA,EAAS,CAACtQ,UAAU,gCACrBiB,EAAAA,EAAAA,KAAA,QAAAlB,SAAM,eAKZ,IAAIK,EACJ,OAAOiB,GACL,IAAK,WACHjB,GAAOa,EAAAA,EAAAA,KAACsP,EAAAA,EAAe,CAACvQ,UAAU,gCAClC,MACF,IAAK,UACHI,GAAOa,EAAAA,EAAAA,KAACqP,EAAAA,EAAS,CAACtQ,UAAU,iCAC5B,MACF,IAAK,WACHI,GAAOa,EAAAA,EAAAA,KAACuP,EAAAA,EAAW,CAACxQ,UAAU,8BAC9B,MACF,QACEI,GAAOa,EAAAA,EAAAA,KAACqP,EAAAA,EAAS,CAACtQ,UAAU,+BAEhC,OACEgB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,oBAAmBD,SAAA,CAC/BK,GACDa,EAAAA,EAAAA,KAAA,QAAAlB,SAAOsB,EAAQA,EAAMoP,OAAO,GAAGC,cAAgBrP,EAAM6J,MAAM,GAAK,cAC5D,GAKZ,CACEjH,IAAK,UACL9C,MAAO,UACP8L,OAAQA,CAAClB,EAAQoE,KACfnP,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,8BAA6BD,SAAA,EAC1CkB,EAAAA,EAAAA,KAAA,UACEjB,UAAU,sEACVM,QAAUuG,IACRA,EAAEgB,kBACF+H,EAAeO,EAAS,EAE1B9S,MAAM,wBAAuB0C,UAE7BkB,EAAAA,EAAAA,KAACuO,EAAAA,EAAO,CAACxP,UAAU,eAErBiB,EAAAA,EAAAA,KAAA,UACEjB,UAAU,sEACVM,QAAUuG,IACRA,EAAEgB,kBACFgI,EAAiBM,EAAS,EAE5B9S,MAAM,kBAAiB0C,UAEvBkB,EAAAA,EAAAA,KAACsO,EAAAA,EAAS,CAACvP,UAAU,mBAO/B,OACEiB,EAAAA,EAAAA,KAACkH,EAAAA,EAAS,CACRC,QAASA,EACTC,KAAMsH,EACNrH,WAvHoB6H,IACtBH,EAASW,EAAAA,EAAOC,wBAAwBT,EAAS1Q,IAAI,EAuHnDpC,MAAOA,EACPmL,YAAY,GACZ,ECjCN,EA9GwD5J,IAAmB,IAAlB,SAAEuR,GAAUvR,EACnE,OACEoC,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,YAAWD,SAAA,EACxBiB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,oCAAmCD,SAAA,EAChDiB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,8BAA6BD,SAAA,EAC1CkB,EAAAA,EAAAA,KAACmP,EAAAA,EAAM,IACAD,EAASE,MAAQ,CAAE1I,IAAKwI,EAASE,MACtCzI,IAAKuI,EAAS/O,KACdA,KAAM+O,EAAS/O,KACfsK,KAAK,QAEP1K,EAAAA,EAAAA,MAAA,OAAAjB,SAAA,EACEkB,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,oCAAmCD,SAAEoQ,EAAS/O,QAC5DJ,EAAAA,EAAAA,MAAA,KAAGhB,UAAU,wBAAuBD,SAAA,CAAC,OAAKoQ,EAAS1Q,OACnDwB,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,OAAMD,UACnBkB,EAAAA,EAAAA,KAAA,QAAMjB,UAAW,4EACiB,aAAhCmQ,EAASU,mBACL,8BACgC,YAAhCV,EAASU,mBACP,gCACA,2BACL9Q,SACAoQ,EAASU,mBACRV,EAASU,mBAAmBJ,OAAO,GAAGC,cAAgBP,EAASU,mBAAmB3F,MAAM,GACxF,oBAMTiF,EAASW,UACR7P,EAAAA,EAAAA,KAAA,KACE8P,KAAMZ,EAASW,QACfpJ,OAAO,SACPsJ,IAAI,sBACJhR,UAAU,uCAAsCD,SACjD,sBAMLiB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,sEAAqED,SAAA,EAClFiB,EAAAA,EAAAA,MAAA,OAAAjB,SAAA,EACEkB,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,yCAAwCD,SAAC,yBACvDiB,EAAAA,EAAAA,MAAA,MAAIhB,UAAU,YAAWD,SAAA,EACvBiB,EAAAA,EAAAA,MAAA,OAAAjB,SAAA,EACEkB,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,wBAAuBD,SAAC,oBACtCkB,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,wBAAuBD,SAAEoQ,EAASc,oBAElDjQ,EAAAA,EAAAA,MAAA,OAAAjB,SAAA,EACEkB,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,wBAAuBD,SAAC,WACtCkB,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,wBAAuBD,SAAEoQ,EAASlO,YAElDjB,EAAAA,EAAAA,MAAA,OAAAjB,SAAA,EACEkB,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,wBAAuBD,SAAC,WACtCkB,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,wBAAuBD,SAAEoQ,EAAS/N,kBAKtDpB,EAAAA,EAAAA,MAAA,OAAAjB,SAAA,EACEkB,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,yCAAwCD,SAAC,aACvDkB,EAAAA,EAAAA,KAAA,WAASjB,UAAU,mCAAkCD,SAClDoQ,EAASe,iBAKhBlQ,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,gCAA+BD,SAAA,EAC5CkB,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,yCAAwCD,SAAC,gBACvDkB,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,uBAAsBD,SAClCoQ,EAASgB,YAAchB,EAASgB,WAAWnV,OAAS,EACnDmU,EAASgB,WAAWtP,KAAI,CAACuP,EAAU1E,KACjCzL,EAAAA,EAAAA,KAAA,QAEEjB,UAAU,oGAAmGD,SAE5GqR,GAHI1E,MAOTzL,EAAAA,EAAAA,KAAA,QAAMjB,UAAU,wBAAuBD,SAAC,iCAK9CiB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,gCAA+BD,SAAA,EAC5CkB,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,yCAAwCD,SAAC,yBACvDiB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,8BAA6BD,SAAA,CACT,aAAhCoQ,EAASU,oBACR5P,EAAAA,EAAAA,KAACsP,EAAAA,EAAe,CAACvQ,UAAU,2BACO,YAAhCmQ,EAASU,oBACX5P,EAAAA,EAAAA,KAACqP,EAAAA,EAAS,CAACtQ,UAAU,6BAErBiB,EAAAA,EAAAA,KAACuP,EAAAA,EAAW,CAACxQ,UAAU,0BAEzBiB,EAAAA,EAAAA,KAAA,QAAMjB,UAAU,wBAAuBD,SACJ,aAAhCoQ,EAASU,mBACN,WACgC,YAAhCV,EAASU,mBACP,uBACA,qBAIR,E,kCCzGV,MA2RA,EA3RwDjS,IAIjD,IAJkD,SACvDyS,EAAQ,SACRC,EAAQ,UACRC,GAAY,GACb3S,EACC,MAAOwF,EAAUoN,IAAe/U,EAAAA,EAAAA,UAA2B,CACzDgV,aAAc,GACdxP,MAAO,GACPG,MAAO,GACP8O,QAAS,GACTQ,aAAc,GACdpO,SAAU,GACVC,gBAAiB,GACjB0N,cAAe,GACfU,MAAO,QAGF/N,EAAQgO,IAAanV,EAAAA,EAAAA,UAAiC,CAAC,IACvD0U,EAAYU,IAAiBpV,EAAAA,EAAAA,UAAqB,KAClDqV,EAAmBC,IAAwBtV,EAAAA,EAAAA,WAAS,IAG3DoR,EAAAA,EAAAA,YAAU,KACgBvP,WACtB,IACEyT,GAAqB,GAErB,MAAMC,EAA6B,CACjC,CACEvS,GAAI,IACJ2B,KAAM,SACNmH,YAAa,kBACb0J,aAAc,EACdC,iBAAkB,EAClB5G,OAAQ,SACR6G,WAAW,IAAIjP,MAAOkP,cACtBC,WAAW,IAAInP,MAAOkP,cACtBE,sBAAsB,EACtBC,sBAAsB,GAExB,CACE9S,GAAI,IACJ2B,KAAM,YACNmH,YAAa,qBACb0J,aAAc,EACdC,iBAAkB,EAClB5G,OAAQ,SACR6G,WAAW,IAAIjP,MAAOkP,cACtBC,WAAW,IAAInP,MAAOkP,cACtBE,sBAAsB,EACtBC,sBAAsB,GAExB,CACE9S,GAAI,IACJ2B,KAAM,gBACNmH,YAAa,yBACb0J,aAAc,EACdC,iBAAkB,EAClB5G,OAAQ,SACR6G,WAAW,IAAIjP,MAAOkP,cACtBC,WAAW,IAAInP,MAAOkP,cACtBE,sBAAsB,EACtBC,sBAAsB,GAExB,CACE9S,GAAI,IACJ2B,KAAM,aACNmH,YAAa,sBACb0J,aAAc,EACdC,iBAAkB,EAClB5G,OAAQ,SACR6G,WAAW,IAAIjP,MAAOkP,cACtBC,WAAW,IAAInP,MAAOkP,cACtBE,sBAAsB,EACtBC,sBAAsB,GAExB,CACE9S,GAAI,IACJ2B,KAAM,aACNmH,YAAa,sBACb0J,aAAc,EACdC,iBAAkB,EAClB5G,OAAQ,SACR6G,WAAW,IAAIjP,MAAOkP,cACtBC,WAAW,IAAInP,MAAOkP,cACtBE,sBAAsB,EACtBC,sBAAsB,GAExB,CACE9S,GAAI,IACJ2B,KAAM,kBACNmH,YAAa,2BACb0J,aAAc,EACdC,iBAAkB,EAClB5G,OAAQ,SACR6G,WAAW,IAAIjP,MAAOkP,cACtBC,WAAW,IAAInP,MAAOkP,cACtBE,sBAAsB,EACtBC,sBAAsB,GAExB,CACE9S,GAAI,IACJ2B,KAAM,aACNmH,YAAa,sBACb0J,aAAc,EACdC,iBAAkB,EAClB5G,OAAQ,SACR6G,WAAW,IAAIjP,MAAOkP,cACtBC,WAAW,IAAInP,MAAOkP,cACtBE,sBAAsB,EACtBC,sBAAsB,IAG1BV,EAAcG,EAChB,CAAE,MAAOrV,GACPgK,QAAQhK,MAAM,6BAA8BA,EAC9C,CAAC,QACCoV,GAAqB,EACvB,GAGFS,EAAiB,GAChB,IAEH,MAAMC,EAAgB5L,IACpB,MAAM,KAAEzF,EAAI,MAAEC,GAAUwF,EAAEa,OAC1B8J,GAAYkB,IAAI,IAAUA,EAAM,CAACtR,GAAOC,MAGpCuC,EAAOxC,IACTwQ,GAAUc,IAAI,IAAUA,EAAM,CAACtR,GAAO,MACxC,EA4BIrD,EAAgBA,CAACL,EAAeJ,KACpCsU,GAAUc,IAAI,IAAUA,EAAM,CAAChV,GAAQJ,KAAW,EAWpD,OACE0D,EAAAA,EAAAA,MAAA,QAAMqQ,SATcxK,IACpBA,EAAEC,iBArBqB6L,MACvB,MAAMC,EAAsB,CAC1BnB,aAAc,CAAC9N,EAAAA,GAAgBpC,SAAS,8BACxCU,MAAO,CAAC0B,EAAAA,GAAgBpC,SAAS,qBAAsBoC,EAAAA,GAAgB1B,SACvEG,MAAO,CAACuB,EAAAA,GAAgBpC,SAAS,4BAA6BoC,EAAAA,GAAgBvB,SAC9E8O,QAAS,CAACvN,EAAAA,GAAgBpC,SAAS,wBACnCmQ,aAAc,CAAC/N,EAAAA,GAAgBpC,SAAS,8BACxC+B,SAAU,CAACK,EAAAA,GAAgBpC,SAAS,wBAAyBoC,EAAAA,GAAgBL,YAC7EC,gBAAiB,CAACI,EAAAA,GAAgBpC,SAAS,gCAAiCoC,EAAAA,GAAgBoB,kBAGxF8N,GAAmBpP,EAAAA,EAAAA,GAAaW,EAAUwO,GAEhD,OADAhB,EAAUiB,GACsC,IAAzC5T,OAAO6T,KAAKD,GAAkB7W,MAAY,EAU7C2W,IACFtB,EAASjN,EAAUrG,EACrB,EAI8BiC,UAAU,YAAWD,SAAA,EACjDiB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,wCAAuCD,SAAA,EACpDkB,EAAAA,EAAAA,KAAC8R,EAAAA,EAAS,CACR5R,MAAM,gBACNC,KAAK,eACLC,MAAO+C,EAASqN,aAChBnQ,SAAUmR,EACV9V,MAAOiH,EAAO6N,aACdlQ,UAAQ,KAGVN,EAAAA,EAAAA,KAAC8R,EAAAA,EAAS,CACR5R,MAAM,gBACNC,KAAK,eACLhE,KAAK,SACLiE,MAAO+C,EAASsN,aAChBpQ,SAAUmR,EACV9V,MAAOiH,EAAO8N,aACdnQ,UAAQ,EACRb,QAASoR,EACThW,QAAS,CACP,CAAEuF,MAAO,GAAIF,MAAO,6BACjBgQ,EAAWtP,KAAKuP,IAAQ,CACzB/P,MAAO+P,EAAShQ,KAChBD,MAAOiQ,EAAShQ,aAKtBH,EAAAA,EAAAA,KAAC8R,EAAAA,EAAS,CACR5R,MAAM,gBACNC,KAAK,QACLhE,KAAK,QACLiE,MAAO+C,EAASnC,MAChBX,SAAUmR,EACV9V,MAAOiH,EAAO3B,MACdV,UAAQ,KAGVN,EAAAA,EAAAA,KAAC8R,EAAAA,EAAS,CACR5R,MAAM,eACNC,KAAK,QACLhE,KAAK,MACLiE,MAAO+C,EAAShC,MAChBd,SAAUmR,EACV9V,MAAOiH,EAAOxB,MACdb,UAAQ,KAGVN,EAAAA,EAAAA,KAAC8R,EAAAA,EAAS,CACR5R,MAAM,WACNC,KAAK,WACLhE,KAAK,WACLiE,MAAO+C,EAASd,SAChBhC,SAAUmR,EACV9V,MAAOiH,EAAON,SACd/B,UAAQ,KAGVN,EAAAA,EAAAA,KAAC8R,EAAAA,EAAS,CACR5R,MAAM,mBACNC,KAAK,kBACLhE,KAAK,WACLiE,MAAO+C,EAASb,gBAChBjC,SAAUmR,EACV9V,MAAOiH,EAAOL,gBACdhC,UAAQ,KAGVN,EAAAA,EAAAA,KAAC8R,EAAAA,EAAS,CACR5R,MAAM,UACNC,KAAK,UACLC,MAAO+C,EAAS8M,QAChB5P,SAAUmR,EACV9V,MAAOiH,EAAOsN,QACd3P,UAAQ,EACRvB,UAAU,sBAKdiB,EAAAA,EAAAA,KAAC+R,EAAAA,EAAW,CACV7R,MAAM,iBACNC,KAAK,QACLC,MAAO+C,EAASuN,OAAS,KACzBrQ,SA3HqBiF,IACzBiL,GAAYkB,IAAI,IAAUA,EAAMf,MAAOpL,MAGnC3C,EAAO+N,OACTC,GAAUc,IAAI,IAAUA,EAAMf,MAAO,MACvC,EAsHIhV,MAAOiH,EAAO+N,MACdhM,QAAS,QACTC,aAAc,CAAC,aAAc,YAAa,YAAa,iBAGzD5E,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,6BAA4BD,SAAA,EACzCkB,EAAAA,EAAAA,KAACgS,EAAAA,EAAM,CACL7V,KAAK,SACLuO,QAAQ,UACRrL,QAASgR,EACT7P,SAAU8P,EAAUxR,SACrB,YAGDkB,EAAAA,EAAAA,KAACgS,EAAAA,EAAM,CACL7V,KAAK,SACLsD,QAAS6Q,EAAUxR,SACpB,sBAIE,E,wBCxRX,MAoPA,EApPgCmT,KAC9B,MAAOC,EAAWC,IAAgB3W,EAAAA,EAAAA,UAAsD,QACjF8U,EAAW8B,IAAgB5W,EAAAA,EAAAA,WAAS,IACpC6W,EAAwBC,IAA6B9W,EAAAA,EAAAA,WAAS,IAC9D+W,EAAkBC,IAAuBhX,EAAAA,EAAAA,UAA0B,OACnEiX,EAA4BC,IAAiClX,EAAAA,EAAAA,WAAS,IACtEmX,EAAmBC,IAAwBpX,EAAAA,EAAAA,WAAS,IACpDqX,EAAkBC,IAAuBtX,EAAAA,EAAAA,UAA0B,OACnEuX,EAAYC,IAAiBxX,EAAAA,EAAAA,WAAS,IAGvC,UACJkT,EACA4B,UAAW2C,EACXC,aAAcC,EACdC,aAAcC,EAAc,yBAC5BC,IACEC,EAAAA,EAAAA,MAGE,sBACJhW,EAAqB,WACrB3B,IACE4X,EAAAA,EAAAA,GAAgB,CAClBvY,qBAAqB,EACrBC,iBAAiB,IAIbuY,GAAoB3K,EAAAA,EAAAA,UAAQ,IACd,QAAdoJ,EAA4BxD,EACzBA,EAAUpF,QAAO4F,GAAYA,EAASU,qBAAuBsC,KACnE,CAACxD,EAAWwD,IAMTwB,EAAsBxE,IAC1BsD,EAAoBtD,GACpBwD,GAA8B,EAAK,EAK/BiB,GAAuB9X,EAAAA,EAAAA,cAAaqT,IACxC4D,EAAoB5D,GACpB0D,GAAqB,EAAK,GACzB,IAEGgB,GAAwB/X,EAAAA,EAAAA,cAAYwB,UACxC,IAAKwV,EAAkB,OAEvBG,GAAc,GACd,MAAMa,QAAetW,GAAsBF,gBACnCgW,EAAeR,EAAiBrU,KAC/B,SACNxD,EAAW,mBAAmB6X,EAAiB1S,QAElD6S,GAAc,GACVa,GACFjB,GAAqB,GACrBE,EAAoB,OAEpBpN,QAAQhK,MAAM,4BAChB,GACC,CAACmX,EAAkBtV,EAAuB8V,IAEvCS,GAAoBjY,EAAAA,EAAAA,cAAYwB,MAAO0W,EAAgCjX,KAC3EsV,GAAa,GACbxW,IAEA,MAAMiY,QAAetW,GAAsBF,UACzC,MAAM2W,QAAoBb,EAAeY,GAAc,GAEvD,OADAzB,GAA0B,GACnB0B,CAAW,GACjBlX,EAAe,gBAElBsV,GAAa,GAETyB,GACFnO,QAAQuO,IAAI,+BAAgCJ,EAC9C,GACC,CAACtW,EAAuB4V,EAAgBvX,IAErCsY,EAAuB7W,MAAO8W,EAAoBC,KACtD,UACQd,EAAyBa,EAAYC,GAC3C1B,GAA8B,EAChC,CAAE,MAAOhX,GACPgK,QAAQhK,MAAM,sCAAuCA,EAEvD,GAGF,OACEqE,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,YAAWD,SAAA,EACxBiB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,8EAA6ED,SAAA,EAC1FiB,EAAAA,EAAAA,MAAA,OAAAjB,SAAA,EACEkB,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,mCAAkCD,SAAC,eACjDkB,EAAAA,EAAAA,KAAA,KAAGjB,UAAU,6BAA4BD,SAAC,sDAE5CkB,EAAAA,EAAAA,KAACgS,EAAAA,EAAM,CACL7S,MAAMa,EAAAA,EAAAA,KAACyO,EAAmB,CAAC1P,UAAU,YACrCM,QAASA,IAAMiT,GAA0B,GAAMxT,SAChD,qBAKHiB,EAAAA,EAAAA,MAACnB,EAAAA,EAAI,CAAAE,SAAA,EACHiB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,4BAA2BD,SAAA,EACxCkB,EAAAA,EAAAA,KAACgS,EAAAA,EAAM,CACLtH,QAAuB,QAAdwH,EAAsB,UAAY,UAC3CzH,KAAK,KACLpL,QAASA,IAAM8S,EAAa,OAAOrT,SACpC,mBAGDkB,EAAAA,EAAAA,KAACgS,EAAAA,EAAM,CACLtH,QAAuB,YAAdwH,EAA0B,UAAY,UAC/CzH,KAAK,KACLpL,QAASA,IAAM8S,EAAa,WAAWrT,SACxC,0BAGDkB,EAAAA,EAAAA,KAACgS,EAAAA,EAAM,CACLtH,QAAuB,aAAdwH,EAA2B,UAAY,UAChDzH,KAAK,KACLpL,QAASA,IAAM8S,EAAa,YAAYrT,SACzC,cAGDkB,EAAAA,EAAAA,KAACgS,EAAAA,EAAM,CACLtH,QAAuB,aAAdwH,EAA2B,UAAY,UAChDzH,KAAK,KACLpL,QAASA,IAAM8S,EAAa,YAAYrT,SACzC,gBAKFmU,GACCjT,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,2BAA0BD,UACvCkB,EAAAA,EAAAA,KAACwK,EAAAA,EAAc,OAGjBxK,EAAAA,EAAAA,KAACqU,EAAY,CACX3F,UAAW+E,EACX5E,gBAnHmBK,IAC3BwE,EAAmBxE,EAAS,EAmHpBP,eAAgB+E,EAChB9E,iBAAkB+E,EAClBvX,MAAO,GAAG8V,EAAYA,EAAU1C,OAAO,GAAGC,cAAgByC,EAAUjI,MAAM,GAAK,oBAAoBwJ,EAAkB1Y,gBAM3HiF,EAAAA,EAAAA,KAACmM,EAAAA,EAAK,CACJC,OAAQiG,EACRhG,QAASA,IAAMiG,GAA0B,GACzClW,MAAM,mBACNqO,KAAK,KAAI3L,UAETkB,EAAAA,EAAAA,KAACsU,EAAe,CACdlE,SAAU0D,EACVzD,SAAUA,IAAMiC,GAA0B,GAC1ChC,UAAWA,MAKdiC,IACCvS,EAAAA,EAAAA,KAACmM,EAAAA,EAAK,CACJC,OAAQqG,EACRpG,QAASA,IAAMqG,GAA8B,GAC7CtW,MAAM,mBACNqO,KAAK,KACLrL,QACEW,EAAAA,EAAAA,MAAAwU,EAAAA,SAAA,CAAAzV,SAAA,EACEkB,EAAAA,EAAAA,KAACgS,EAAAA,EAAM,CACLtH,QAAQ,UACRrL,QAASA,IAAMqT,GAA8B,GAAO5T,SACrD,UAGwC,YAAxCyT,EAAiB3C,qBAChB5P,EAAAA,EAAAA,KAACgS,EAAAA,EAAM,CACLtH,QAAQ,UACRrL,QAASA,IAAM6U,EAAqB3B,EAAiB/T,GAAI,YAAYM,SACtE,WAIsC,aAAxCyT,EAAiB3C,qBAChB5P,EAAAA,EAAAA,KAACgS,EAAAA,EAAM,CACLtH,QAAQ,UACRrL,QAASA,IAAM6U,EAAqB3B,EAAiB/T,GAAI,WAAWM,SACrE,sBAKNA,UAEDkB,EAAAA,EAAAA,KAACwU,EAAe,CAACtF,SAAUqD,MAK9BM,IACC7S,EAAAA,EAAAA,KAACmM,EAAAA,EAAK,CACJC,OAAQuG,EACRtG,QAASA,IAAMuG,GAAqB,GACpCxW,MAAM,kBACNqO,KAAK,KACLrL,QACEW,EAAAA,EAAAA,MAAAwU,EAAAA,SAAA,CAAAzV,SAAA,EACEkB,EAAAA,EAAAA,KAACgS,EAAAA,EAAM,CACLtH,QAAQ,UACRrL,QAASA,IAAMuT,GAAqB,GACpCpS,SAAUuS,EAAWjU,SACtB,YAGDkB,EAAAA,EAAAA,KAACgS,EAAAA,EAAM,CACLtH,QAAQ,SACRrL,QAASuU,EACTnU,QAASsT,EAAWjU,SACrB,uBAIJA,UAEDiB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,wBAAuBD,SAAA,CAAC,oCACH+T,EAAiB1S,KAAK,0CAI1D,C", "sources": ["hooks/useErrorHandler.ts", "../node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js", "components/common/Card.tsx", "components/common/FormField.tsx", "utils/validation.ts", "components/common/ImageUpload.tsx", "../node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ChevronUpIcon.js", "components/common/DataTable.tsx", "components/common/Modal.tsx", "../node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js", "../node_modules/@heroicons/react/24/outline/esm/TrashIcon.js", "../node_modules/@heroicons/react/24/outline/esm/EyeIcon.js", "../node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js", "../node_modules/@heroicons/react/24/outline/esm/BuildingOffice2Icon.js", "features/suppliers/components/SupplierList.tsx", "features/suppliers/components/SupplierDetails.tsx", "features/suppliers/components/AddSupplierForm.tsx", "pages/SuppliersPage.tsx"], "sourcesContent": ["/**\r\n * Error <PERSON>ler Hook\r\n * \r\n * This hook provides React-specific error handling utilities and state management.\r\n */\r\n\r\nimport { useState, useCallback } from 'react';\r\nimport { \r\n  handleApiError, \r\n  handleValidationError, \r\n  handleFormError,\r\n  logError,\r\n  reportError,\r\n  type ApiError,\r\n  type ValidationError \r\n} from '../utils/errorHandling';\r\nimport useNotification from './useNotification';\r\n\r\ninterface ErrorState {\r\n  hasError: boolean;\r\n  error: Error | ApiError | ValidationError | null;\r\n  errorType: 'api' | 'validation' | 'form' | 'general' | null;\r\n  context?: string;\r\n}\r\n\r\ninterface UseErrorHandlerOptions {\r\n  enableNotifications?: boolean;\r\n  enableReporting?: boolean;\r\n  onError?: (error: any, context?: string) => void;\r\n}\r\n\r\nexport const useErrorHandler = (options: UseErrorHandlerOptions = {}) => {\r\n  const { enableNotifications = true, enableReporting = true, onError } = options;\r\n  const { showNotification } = useNotification();\r\n  \r\n  const [errorState, setErrorState] = useState<ErrorState>({\r\n    hasError: false,\r\n    error: null,\r\n    errorType: null\r\n  });\r\n\r\n  // Clear error state\r\n  const clearError = useCallback(() => {\r\n    setErrorState({\r\n      hasError: false,\r\n      error: null,\r\n      errorType: null\r\n    });\r\n  }, []);\r\n\r\n  // Handle API errors\r\n  const handleApiErrorWithState = useCallback((error: any, context?: string) => {\r\n    const apiError = handleApiError(\r\n      error,\r\n      enableNotifications ? (notification: { type: string; title: string; message: string }) => {\r\n        showNotification({\r\n          type: notification.type as 'error' | 'success' | 'warning' | 'info',\r\n          title: notification.title,\r\n          message: notification.message\r\n        });\r\n      } : undefined\r\n    );\r\n\r\n    setErrorState({\r\n      hasError: true,\r\n      error: apiError,\r\n      errorType: 'api',\r\n      ...(context && { context })\r\n    });\r\n\r\n    if (enableReporting && error instanceof Error) {\r\n      reportError(error, context);\r\n    }\r\n\r\n    if (onError) {\r\n      onError(error, context);\r\n    }\r\n\r\n    return apiError;\r\n  }, [enableNotifications, enableReporting, showNotification, onError]);\r\n\r\n  // Handle validation errors\r\n  const handleValidationErrorWithState = useCallback((\r\n    field: string,\r\n    message: string,\r\n    code?: string,\r\n    context?: string\r\n  ) => {\r\n    const validationError = handleValidationError(field, message, code);\r\n\r\n    setErrorState({\r\n      hasError: true,\r\n      error: validationError,\r\n      errorType: 'validation',\r\n      ...(context && { context })\r\n    });\r\n\r\n    if (enableNotifications) {\r\n      showNotification({\r\n        type: 'error',\r\n        title: 'Validation Error',\r\n        message: validationError.message\r\n      });\r\n    }\r\n\r\n    if (onError) {\r\n      onError(validationError, context);\r\n    }\r\n\r\n    return validationError;\r\n  }, [enableNotifications, showNotification, onError]);\r\n\r\n  // Handle form errors\r\n  const handleFormErrorWithState = useCallback((\r\n    error: any,\r\n    setFieldError?: (field: string, message: string) => void,\r\n    context?: string\r\n  ) => {\r\n    handleFormError(\r\n      error,\r\n      setFieldError,\r\n      enableNotifications ? (notification: { type: string; title: string; message: string }) => {\r\n        showNotification({\r\n          type: notification.type as 'error' | 'success' | 'warning' | 'info',\r\n          title: notification.title,\r\n          message: notification.message\r\n        });\r\n      } : undefined\r\n    );\r\n\r\n    setErrorState({\r\n      hasError: true,\r\n      error,\r\n      errorType: 'form',\r\n      ...(context && { context })\r\n    });\r\n\r\n    if (enableReporting && error instanceof Error) {\r\n      reportError(error, context);\r\n    }\r\n\r\n    if (onError) {\r\n      onError(error, context);\r\n    }\r\n  }, [enableNotifications, enableReporting, showNotification, onError]);\r\n\r\n  // Handle general errors\r\n  const handleGeneralError = useCallback((error: any, context?: string) => {\r\n    const errorObj = error instanceof Error ? error : new Error(String(error));\r\n\r\n    setErrorState({\r\n      hasError: true,\r\n      error: errorObj,\r\n      errorType: 'general',\r\n      ...(context && { context })\r\n    });\r\n\r\n    if (enableNotifications) {\r\n      showNotification({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: errorObj.message\r\n      });\r\n    }\r\n\r\n    if (enableReporting) {\r\n      reportError(errorObj, context);\r\n    }\r\n\r\n    logError(errorObj, context);\r\n\r\n    if (onError) {\r\n      onError(error, context);\r\n    }\r\n\r\n    return errorObj;\r\n  }, [enableNotifications, enableReporting, showNotification, onError]);\r\n\r\n  // Async operation wrapper with error handling\r\n  const withErrorHandling = useCallback(async <T>(\r\n    operation: () => Promise<T>,\r\n    context?: string\r\n  ): Promise<T | null> => {\r\n    try {\r\n      clearError();\r\n      return await operation();\r\n    } catch (error) {\r\n      handleApiErrorWithState(error, context);\r\n      return null;\r\n    }\r\n  }, [clearError, handleApiErrorWithState]);\r\n\r\n  // Form submission wrapper with error handling\r\n  const withFormErrorHandling = useCallback(async <T>(\r\n    operation: () => Promise<T>,\r\n    setFieldError?: (field: string, message: string) => void,\r\n    context?: string\r\n  ): Promise<T | null> => {\r\n    try {\r\n      clearError();\r\n      return await operation();\r\n    } catch (error) {\r\n      handleFormErrorWithState(error, setFieldError, context);\r\n      return null;\r\n    }\r\n  }, [clearError, handleFormErrorWithState]);\r\n\r\n  return {\r\n    // Error state\r\n    ...errorState,\r\n    \r\n    // Error handlers\r\n    handleApiError: handleApiErrorWithState,\r\n    handleValidationError: handleValidationErrorWithState,\r\n    handleFormError: handleFormErrorWithState,\r\n    handleGeneralError,\r\n    clearError,\r\n    \r\n    // Wrapper functions\r\n    withErrorHandling,\r\n    withFormErrorHandling,\r\n    \r\n    // Utility functions\r\n    isApiError: (error: any): error is ApiError => \r\n      error && typeof error === 'object' && 'status' in error,\r\n    isValidationError: (error: any): error is ValidationError => \r\n      error && typeof error === 'object' && 'field' in error,\r\n  };\r\n};\r\n\r\nexport default useErrorHandler;\r\n", "import * as React from \"react\";\nfunction PhotoIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PhotoIcon);\nexport default ForwardRef;", "/**\r\n * Card Component\r\n *\r\n * A reusable card component for displaying content in a contained box.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\nexport interface CardProps {\r\n  title?: string | ReactNode;\r\n  subtitle?: string | ReactNode;\r\n  children: ReactNode;\r\n  className?: string;\r\n  bodyClassName?: string;\r\n  headerClassName?: string;\r\n  footerClassName?: string;\r\n  icon?: ReactNode;\r\n  footer?: ReactNode;\r\n  onClick?: () => void;\r\n  hoverable?: boolean;\r\n  noPadding?: boolean;\r\n  bordered?: boolean;\r\n  loading?: boolean;\r\n  testId?: string;\r\n}\r\n\r\nconst Card: React.FC<CardProps> = ({\r\n  title,\r\n  subtitle,\r\n  children,\r\n  className = '',\r\n  bodyClassName = '',\r\n  headerClassName = '',\r\n  footerClassName = '',\r\n  icon,\r\n  footer,\r\n  onClick,\r\n  hoverable = false,\r\n  noPadding = false,\r\n  bordered = true,\r\n  loading = false,\r\n  testId,\r\n}) => {\r\n  // Base classes\r\n  const cardClasses = `\r\n    bg-white rounded-xl ${bordered ? 'border border-gray-100' : ''} overflow-hidden transition-all duration-300\r\n    ${hoverable ? 'hover:shadow-md hover:border-gray-200 transform hover:-translate-y-1' : 'shadow-sm'}\r\n    ${onClick ? 'cursor-pointer' : ''}\r\n    ${className}\r\n  `;\r\n\r\n  // Header classes\r\n  const headerClasses = `\r\n    px-6 py-4 border-b border-gray-100 flex items-center justify-between\r\n    ${headerClassName}\r\n  `;\r\n\r\n  // Body classes\r\n  const bodyClasses = `\r\n    ${noPadding ? '' : 'p-6'}\r\n    ${bodyClassName}\r\n  `;\r\n\r\n  // Footer classes\r\n  const footerClasses = `\r\n    px-6 py-4 bg-gray-50 border-t border-gray-100\r\n    ${footerClassName}\r\n  `;\r\n\r\n  // Loading skeleton\r\n  if (loading) {\r\n    return (\r\n      <div className={cardClasses} data-testid={testId}>\r\n        {(title || subtitle || icon) && (\r\n          <div className={headerClasses}>\r\n            <div className=\"w-full\">\r\n              {title && <div className=\"h-6 bg-gray-200 rounded w-1/3 animate-pulse\"></div>}\r\n              {subtitle && <div className=\"h-4 mt-2 bg-gray-200 rounded w-1/2 animate-pulse\"></div>}\r\n            </div>\r\n            {icon && <div className=\"h-8 w-8 bg-gray-200 rounded-full animate-pulse\"></div>}\r\n          </div>\r\n        )}\r\n\r\n        <div className={bodyClasses}>\r\n          <div className=\"h-24 bg-gray-200 rounded animate-pulse\"></div>\r\n        </div>\r\n\r\n        {footer && (\r\n          <div className={footerClasses}>\r\n            <div className=\"h-8 bg-gray-200 rounded w-1/4 animate-pulse\"></div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className={cardClasses}\r\n      onClick={onClick}\r\n      data-testid={testId}\r\n    >\r\n      {(title || subtitle || icon) && (\r\n        <div className={headerClasses}>\r\n          <div>\r\n            {typeof title === 'string' ? (\r\n              <h3 className=\"text-lg font-semibold text-primary\">{title}</h3>\r\n            ) : (\r\n              title\r\n            )}\r\n            {typeof subtitle === 'string' ? (\r\n              <p className=\"mt-1 text-sm text-gray-500\">{subtitle}</p>\r\n            ) : (\r\n              subtitle\r\n            )}\r\n          </div>\r\n          {icon && <div className=\"text-primary\">{icon}</div>}\r\n        </div>\r\n      )}\r\n\r\n      <div className={bodyClasses}>{children}</div>\r\n\r\n      {footer && (\r\n        <div className={footerClasses}>\r\n          {footer}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(Card);\r\n", "import React from 'react';\r\n\r\ninterface FormFieldProps {\r\n  label: string;\r\n  name: string;\r\n  type?: string;\r\n  value: any;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;\r\n  error?: string | undefined;\r\n  required?: boolean;\r\n  placeholder?: string;\r\n  options?: { value: string; label: string }[];\r\n  className?: string;\r\n  disabled?: boolean;\r\n  loading?: boolean;\r\n}\r\n\r\nconst FormField: React.FC<FormFieldProps> = ({\r\n  label,\r\n  name,\r\n  type = 'text',\r\n  value,\r\n  onChange,\r\n  error,\r\n  required = false,\r\n  placeholder = '',\r\n  options = [],\r\n  className = '',\r\n  disabled = false,\r\n  loading = false\r\n}) => {\r\n  const inputClasses = `mt-1 block w-full rounded-md shadow-sm sm:text-sm ${\r\n    error ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-primary focus:ring-primary'\r\n  }`;\r\n  \r\n  const renderField = () => {\r\n    switch (type) {\r\n      case 'textarea':\r\n        return (\r\n          <textarea\r\n            id={name}\r\n            name={name}\r\n            value={value}\r\n            onChange={onChange}\r\n            className={inputClasses}\r\n            placeholder={placeholder}\r\n            disabled={disabled}\r\n          />\r\n        );\r\n      \r\n      case 'select':\r\n        return (\r\n          <select\r\n            id={name}\r\n            name={name}\r\n            value={value}\r\n            onChange={onChange}\r\n            className={inputClasses}\r\n            disabled={disabled || loading}\r\n          >\r\n            {loading ? (\r\n              <option value=\"\">Loading...</option>\r\n            ) : (\r\n              options.map(option => (\r\n                <option key={option.value} value={option.value}>\r\n                  {option.label}\r\n                </option>\r\n              ))\r\n            )}\r\n          </select>\r\n        );\r\n      \r\n      case 'checkbox':\r\n        return (\r\n          <input\r\n            type=\"checkbox\"\r\n            id={name}\r\n            name={name}\r\n            checked={value}\r\n            onChange={onChange}\r\n            className=\"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary\"\r\n            disabled={disabled}\r\n          />\r\n        );\r\n      \r\n      default:\r\n        return (\r\n          <input\r\n            type={type}\r\n            id={name}\r\n            name={name}\r\n            value={value}\r\n            onChange={onChange}\r\n            className={inputClasses}\r\n            placeholder={placeholder}\r\n            disabled={disabled}\r\n          />\r\n        );\r\n    }\r\n  };\r\n  \r\n  return (\r\n    <div className={`${className}`}>\r\n      <label htmlFor={name} className=\"block text-sm font-medium text-gray-700\">\r\n        {label} {required && <span className=\"text-red-500\">*</span>}\r\n      </label>\r\n      {renderField()}\r\n      {error && <p className=\"mt-1 text-sm text-red-600\">{error}</p>}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FormField;\r\n", "/**\r\n * Validation Utilities\r\n * \r\n * This file provides a comprehensive form validation utility with both function-based\r\n * and rule-based validation approaches.\r\n */\r\n\r\n// Type definitions\r\nexport type ValidationRule = {\r\n  validator: (value: any, formData?: any) => boolean;\r\n  message: string;\r\n};\r\n\r\nexport type ValidationRules = Record<string, ValidationRule | ValidationRule[]>;\r\n\r\n// Individual validation functions\r\nexport const isValidEmail = (email: string): boolean => {\r\n  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\r\n  return emailRegex.test(email);\r\n};\r\n\r\nexport const isValidPhone = (phone: string): boolean => {\r\n  const phoneRegex = /^\\+?[0-9]{10,15}$/;\r\n  return phoneRegex.test(phone);\r\n};\r\n\r\nexport const isValidUrl = (url: string): boolean => {\r\n  try {\r\n    new URL(url);\r\n    return true;\r\n  } catch (error) {\r\n    return false;\r\n  }\r\n};\r\n\r\nexport const isRequired = (value: any): boolean => {\r\n  if (value === null || value === undefined) return false;\r\n  if (typeof value === 'string') return value.trim().length > 0;\r\n  if (Array.isArray(value)) return value.length > 0;\r\n  return true;\r\n};\r\n\r\nexport const minLength = (value: string, min: number): boolean => {\r\n  return value.length >= min;\r\n};\r\n\r\nexport const maxLength = (value: string, max: number): boolean => {\r\n  return value.length <= max;\r\n};\r\n\r\nexport const isNumeric = (value: string): boolean => {\r\n  return /^[0-9]+$/.test(value);\r\n};\r\n\r\nexport const isDecimal = (value: string): boolean => {\r\n  return /^[0-9]+(\\.[0-9]+)?$/.test(value);\r\n};\r\n\r\nexport const isAlphanumeric = (value: string): boolean => {\r\n  return /^[a-zA-Z0-9]+$/.test(value);\r\n};\r\n\r\nexport const isValidDate = (dateString: string): boolean => {\r\n  const date = new Date(dateString);\r\n  return !isNaN(date.getTime());\r\n};\r\n\r\nexport const doPasswordsMatch = (password: string, confirmPassword: string): boolean => {\r\n  return password === confirmPassword;\r\n};\r\n\r\nexport const isStrongPassword = (password: string): boolean => {\r\n  // Password must be at least 8 characters long\r\n  if (password.length < 8) return false;\r\n  \r\n  // Password must contain at least one uppercase letter\r\n  if (!/[A-Z]/.test(password)) return false;\r\n  \r\n  // Password must contain at least one lowercase letter\r\n  if (!/[a-z]/.test(password)) return false;\r\n  \r\n  // Password must contain at least one number\r\n  if (!/[0-9]/.test(password)) return false;\r\n  \r\n  // Password must contain at least one special character\r\n  if (!/[!@#$%^&*()_+\\-=[\\]{};':\"\\\\|,.<>/?]/.test(password)) return false;\r\n  \r\n  return true;\r\n};\r\n\r\n// Field validation\r\nexport const validateField = (\r\n  _name: string,\r\n  value: any,\r\n  rules: ValidationRule | ValidationRule[],\r\n  formData?: any\r\n): string => {\r\n  const ruleArray = Array.isArray(rules) ? rules : [rules];\r\n  \r\n  for (const rule of ruleArray) {\r\n    if (!rule.validator(value, formData)) {\r\n      return rule.message;\r\n    }\r\n  }\r\n  \r\n  return '';\r\n};\r\n\r\n// Form validation\r\nexport const validateForm = <T extends Record<string, any>>(\r\n  values: T,\r\n  validationRules: ValidationRules\r\n): Partial<Record<keyof T, string>> => {\r\n  const errors: Partial<Record<keyof T, string>> = {};\r\n  \r\n  Object.entries(validationRules).forEach(([fieldName, rules]) => {\r\n    const key = fieldName as keyof T;\r\n    const error = validateField(fieldName, values[key], rules, values);\r\n    if (error) {\r\n      errors[key] = error;\r\n    }\r\n  });\r\n  \r\n  return errors;\r\n};\r\n\r\n// Common validation rules\r\nexport const validationRules = {\r\n  required: (message: string = 'This field is required'): ValidationRule => ({\r\n    validator: isRequired,\r\n    message\r\n  }),\r\n  \r\n  email: (message: string = 'Please enter a valid email address'): ValidationRule => ({\r\n    validator: isValidEmail,\r\n    message\r\n  }),\r\n  \r\n  phone: (message: string = 'Please enter a valid phone number'): ValidationRule => ({\r\n    validator: isValidPhone,\r\n    message\r\n  }),\r\n  \r\n  url: (message: string = 'Please enter a valid URL'): ValidationRule => ({\r\n    validator: isValidUrl,\r\n    message\r\n  }),\r\n  \r\n  minLength: (min: number, message?: string): ValidationRule => ({\r\n    validator: (value: string) => minLength(value, min),\r\n    message: message || `Must be at least ${min} characters`\r\n  }),\r\n  \r\n  maxLength: (max: number, message?: string): ValidationRule => ({\r\n    validator: (value: string) => maxLength(value, max),\r\n    message: message || `Must be no more than ${max} characters`\r\n  }),\r\n  \r\n  numeric: (message: string = 'Please enter a numeric value'): ValidationRule => ({\r\n    validator: isNumeric,\r\n    message\r\n  }),\r\n  \r\n  decimal: (message: string = 'Please enter a valid decimal number'): ValidationRule => ({\r\n    validator: isDecimal,\r\n    message\r\n  }),\r\n  \r\n  alphanumeric: (message: string = 'Please use only letters and numbers'): ValidationRule => ({\r\n    validator: isAlphanumeric,\r\n    message\r\n  }),\r\n  \r\n  date: (message: string = 'Please enter a valid date'): ValidationRule => ({\r\n    validator: isValidDate,\r\n    message\r\n  }),\r\n  \r\n  password: (message: string = 'Password must be at least 8 characters and include uppercase, lowercase, number, and special character'): ValidationRule => ({\r\n    validator: isStrongPassword,\r\n    message\r\n  }),\r\n  \r\n  passwordMatch: (message: string = 'Passwords do not match'): ValidationRule => ({\r\n    validator: (value: string, formData?: any) => doPasswordsMatch(value, formData?.confirmPassword),\r\n    message\r\n  }),\r\n  \r\n  confirmPasswordMatch: (message: string = 'Passwords do not match'): ValidationRule => ({\r\n    validator: (value: string, formData?: any) => doPasswordsMatch(value, formData?.password),\r\n    message\r\n  }),\r\n\r\n  // Product-specific validation rules\r\n  sku: (message: string = 'Please enter a valid SKU'): ValidationRule => ({\r\n    validator: (value: string) => /^[A-Z0-9-_]{3,20}$/i.test(value),\r\n    message\r\n  }),\r\n\r\n  price: (message: string = 'Please enter a valid price'): ValidationRule => ({\r\n    validator: (value: number) => value > 0 && value <= 999999,\r\n    message\r\n  }),\r\n\r\n  stock: (message: string = 'Please enter a valid stock quantity'): ValidationRule => ({\r\n    validator: (value: number) => Number.isInteger(value) && value >= 0,\r\n    message\r\n  }),\r\n\r\n  minimumStock: (message: string = 'Please enter a valid minimum stock level'): ValidationRule => ({\r\n    validator: (value: number) => Number.isInteger(value) && value >= 0,\r\n    message\r\n  }),\r\n\r\n  stockConsistency: (message: string = 'Minimum stock cannot be greater than current stock'): ValidationRule => ({\r\n    validator: (minimumStock: number, formData?: any) => {\r\n      if (!formData || !formData.stock) return true;\r\n      return minimumStock <= formData.stock;\r\n    },\r\n    message\r\n  }),\r\n\r\n  arrayNotEmpty: (message: string = 'At least one item is required'): ValidationRule => ({\r\n    validator: (value: any[]) => Array.isArray(value) && value.length > 0,\r\n    message\r\n  }),\r\n\r\n  imageArray: (maxFiles: number = 10, message?: string): ValidationRule => ({\r\n    validator: (value: any[]) => {\r\n      if (!Array.isArray(value)) return false;\r\n      return value.length <= maxFiles;\r\n    },\r\n    message: message || `Maximum ${maxFiles} images allowed`\r\n  })\r\n};\r\n\r\n", "/**\r\n * Image Upload Component\r\n * \r\n * A reusable component for uploading and previewing images with drag and drop support.\r\n */\r\n\r\nimport React, { useState, useRef, useCallback } from 'react';\r\nimport { PhotoIcon, XMarkIcon } from '@heroicons/react/24/outline';\r\nimport { validateFile } from '../../utils/errorHandling';\r\n\r\ninterface ImageUploadProps {\r\n  label: string;\r\n  name: string;\r\n  value?: File | string | null;\r\n  onChange: (file: File | null) => void;\r\n  error?: string | undefined;\r\n  required?: boolean;\r\n  disabled?: boolean;\r\n  maxSize?: number; // in bytes\r\n  allowedTypes?: string[];\r\n  className?: string;\r\n}\r\n\r\nconst ImageUpload: React.FC<ImageUploadProps> = ({\r\n  label,\r\n  name,\r\n  value,\r\n  onChange,\r\n  error,\r\n  required = false,\r\n  disabled = false,\r\n  maxSize = 5 * 1024 * 1024, // 5MB default\r\n  allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],\r\n  className = ''\r\n}) => {\r\n  const [isDragOver, setIsDragOver] = useState(false);\r\n  const [preview, setPreview] = useState<string | null>(null);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  // Generate preview URL when value changes\r\n  React.useEffect(() => {\r\n    if (value instanceof File) {\r\n      const url = URL.createObjectURL(value);\r\n      setPreview(url);\r\n      return () => URL.revokeObjectURL(url);\r\n    } else if (typeof value === 'string' && value) {\r\n      setPreview(value);\r\n      return;\r\n    } else {\r\n      setPreview(null);\r\n      return;\r\n    }\r\n  }, [value]);\r\n\r\n  const handleFileSelect = useCallback((file: File) => {\r\n    const validation = validateFile(file, {\r\n      maxSize,\r\n      allowedTypes\r\n    });\r\n\r\n    if (validation.valid) {\r\n      onChange(file);\r\n    } else {\r\n      // Handle validation error - you might want to show this error\r\n      console.error('File validation failed:', validation.error);\r\n    }\r\n  }, [maxSize, allowedTypes, onChange]);\r\n\r\n  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = e.target.files?.[0];\r\n    if (file) {\r\n      handleFileSelect(file);\r\n    }\r\n  };\r\n\r\n  const handleDragOver = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    if (!disabled) {\r\n      setIsDragOver(true);\r\n    }\r\n  };\r\n\r\n  const handleDragLeave = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragOver(false);\r\n  };\r\n\r\n  const handleDrop = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragOver(false);\r\n    \r\n    if (disabled) return;\r\n\r\n    const file = e.dataTransfer.files?.[0];\r\n    if (file) {\r\n      handleFileSelect(file);\r\n    }\r\n  };\r\n\r\n  const handleRemove = () => {\r\n    onChange(null);\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  const handleClick = () => {\r\n    if (!disabled && fileInputRef.current) {\r\n      fileInputRef.current.click();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={className}>\r\n      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n        {label} {required && <span className=\"text-red-500\">*</span>}\r\n      </label>\r\n      \r\n      <div\r\n        className={`\r\n          relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors\r\n          ${isDragOver ? 'border-primary bg-primary bg-opacity-5' : 'border-gray-300'}\r\n          ${error ? 'border-red-300' : ''}\r\n          ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-primary hover:bg-gray-50'}\r\n        `}\r\n        onDragOver={handleDragOver}\r\n        onDragLeave={handleDragLeave}\r\n        onDrop={handleDrop}\r\n        onClick={handleClick}\r\n      >\r\n        <input\r\n          ref={fileInputRef}\r\n          type=\"file\"\r\n          name={name}\r\n          accept={allowedTypes.join(',')}\r\n          onChange={handleFileInputChange}\r\n          className=\"hidden\"\r\n          disabled={disabled}\r\n        />\r\n\r\n        {preview ? (\r\n          <div className=\"relative\">\r\n            <img\r\n              src={preview}\r\n              alt=\"Preview\"\r\n              className=\"mx-auto h-32 w-32 object-cover rounded-lg\"\r\n            />\r\n            {!disabled && (\r\n              <button\r\n                type=\"button\"\r\n                onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  handleRemove();\r\n                }}\r\n                className=\"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors\"\r\n              >\r\n                <XMarkIcon className=\"h-4 w-4\" />\r\n              </button>\r\n            )}\r\n          </div>\r\n        ) : (\r\n          <div>\r\n            <PhotoIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\r\n            <div className=\"mt-4\">\r\n              <p className=\"text-sm text-gray-600\">\r\n                {isDragOver ? 'Drop image here' : 'Click to upload or drag and drop'}\r\n              </p>\r\n              <p className=\"text-xs text-gray-500 mt-1\">\r\n                PNG, JPG, GIF up to {Math.round(maxSize / 1024 / 1024)}MB\r\n              </p>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {error && <p className=\"mt-1 text-sm text-red-600\">{error}</p>}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ImageUpload;\r\n", "import * as React from \"react\";\nfunction MagnifyingGlassIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MagnifyingGlassIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ChevronUpIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m4.5 15.75 7.5-7.5 7.5 7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChevronUpIcon);\nexport default ForwardRef;", "/**\r\n * DataTable Component\r\n *\r\n * A reusable data table component with sorting, filtering, pagination, and row selection.\r\n */\r\n\r\nimport React, { useState, useMemo, memo } from 'react';\r\nimport { MagnifyingGlassIcon, ChevronUpIcon, ChevronDownIcon } from '@heroicons/react/24/outline';\r\nimport LoadingSpinner from './LoadingSpinner';\r\nimport { CONFIG } from '../../constants/config';\r\n\r\nexport interface Column<T = Record<string, any>> {\r\n  key: string;\r\n  label: string;\r\n  sortable?: boolean;\r\n  render?: (value: any, row: T) => React.ReactNode;\r\n  width?: string;\r\n  align?: 'left' | 'center' | 'right';\r\n  className?: string;\r\n}\r\n\r\nexport interface DataTableProps<T = Record<string, any>> {\r\n  columns: Column<T>[];\r\n  data: T[];\r\n  onRowClick?: ((row: T) => void) | undefined;\r\n  title?: string | React.ReactNode;\r\n  description?: string | React.ReactNode;\r\n  loading?: boolean;\r\n  pagination?: boolean;\r\n  pageSize?: number;\r\n  selectable?: boolean;\r\n  onSelectionChange?: (selectedRows: T[]) => void;\r\n  actions?: React.ReactNode;\r\n  emptyMessage?: string;\r\n  className?: string;\r\n  headerClassName?: string;\r\n  bodyClassName?: string;\r\n  footerClassName?: string;\r\n  rowClassName?: (row: T, index: number) => string;\r\n  initialSortKey?: string;\r\n  initialSortDirection?: 'asc' | 'desc';\r\n  testId?: string;\r\n}\r\n\r\nfunction DataTable<T extends Record<string, any>>({\r\n  columns,\r\n  data,\r\n  onRowClick,\r\n  title,\r\n  description,\r\n  loading = false,\r\n  pagination = true,\r\n  pageSize = CONFIG.DEFAULT_PAGE_SIZE,\r\n  selectable = true,\r\n  onSelectionChange,\r\n  actions,\r\n  emptyMessage = 'No results found',\r\n  className = '',\r\n  headerClassName = '',\r\n  bodyClassName = '',\r\n  footerClassName = '',\r\n  rowClassName,\r\n  initialSortKey,\r\n  initialSortDirection = 'asc',\r\n  testId,\r\n}: DataTableProps<T>) {\r\n  // State\r\n  const [sortConfig, setSortConfig] = useState<{\r\n    key: string;\r\n    direction: 'asc' | 'desc';\r\n  } | null>(initialSortKey ? { key: initialSortKey, direction: initialSortDirection } : null);\r\n\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [selectedRows, setSelectedRows] = useState<number[]>([]);\r\n  const [hoveredRow, setHoveredRow] = useState<number | null>(null);\r\n\r\n  // Sorting\r\n  const handleSort = (key: string) => {\r\n    let direction: 'asc' | 'desc' = 'asc';\r\n    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {\r\n      direction = 'desc';\r\n    }\r\n    setSortConfig({ key, direction });\r\n  };\r\n\r\n  const sortedData = useMemo(() => {\r\n    if (!sortConfig) return data;\r\n\r\n    return [...data].sort((a, b) => {\r\n      const aValue = a[sortConfig.key];\r\n      const bValue = b[sortConfig.key];\r\n\r\n      // Handle null or undefined values\r\n      if (aValue == null && bValue == null) return 0;\r\n      if (aValue == null) return sortConfig.direction === 'asc' ? -1 : 1;\r\n      if (bValue == null) return sortConfig.direction === 'asc' ? 1 : -1;\r\n\r\n      // Handle different data types\r\n      if (typeof aValue === 'string' && typeof bValue === 'string') {\r\n        return sortConfig.direction === 'asc'\r\n          ? aValue.localeCompare(bValue)\r\n          : bValue.localeCompare(aValue);\r\n      }\r\n\r\n      if (aValue < bValue) {\r\n        return sortConfig.direction === 'asc' ? -1 : 1;\r\n      }\r\n      if (aValue > bValue) {\r\n        return sortConfig.direction === 'asc' ? 1 : -1;\r\n      }\r\n      return 0;\r\n    });\r\n  }, [data, sortConfig]);\r\n\r\n  // Filtering\r\n  const filteredData = useMemo(() => {\r\n    if (!searchTerm) return sortedData;\r\n\r\n    return sortedData.filter((row) =>\r\n      Object.entries(row).some(([_key, value]) => {\r\n        // Skip filtering on complex objects\r\n        if (value === null || value === undefined) return false;\r\n        if (typeof value === 'object') return false;\r\n\r\n        return String(value).toLowerCase().includes(searchTerm.toLowerCase());\r\n      })\r\n    );\r\n  }, [sortedData, searchTerm]);\r\n\r\n  // Pagination\r\n  const totalPages = Math.ceil(filteredData.length / pageSize);\r\n  const paginatedData = useMemo(() => {\r\n    const startIndex = (currentPage - 1) * pageSize;\r\n    return filteredData.slice(startIndex, startIndex + pageSize);\r\n  }, [filteredData, currentPage, pageSize]);\r\n\r\n  const handlePageChange = (page: number) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  // Row selection\r\n  const handleRowSelect = (index: number, event: React.MouseEvent) => {\r\n    event.stopPropagation();\r\n\r\n    const newSelectedRows = [...selectedRows];\r\n\r\n    if (selectedRows.includes(index)) {\r\n      const idx = newSelectedRows.indexOf(index);\r\n      newSelectedRows.splice(idx, 1);\r\n    } else {\r\n      newSelectedRows.push(index);\r\n    }\r\n\r\n    setSelectedRows(newSelectedRows);\r\n\r\n    if (onSelectionChange) {\r\n      const selectedItems = newSelectedRows\r\n        .map(idx => paginatedData[idx])\r\n        .filter((item): item is T => item !== undefined);\r\n      onSelectionChange(selectedItems);\r\n    }\r\n  };\r\n\r\n  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const newSelectedRows = event.target.checked\r\n      ? Array.from({ length: paginatedData.length }, (_, i) => i)\r\n      : [];\r\n\r\n    setSelectedRows(newSelectedRows);\r\n\r\n    if (onSelectionChange) {\r\n      const selectedItems = newSelectedRows\r\n        .map(idx => paginatedData[idx])\r\n        .filter((item): item is T => item !== undefined);\r\n      onSelectionChange(selectedItems);\r\n    }\r\n  };\r\n\r\n  // Status badge renderer\r\n  const renderStatusBadge = (status: string) => {\r\n    let bgColor = 'bg-gray-100 text-gray-800';\r\n\r\n    if (typeof status === 'string') {\r\n      const statusLower = status.toLowerCase();\r\n\r\n      if (statusLower.includes('active') || statusLower.includes('approved') ||\r\n          statusLower.includes('verified') || statusLower.includes('completed') ||\r\n          statusLower.includes('success')) {\r\n        bgColor = 'bg-green-100 text-green-800';\r\n      } else if (statusLower.includes('pending') || statusLower.includes('processing')) {\r\n        bgColor = 'bg-yellow-100 text-yellow-800';\r\n      } else if (statusLower.includes('rejected') || statusLower.includes('banned') ||\r\n                statusLower.includes('failed') || statusLower.includes('error')) {\r\n        bgColor = 'bg-red-100 text-red-800';\r\n      } else if (statusLower.includes('inactive')) {\r\n        bgColor = 'bg-gray-100 text-gray-800';\r\n      }\r\n    }\r\n\r\n    return (\r\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${bgColor}`}>\r\n        {status}\r\n      </span>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-md ${className}`}\r\n      data-testid={testId}\r\n    >\r\n      {/* Header */}\r\n      {(title || description) && (\r\n        <div className={`px-6 py-4 border-b border-gray-100 ${headerClassName}`}>\r\n          {typeof title === 'string' ? (\r\n            <h3 className=\"text-lg font-semibold text-gray-800\">{title}</h3>\r\n          ) : (\r\n            title\r\n          )}\r\n          {typeof description === 'string' ? (\r\n            <p className=\"mt-1 text-sm text-gray-500\">{description}</p>\r\n          ) : (\r\n            description\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      {/* Search and Actions */}\r\n      <div className=\"p-4 border-b border-gray-100 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\r\n        <div className=\"relative flex-1\">\r\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n            <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\r\n          </div>\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"Search...\"\r\n            className=\"block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200\"\r\n            value={searchTerm}\r\n            onChange={(e) => {\r\n              setSearchTerm(e.target.value);\r\n              setCurrentPage(1); // Reset to first page on search\r\n            }}\r\n            data-testid={`${testId}-search`}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-2\">\r\n          {selectedRows.length > 0 && (\r\n            <div className=\"flex items-center space-x-2\">\r\n              <span className=\"text-sm text-gray-500\">{selectedRows.length} selected</span>\r\n              <button\r\n                className=\"px-3 py-1.5 bg-red-50 text-red-600 rounded-md text-sm font-medium hover:bg-red-100 transition-colors\"\r\n                onClick={() => {\r\n                  setSelectedRows([]);\r\n                  if (onSelectionChange) onSelectionChange([]);\r\n                }}\r\n                data-testid={`${testId}-clear-selection`}\r\n              >\r\n                Clear\r\n              </button>\r\n            </div>\r\n          )}\r\n          {actions}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Table */}\r\n      <div className={`overflow-x-auto ${bodyClassName}`}>\r\n        {loading ? (\r\n          <div className=\"flex justify-center items-center py-20\">\r\n            <LoadingSpinner size=\"lg\" variant=\"spinner\" />\r\n          </div>\r\n        ) : (\r\n          <table className=\"min-w-full divide-y divide-gray-100\">\r\n            <thead className=\"bg-gray-50\">\r\n              <tr>\r\n                {selectable && (\r\n                  <th className=\"w-12 px-6 py-3\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      className=\"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary\"\r\n                      onChange={handleSelectAll}\r\n                      checked={selectedRows.length === paginatedData.length && paginatedData.length > 0}\r\n                      data-testid={`${testId}-select-all`}\r\n                    />\r\n                  </th>\r\n                )}\r\n                {columns.map((column) => (\r\n                  <th\r\n                    key={column.key}\r\n                    className={`px-6 py-3 text-${column.align || 'left'} text-xs font-medium text-gray-500 uppercase tracking-wider ${column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''} transition-colors duration-200 ${column.width ? column.width : ''} ${column.className || ''}`}\r\n                    onClick={() => column.sortable && handleSort(column.key)}\r\n                    data-testid={`${testId}-column-${column.key}`}\r\n                  >\r\n                    <div className=\"flex items-center space-x-1\">\r\n                      <span>{column.label}</span>\r\n                      {column.sortable && (\r\n                        <span className={`transition-colors duration-200 ${\r\n                          sortConfig?.key === column.key ? 'text-primary' : 'text-gray-400'\r\n                        }`}>\r\n                          {sortConfig?.key === column.key && sortConfig.direction === 'asc'\r\n                            ? <ChevronUpIcon className=\"h-4 w-4\" />\r\n                            : sortConfig?.key === column.key && sortConfig.direction === 'desc'\r\n                              ? <ChevronDownIcon className=\"h-4 w-4\" />\r\n                              : <span className=\"text-gray-300\">↕</span>}\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                  </th>\r\n                ))}\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"bg-white divide-y divide-gray-100\">\r\n              {paginatedData.length > 0 ? (\r\n                paginatedData.map((row, index) => (\r\n                  <tr\r\n                    key={index}\r\n                    className={`group transition-all duration-200 ${\r\n                      onRowClick ? 'cursor-pointer' : ''\r\n                    } ${selectedRows.includes(index) ? 'bg-primary bg-opacity-5' : ''}\r\n                    ${hoveredRow === index ? 'bg-gray-50' : ''}\r\n                    ${rowClassName ? rowClassName(row, index) : ''}`}\r\n                    onClick={() => onRowClick && onRowClick(row)}\r\n                    onMouseEnter={() => setHoveredRow(index)}\r\n                    onMouseLeave={() => setHoveredRow(null)}\r\n                    data-testid={`${testId}-row-${index}`}\r\n                  >\r\n                    {selectable && (\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          className=\"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary\"\r\n                          checked={selectedRows.includes(index)}\r\n                          onChange={() => {}} // Empty handler to avoid React warning about controlled component\r\n                          onClick={(e) => handleRowSelect(index, e)}\r\n                          data-testid={`${testId}-row-${index}-checkbox`}\r\n                        />\r\n                      </td>\r\n                    )}\r\n                    {columns.map((column) => (\r\n                      <td\r\n                        key={column.key}\r\n                        className={`px-6 py-4 whitespace-nowrap text-sm text-gray-600 group-hover:text-gray-900 text-${column.align || 'left'} ${column.className || ''}`}\r\n                        data-testid={`${testId}-row-${index}-cell-${column.key}`}\r\n                      >\r\n                        {column.render\r\n                          ? column.render(row[column.key], row)\r\n                          : column.key.toLowerCase().includes('status')\r\n                            ? renderStatusBadge(row[column.key])\r\n                            : row[column.key]}\r\n                      </td>\r\n                    ))}\r\n                  </tr>\r\n                ))\r\n              ) : (\r\n                <tr>\r\n                  <td\r\n                    colSpan={columns.length + (selectable ? 1 : 0)}\r\n                    className=\"px-6 py-10 text-center text-gray-500\"\r\n                    data-testid={`${testId}-empty-message`}\r\n                  >\r\n                    {emptyMessage}\r\n                  </td>\r\n                </tr>\r\n              )}\r\n            </tbody>\r\n          </table>\r\n        )}\r\n      </div>\r\n\r\n      {/* Pagination */}\r\n      {pagination && totalPages > 1 && (\r\n        <div className={`px-6 py-4 border-t border-gray-100 flex items-center justify-between ${footerClassName}`}>\r\n          <div className=\"text-sm text-gray-500\">\r\n            Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, filteredData.length)} of {filteredData.length} entries\r\n          </div>\r\n          <div className=\"flex space-x-1\">\r\n            <button\r\n              onClick={() => handlePageChange(Math.max(1, currentPage - 1))}\r\n              disabled={currentPage === 1}\r\n              className={`px-3 py-1 rounded-md text-sm ${\r\n                currentPage === 1\r\n                  ? 'text-gray-400 cursor-not-allowed'\r\n                  : 'text-gray-700 hover:bg-gray-100'\r\n              }`}\r\n              data-testid={`${testId}-pagination-prev`}\r\n            >\r\n              Previous\r\n            </button>\r\n            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\r\n              // Show pages around current page\r\n              let pageNum: number;\r\n              if (totalPages <= 5) {\r\n                pageNum = i + 1;\r\n              } else if (currentPage <= 3) {\r\n                pageNum = i + 1;\r\n              } else if (currentPage >= totalPages - 2) {\r\n                pageNum = totalPages - 4 + i;\r\n              } else {\r\n                pageNum = currentPage - 2 + i;\r\n              }\r\n\r\n              return (\r\n                <button\r\n                  key={pageNum}\r\n                  onClick={() => handlePageChange(pageNum)}\r\n                  className={`px-3 py-1 rounded-md text-sm ${\r\n                    currentPage === pageNum\r\n                      ? 'bg-primary text-white'\r\n                      : 'text-gray-700 hover:bg-gray-100'\r\n                  }`}\r\n                  data-testid={`${testId}-pagination-${pageNum}`}\r\n                >\r\n                  {pageNum}\r\n                </button>\r\n              );\r\n            })}\r\n            <button\r\n              onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}\r\n              disabled={currentPage === totalPages}\r\n              className={`px-3 py-1 rounded-md text-sm ${\r\n                currentPage === totalPages\r\n                  ? 'text-gray-400 cursor-not-allowed'\r\n                  : 'text-gray-700 hover:bg-gray-100'\r\n              }`}\r\n              data-testid={`${testId}-pagination-next`}\r\n            >\r\n              Next\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default memo(DataTable) as typeof DataTable;\r\n\r\n\r\n\r\n\r\n", "/**\r\n * Modal Component\r\n * \r\n * A reusable modal dialog component.\r\n */\r\n\r\nimport React, { Fragment, useEffect, useRef, memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\nimport { XMarkIcon } from '@heroicons/react/24/outline';\r\nimport { createPortal } from 'react-dom';\r\n\r\nexport interface ModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title: string | ReactNode;\r\n  children: ReactNode;\r\n  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full';\r\n  footer?: ReactNode;\r\n  closeOnEsc?: boolean;\r\n  closeOnBackdropClick?: boolean;\r\n  showCloseButton?: boolean;\r\n  centered?: boolean;\r\n  className?: string;\r\n  bodyClassName?: string;\r\n  headerClassName?: string;\r\n  footerClassName?: string;\r\n  backdropClassName?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst Modal: React.FC<ModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  children,\r\n  size = 'md',\r\n  footer,\r\n  closeOnEsc = true,\r\n  closeOnBackdropClick = true,\r\n  showCloseButton = true,\r\n  centered = true,\r\n  className = '',\r\n  bodyClassName = '',\r\n  headerClassName = '',\r\n  footerClassName = '',\r\n  backdropClassName = '',\r\n  testId,\r\n}) => {\r\n  const modalRef = useRef<HTMLDivElement>(null);\r\n  \r\n  // Handle Escape key press\r\n  useEffect(() => {\r\n    const handleEscape = (e: KeyboardEvent) => {\r\n      if (closeOnEsc && e.key === 'Escape') {\r\n        onClose();\r\n      }\r\n    };\r\n\r\n    if (isOpen) {\r\n      document.addEventListener('keydown', handleEscape);\r\n      // Prevent scrolling on the body when modal is open\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener('keydown', handleEscape);\r\n      document.body.style.overflow = 'auto';\r\n    };\r\n  }, [isOpen, onClose, closeOnEsc]);\r\n  \r\n  // Focus trap inside modal\r\n  useEffect(() => {\r\n    if (!isOpen || !modalRef.current) return;\r\n    \r\n    const focusableElements = modalRef.current.querySelectorAll(\r\n      'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\r\n    );\r\n    \r\n    if (focusableElements.length === 0) return;\r\n    \r\n    const firstElement = focusableElements[0] as HTMLElement;\r\n    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;\r\n    \r\n    const handleTabKey = (e: KeyboardEvent) => {\r\n      if (e.key !== 'Tab') return;\r\n      \r\n      if (e.shiftKey) {\r\n        if (document.activeElement === firstElement) {\r\n          lastElement.focus();\r\n          e.preventDefault();\r\n        }\r\n      } else {\r\n        if (document.activeElement === lastElement) {\r\n          firstElement.focus();\r\n          e.preventDefault();\r\n        }\r\n      }\r\n    };\r\n    \r\n    document.addEventListener('keydown', handleTabKey);\r\n    firstElement.focus();\r\n    \r\n    return () => {\r\n      document.removeEventListener('keydown', handleTabKey);\r\n    };\r\n  }, [isOpen]);\r\n\r\n  if (!isOpen) return null;\r\n  \r\n  // Size classes\r\n  const sizeClasses = {\r\n    xs: 'max-w-xs',\r\n    sm: 'max-w-md',\r\n    md: 'max-w-lg',\r\n    lg: 'max-w-2xl',\r\n    xl: 'max-w-4xl',\r\n    full: 'max-w-full mx-4',\r\n  };\r\n  \r\n  // Modal content\r\n  const modalContent = (\r\n    <Fragment>\r\n      {/* Backdrop */}\r\n      <div \r\n        className={`fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity ${backdropClassName}`}\r\n        onClick={closeOnBackdropClick ? onClose : undefined}\r\n        data-testid={`${testId}-backdrop`}\r\n      />\r\n\r\n      {/* Modal */}\r\n      <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n        <div className={`flex min-h-full items-${centered ? 'center' : 'start'} justify-center p-4 text-center`}>\r\n          <div \r\n            ref={modalRef}\r\n            className={`${sizeClasses[size]} w-full transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all ${className}`}\r\n            onClick={(e) => e.stopPropagation()}\r\n            data-testid={testId}\r\n          >\r\n            {/* Header */}\r\n            <div className={`flex items-center justify-between px-6 py-4 border-b border-gray-100 ${headerClassName}`}>\r\n              {typeof title === 'string' ? (\r\n                <h3 className=\"text-lg font-semibold text-gray-800\">{title}</h3>\r\n              ) : (\r\n                title\r\n              )}\r\n              {showCloseButton && (\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary rounded-full p-1\"\r\n                  onClick={onClose}\r\n                  aria-label=\"Close modal\"\r\n                  data-testid={`${testId}-close-button`}\r\n                >\r\n                  <XMarkIcon className=\"h-6 w-6\" />\r\n                </button>\r\n              )}\r\n            </div>\r\n\r\n            {/* Content */}\r\n            <div className={`px-6 py-4 ${bodyClassName}`}>\r\n              {children}\r\n            </div>\r\n\r\n            {/* Footer */}\r\n            {footer && (\r\n              <div className={`px-6 py-4 bg-gray-50 border-t border-gray-100 flex justify-end space-x-3 ${footerClassName}`}>\r\n                {footer}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Fragment>\r\n  );\r\n  \r\n  // Use portal to render modal at the end of the document body\r\n  return createPortal(modalContent, document.body);\r\n};\r\n\r\nexport default memo(Modal);\r\n", "import * as React from \"react\";\nfunction EnvelopeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EnvelopeIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction TrashIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(TrashIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction EyeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EyeIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction PhoneIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PhoneIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction BuildingOffice2Icon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(BuildingOffice2Icon);\nexport default ForwardRef;", "/**\r\n * Supplier List Component\r\n *\r\n * This component displays a list of suppliers in a data table.\r\n */\r\n\r\nimport React from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport DataTable from '../../../components/common/DataTable';\r\nimport Avatar from '../../../components/common/Avatar';\r\nimport type{ Supplier } from '../types/index';\r\nimport {\r\n  CheckCircleIcon,\r\n  XCircleIcon,\r\n  ClockIcon,\r\n  TrashIcon,\r\n  EyeIcon,\r\n  EnvelopeIcon,\r\n  PhoneIcon\r\n} from '@heroicons/react/24/outline';\r\nimport { ROUTES } from '../../../constants/routes';\r\n\r\ninterface SupplierListProps {\r\n  suppliers: Supplier[];\r\n  onViewSupplier: (supplier: Supplier) => void;\r\n  onDeleteSupplier: (supplier: Supplier) => void;\r\n  onSupplierClick: (supplier: Supplier) => void;\r\n  title?: string;\r\n}\r\n\r\nconst SupplierList: React.FC<SupplierListProps> = ({\r\n  suppliers,\r\n  onViewSupplier,\r\n  onDeleteSupplier,\r\n  onSupplierClick: _onSupplierClick, // Keep for interface compatibility but use navigation instead\r\n  title = 'Suppliers'\r\n}) => {\r\n  const navigate = useNavigate();\r\n\r\n  // Handle row click to navigate to supplier profile page\r\n  const handleRowClick = (supplier: Supplier) => {\r\n    navigate(ROUTES.getSupplierProfileRoute(supplier.id));\r\n  };\r\n  const columns = [\r\n    {\r\n      key: 'name',\r\n      label: 'Supplier Name',\r\n      sortable: true,\r\n      render: (_value: string, supplier: Supplier) => (\r\n        <div className=\"flex items-center\">\r\n          <div className=\"mr-3\">\r\n            <Avatar\r\n              {...(supplier.logo && { src: supplier.logo })}\r\n              alt={supplier.name}\r\n              name={supplier.name}\r\n              size=\"sm\"\r\n            />\r\n          </div>\r\n          <div>\r\n            <div className=\"font-medium text-gray-900\">{supplier.name}</div>\r\n            <div className=\"text-xs text-gray-500\">ID: {supplier.id}</div>\r\n          </div>\r\n        </div>\r\n      )\r\n    },\r\n    {\r\n      key: 'email',\r\n      label: 'Email',\r\n      sortable: true,\r\n      render: (value: string) => (\r\n        <div className=\"flex items-center\">\r\n          <EnvelopeIcon className=\"w-4 h-4 text-gray-400 mr-2\" />\r\n          <span>{value}</span>\r\n        </div>\r\n      )\r\n    },\r\n    {\r\n      key: 'phone',\r\n      label: 'Phone',\r\n      sortable: true,\r\n      render: (value: string) => (\r\n        <div className=\"flex items-center\">\r\n          <PhoneIcon className=\"w-4 h-4 text-gray-400 mr-2\" />\r\n          <span>{value}</span>\r\n        </div>\r\n      )\r\n    },\r\n    {\r\n      key: 'verificationStatus',\r\n      label: 'Status',\r\n      sortable: true,\r\n      render: (value: string) => {\r\n        // Handle undefined or null values\r\n        if (!value) {\r\n          return (\r\n            <div className=\"flex items-center\">\r\n              <ClockIcon className=\"w-4 h-4 text-gray-400 mr-1\" />\r\n              <span>Unknown</span>\r\n            </div>\r\n          );\r\n        }\r\n\r\n        let icon;\r\n        switch(value) {\r\n          case 'verified':\r\n            icon = <CheckCircleIcon className=\"w-4 h-4 text-green-500 mr-1\" />;\r\n            break;\r\n          case 'pending':\r\n            icon = <ClockIcon className=\"w-4 h-4 text-yellow-500 mr-1\" />;\r\n            break;\r\n          case 'rejected':\r\n            icon = <XCircleIcon className=\"w-4 h-4 text-red-500 mr-1\" />;\r\n            break;\r\n          default:\r\n            icon = <ClockIcon className=\"w-4 h-4 text-gray-400 mr-1\" />;\r\n        }\r\n        return (\r\n          <div className=\"flex items-center\">\r\n            {icon}\r\n            <span>{value ? value.charAt(0).toUpperCase() + value.slice(1) : 'Unknown'}</span>\r\n          </div>\r\n        );\r\n      }\r\n    },\r\n\r\n    {\r\n      key: 'actions',\r\n      label: 'Actions',\r\n      render: (_: any, supplier: Supplier) => (\r\n        <div className=\"flex items-center space-x-2\">\r\n          <button\r\n            className=\"p-1 text-gray-500 hover:text-primary rounded-full hover:bg-gray-100\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              onViewSupplier(supplier);\r\n            }}\r\n            title=\"View supplier details\"\r\n          >\r\n            <EyeIcon className=\"w-5 h-5\" />\r\n          </button>\r\n          <button\r\n            className=\"p-1 text-gray-500 hover:text-red-600 rounded-full hover:bg-gray-100\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              onDeleteSupplier(supplier);\r\n            }}\r\n            title=\"Delete supplier\"\r\n          >\r\n            <TrashIcon className=\"w-5 h-5\" />\r\n          </button>\r\n        </div>\r\n      )\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <DataTable\r\n      columns={columns}\r\n      data={suppliers}\r\n      onRowClick={handleRowClick}\r\n      title={title}\r\n      pagination={true}\r\n    />\r\n  );\r\n};\r\n\r\nexport default SupplierList;\r\n", "/**\r\n * Supplier Details Component\r\n * \r\n * This component displays detailed information about a supplier.\r\n */\r\n\r\nimport React from 'react';\r\nimport Avatar from '../../../components/common/Avatar';\r\nimport type{ Supplier } from '../types/index';\r\nimport {\r\n  CheckCircleIcon,\r\n  XCircleIcon,\r\n  ClockIcon\r\n} from '@heroicons/react/24/outline';\r\n\r\ninterface SupplierDetailsProps {\r\n  supplier: Supplier;\r\n}\r\n\r\nconst SupplierDetails: React.FC<SupplierDetailsProps> = ({ supplier }) => {\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-4\">\r\n          <Avatar\r\n            {...(supplier.logo && { src: supplier.logo })}\r\n            alt={supplier.name}\r\n            name={supplier.name}\r\n            size=\"xl\"\r\n          />\r\n          <div>\r\n            <h3 className=\"text-lg font-medium text-gray-900\">{supplier.name}</h3>\r\n            <p className=\"text-sm text-gray-500\">ID: {supplier.id}</p>\r\n            <div className=\"mt-1\">\r\n              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\r\n                supplier.verificationStatus === 'verified'\r\n                  ? 'bg-green-100 text-green-800'\r\n                  : supplier.verificationStatus === 'pending'\r\n                    ? 'bg-yellow-100 text-yellow-800'\r\n                    : 'bg-red-100 text-red-800'\r\n              }`}>\r\n                {supplier.verificationStatus ?\r\n                  supplier.verificationStatus.charAt(0).toUpperCase() + supplier.verificationStatus.slice(1) :\r\n                  'Unknown'\r\n                }\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        {supplier.website && (\r\n          <a\r\n            href={supplier.website}\r\n            target=\"_blank\"\r\n            rel=\"noopener noreferrer\"\r\n            className=\"text-primary hover:underline text-sm\"\r\n          >\r\n            Visit Website\r\n          </a>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 border-t border-gray-200 pt-4\">\r\n        <div>\r\n          <h4 className=\"text-sm font-medium text-gray-500 mb-3\">Contact Information</h4>\r\n          <dl className=\"space-y-3\">\r\n            <div>\r\n              <dt className=\"text-xs text-gray-500\">Contact Person</dt>\r\n              <dd className=\"text-sm text-gray-900\">{supplier.contactPerson}</dd>\r\n            </div>\r\n            <div>\r\n              <dt className=\"text-xs text-gray-500\">Email</dt>\r\n              <dd className=\"text-sm text-gray-900\">{supplier.email}</dd>\r\n            </div>\r\n            <div>\r\n              <dt className=\"text-xs text-gray-500\">Phone</dt>\r\n              <dd className=\"text-sm text-gray-900\">{supplier.phone}</dd>\r\n            </div>\r\n          </dl>\r\n        </div>\r\n\r\n        <div>\r\n          <h4 className=\"text-sm font-medium text-gray-500 mb-3\">Address</h4>\r\n          <address className=\"not-italic text-sm text-gray-900\">\r\n            {supplier.address}\r\n          </address>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"border-t border-gray-200 pt-4\">\r\n        <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Categories</h4>\r\n        <div className=\"flex flex-wrap gap-2\">\r\n          {supplier.categories && supplier.categories.length > 0 ? (\r\n            supplier.categories.map((category, index) => (\r\n              <span\r\n                key={index}\r\n                className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\"\r\n              >\r\n                {category}\r\n              </span>\r\n            ))\r\n          ) : (\r\n            <span className=\"text-gray-500 text-sm\">No categories assigned</span>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"border-t border-gray-200 pt-4\">\r\n        <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Verification Status</h4>\r\n        <div className=\"flex items-center space-x-2\">\r\n          {supplier.verificationStatus === 'verified' ? (\r\n            <CheckCircleIcon className=\"w-5 h-5 text-green-500\" />\r\n          ) : supplier.verificationStatus === 'pending' ? (\r\n            <ClockIcon className=\"w-5 h-5 text-yellow-500\" />\r\n          ) : (\r\n            <XCircleIcon className=\"w-5 h-5 text-red-500\" />\r\n          )}\r\n          <span className=\"text-sm text-gray-700\">\r\n            {supplier.verificationStatus === 'verified'\r\n              ? 'Verified'\r\n              : supplier.verificationStatus === 'pending'\r\n                ? 'Pending verification'\r\n                : 'Rejected'}\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SupplierDetails;\r\n", "/**\r\n * Add Supplier Form Component\r\n *\r\n * This component provides a form for adding new suppliers.\r\n */\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Button from '../../../components/common/Button';\r\nimport FormField from '../../../components/common/FormField';\r\nimport ImageUpload from '../../../components/common/ImageUpload';\r\nimport type{ SupplierFormData } from '../types/index';\r\nimport { validateForm, validationRules } from '../../../utils/validation';\r\nimport type { Category } from '../../categories/types';\r\n\r\ninterface AddSupplierFormProps {\r\n  onSubmit: (supplierData: SupplierFormData, setFieldError?: (field: string, message: string) => void) => void;\r\n  onCancel: () => void;\r\n  isLoading?: boolean;\r\n}\r\n\r\nconst AddSupplierForm: React.FC<AddSupplierFormProps> = ({\r\n  onSubmit,\r\n  onCancel,\r\n  isLoading = false\r\n}) => {\r\n  const [formData, setFormData] = useState<SupplierFormData>({\r\n    supplierName: '',\r\n    email: '',\r\n    phone: '',\r\n    address: '',\r\n    businessType: '',\r\n    password: '',\r\n    confirmPassword: '',\r\n    contactPerson: '', // Required field for backend\r\n    image: null\r\n  });\r\n\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n  const [categories, setCategories] = useState<Category[]>([]);\r\n  const [loadingCategories, setLoadingCategories] = useState(true);\r\n\r\n  // Fetch categories for business type dropdown\r\n  useEffect(() => {\r\n    const fetchCategories = async () => {\r\n      try {\r\n        setLoadingCategories(true);\r\n        // For now, use mock data. In production, you would use the categories API\r\n        const mockCategories: Category[] = [\r\n          {\r\n            id: '1',\r\n            name: 'Retail',\r\n            description: 'Retail business',\r\n            productCount: 0,\r\n            subcategoryCount: 0,\r\n            status: 'active',\r\n            createdAt: new Date().toISOString(),\r\n            updatedAt: new Date().toISOString(),\r\n            visibleInSupplierApp: true,\r\n            visibleInCustomerApp: true\r\n          },\r\n          {\r\n            id: '2',\r\n            name: 'Wholesale',\r\n            description: 'Wholesale business',\r\n            productCount: 0,\r\n            subcategoryCount: 0,\r\n            status: 'active',\r\n            createdAt: new Date().toISOString(),\r\n            updatedAt: new Date().toISOString(),\r\n            visibleInSupplierApp: true,\r\n            visibleInCustomerApp: true\r\n          },\r\n          {\r\n            id: '3',\r\n            name: 'Manufacturing',\r\n            description: 'Manufacturing business',\r\n            productCount: 0,\r\n            subcategoryCount: 0,\r\n            status: 'active',\r\n            createdAt: new Date().toISOString(),\r\n            updatedAt: new Date().toISOString(),\r\n            visibleInSupplierApp: true,\r\n            visibleInCustomerApp: true\r\n          },\r\n          {\r\n            id: '4',\r\n            name: 'Technology',\r\n            description: 'Technology business',\r\n            productCount: 0,\r\n            subcategoryCount: 0,\r\n            status: 'active',\r\n            createdAt: new Date().toISOString(),\r\n            updatedAt: new Date().toISOString(),\r\n            visibleInSupplierApp: true,\r\n            visibleInCustomerApp: true\r\n          },\r\n          {\r\n            id: '5',\r\n            name: 'Healthcare',\r\n            description: 'Healthcare business',\r\n            productCount: 0,\r\n            subcategoryCount: 0,\r\n            status: 'active',\r\n            createdAt: new Date().toISOString(),\r\n            updatedAt: new Date().toISOString(),\r\n            visibleInSupplierApp: true,\r\n            visibleInCustomerApp: true\r\n          },\r\n          {\r\n            id: '6',\r\n            name: 'Food & Beverage',\r\n            description: 'Food & Beverage business',\r\n            productCount: 0,\r\n            subcategoryCount: 0,\r\n            status: 'active',\r\n            createdAt: new Date().toISOString(),\r\n            updatedAt: new Date().toISOString(),\r\n            visibleInSupplierApp: true,\r\n            visibleInCustomerApp: true\r\n          },\r\n          {\r\n            id: '7',\r\n            name: 'Automotive',\r\n            description: 'Automotive business',\r\n            productCount: 0,\r\n            subcategoryCount: 0,\r\n            status: 'active',\r\n            createdAt: new Date().toISOString(),\r\n            updatedAt: new Date().toISOString(),\r\n            visibleInSupplierApp: true,\r\n            visibleInCustomerApp: true\r\n          }\r\n        ];\r\n        setCategories(mockCategories);\r\n      } catch (error) {\r\n        console.error('Error fetching categories:', error);\r\n      } finally {\r\n        setLoadingCategories(false);\r\n      }\r\n    };\r\n\r\n    fetchCategories();\r\n  }, []);\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({ ...prev, [name]: value }));\r\n\r\n    // Clear error when field is edited\r\n    if (errors[name]) {\r\n      setErrors(prev => ({ ...prev, [name]: '' }));\r\n    }\r\n  };\r\n\r\n  const handleImageChange = (file: File | null) => {\r\n    setFormData(prev => ({ ...prev, image: file }));\r\n\r\n    // Clear error when field is edited\r\n    if (errors.image) {\r\n      setErrors(prev => ({ ...prev, image: '' }));\r\n    }\r\n  };\r\n\r\n  const validateFormData = () => {\r\n    const formValidationRules = {\r\n      supplierName: [validationRules.required('Supplier name is required')],\r\n      email: [validationRules.required('Email is required'), validationRules.email()],\r\n      phone: [validationRules.required('Phone number is required'), validationRules.phone()],\r\n      address: [validationRules.required('Address is required')],\r\n      businessType: [validationRules.required('Business type is required')],\r\n      password: [validationRules.required('Password is required'), validationRules.password()],\r\n      confirmPassword: [validationRules.required('Confirm password is required'), validationRules.passwordMatch()],\r\n    };\r\n\r\n    const validationErrors = validateForm(formData, formValidationRules);\r\n    setErrors(validationErrors);\r\n    return Object.keys(validationErrors).length === 0;\r\n  };\r\n\r\n  const setFieldError = (field: string, message: string) => {\r\n    setErrors(prev => ({ ...prev, [field]: message }));\r\n  };\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (validateFormData()) {\r\n      onSubmit(formData, setFieldError);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\r\n        <FormField\r\n          label=\"Supplier Name\"\r\n          name=\"supplierName\"\r\n          value={formData.supplierName}\r\n          onChange={handleChange}\r\n          error={errors.supplierName}\r\n          required\r\n        />\r\n\r\n        <FormField\r\n          label=\"Business Type\"\r\n          name=\"businessType\"\r\n          type=\"select\"\r\n          value={formData.businessType}\r\n          onChange={handleChange}\r\n          error={errors.businessType}\r\n          required\r\n          loading={loadingCategories}\r\n          options={[\r\n            { value: '', label: 'Select a business type' },\r\n            ...categories.map((category) => ({\r\n              value: category.name,\r\n              label: category.name\r\n            }))\r\n          ]}\r\n        />\r\n\r\n        <FormField\r\n          label=\"Email Address\"\r\n          name=\"email\"\r\n          type=\"email\"\r\n          value={formData.email}\r\n          onChange={handleChange}\r\n          error={errors.email}\r\n          required\r\n        />\r\n\r\n        <FormField\r\n          label=\"Phone Number\"\r\n          name=\"phone\"\r\n          type=\"tel\"\r\n          value={formData.phone}\r\n          onChange={handleChange}\r\n          error={errors.phone}\r\n          required\r\n        />\r\n\r\n        <FormField\r\n          label=\"Password\"\r\n          name=\"password\"\r\n          type=\"password\"\r\n          value={formData.password}\r\n          onChange={handleChange}\r\n          error={errors.password}\r\n          required\r\n        />\r\n\r\n        <FormField\r\n          label=\"Confirm Password\"\r\n          name=\"confirmPassword\"\r\n          type=\"password\"\r\n          value={formData.confirmPassword}\r\n          onChange={handleChange}\r\n          error={errors.confirmPassword}\r\n          required\r\n        />\r\n\r\n        <FormField\r\n          label=\"Address\"\r\n          name=\"address\"\r\n          value={formData.address}\r\n          onChange={handleChange}\r\n          error={errors.address}\r\n          required\r\n          className=\"sm:col-span-2\"\r\n        />\r\n      </div>\r\n\r\n      {/* Image Upload Field */}\r\n      <ImageUpload\r\n        label=\"Supplier Image\"\r\n        name=\"image\"\r\n        value={formData.image || null}\r\n        onChange={handleImageChange}\r\n        error={errors.image}\r\n        maxSize={5 * 1024 * 1024} // 5MB\r\n        allowedTypes={['image/jpeg', 'image/png', 'image/gif', 'image/webp']}\r\n      />\r\n\r\n      <div className=\"flex justify-end space-x-3\">\r\n        <Button \r\n          type=\"button\" \r\n          variant=\"outline\" \r\n          onClick={onCancel}\r\n          disabled={isLoading}\r\n        >\r\n          Cancel\r\n        </Button>\r\n        <Button \r\n          type=\"submit\" \r\n          loading={isLoading}\r\n        >\r\n          Add Supplier\r\n        </Button>\r\n      </div>\r\n    </form>\r\n  );\r\n};\r\n\r\nexport default AddSupplierForm;\r\n\r\n\r\n\r\n", "/**\r\n * Suppliers Page\r\n *\r\n * This page displays and manages suppliers in the system.\r\n */\r\n\r\nimport React, { useState, useMemo, useCallback } from 'react';\r\nimport Button from '../components/common/Button';\r\nimport Card from '../components/common/Card';\r\nimport Modal from '../components/common/Modal';\r\nimport LoadingSpinner from '../components/common/LoadingSpinner';\r\nimport { BuildingOffice2Icon } from '@heroicons/react/24/outline';\r\nimport SupplierList from '../features/suppliers/components/SupplierList';\r\nimport SupplierDetails from '../features/suppliers/components/SupplierDetails';\r\nimport AddSupplierForm from '../features/suppliers/components/AddSupplierForm';\r\nimport type { Supplier, SupplierFormData } from '../features/suppliers/types';\r\nimport { useSuppliers } from '../features/suppliers/hooks/useSuppliers';\r\nimport useErrorHandler from '../hooks/useErrorHandler';\r\n\r\nconst SuppliersPage: React.FC = () => {\r\n  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'verified' | 'rejected'>('all');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isAddSupplierModalOpen, setIsAddSupplierModalOpen] = useState(false);\r\n  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);\r\n  const [isSupplierDetailsModalOpen, setIsSupplierDetailsModalOpen] = useState(false);\r\n  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);\r\n  const [supplierToDelete, setSupplierToDelete] = useState<Supplier | null>(null);\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n\r\n  // Use the useSuppliers hook for API integration\r\n  const {\r\n    suppliers,\r\n    isLoading: suppliersLoading,\r\n    createEntity: createSupplier,\r\n    deleteEntity: deleteSupplier,\r\n    updateVerificationStatus\r\n  } = useSuppliers();\r\n\r\n  // Error handling\r\n  const {\r\n    withFormErrorHandling,\r\n    clearError\r\n  } = useErrorHandler({\r\n    enableNotifications: true,\r\n    enableReporting: true\r\n  });\r\n\r\n  // Memoize filtered suppliers to prevent unnecessary recalculations\r\n  const filteredSuppliers = useMemo(() => {\r\n    if (activeTab === 'all') return suppliers;\r\n    return suppliers.filter(supplier => supplier.verificationStatus === activeTab);\r\n  }, [suppliers, activeTab]);\r\n\r\n  const handleSupplierClick = (supplier: Supplier) => {\r\n    handleViewSupplier(supplier);\r\n  };\r\n\r\n  const handleViewSupplier = (supplier: Supplier) => {\r\n    setSelectedSupplier(supplier);\r\n    setIsSupplierDetailsModalOpen(true);\r\n  };\r\n\r\n\r\n\r\n  const handleDeleteSupplier = useCallback((supplier: Supplier) => {\r\n    setSupplierToDelete(supplier);\r\n    setIsDeleteModalOpen(true);\r\n  }, []);\r\n\r\n  const confirmDeleteSupplier = useCallback(async () => {\r\n    if (!supplierToDelete) return;\r\n\r\n    setIsDeleting(true);\r\n    const result = await withFormErrorHandling(async () => {\r\n      await deleteSupplier(supplierToDelete.id);\r\n      return true;\r\n    }, undefined, `Delete supplier ${supplierToDelete.name}`);\r\n\r\n    setIsDeleting(false);\r\n    if (result) {\r\n      setIsDeleteModalOpen(false);\r\n      setSupplierToDelete(null);\r\n    } else {\r\n      console.error('Failed to delete supplier');\r\n    }\r\n  }, [supplierToDelete, withFormErrorHandling, deleteSupplier]);\r\n\r\n  const handleAddSupplier = useCallback(async (supplierData: SupplierFormData, setFieldError?: (field: string, message: string) => void) => {\r\n    setIsLoading(true);\r\n    clearError();\r\n\r\n    const result = await withFormErrorHandling(async () => {\r\n      const newSupplier = await createSupplier(supplierData, false); // Don't show notifications from hook\r\n      setIsAddSupplierModalOpen(false);\r\n      return newSupplier;\r\n    }, setFieldError, 'Add Supplier');\r\n\r\n    setIsLoading(false);\r\n\r\n    if (result) {\r\n      console.log('Supplier added successfully:', result);\r\n    }\r\n  }, [withFormErrorHandling, createSupplier, clearError]);\r\n\r\n  const handleVerifySupplier = async (supplierId: string, newStatus: 'verified' | 'pending') => {\r\n    try {\r\n      await updateVerificationStatus(supplierId, newStatus);\r\n      setIsSupplierDetailsModalOpen(false);\r\n    } catch (error) {\r\n      console.error('Error updating verification status:', error);\r\n      // Error notification is handled by the hook\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n        <div>\r\n          <h1 className=\"text-2xl font-bold text-gray-800\">Suppliers</h1>\r\n          <p className=\"mt-1 text-sm text-gray-500\">Manage your suppliers and verify applications</p>\r\n        </div>\r\n        <Button\r\n          icon={<BuildingOffice2Icon className=\"h-5 w-5\" />}\r\n          onClick={() => setIsAddSupplierModalOpen(true)}\r\n        >\r\n          Add Supplier\r\n        </Button>\r\n      </div>\r\n\r\n      <Card>\r\n        <div className=\"flex flex-wrap gap-3 mb-6\">\r\n          <Button\r\n            variant={activeTab === 'all' ? 'primary' : 'outline'}\r\n            size=\"sm\"\r\n            onClick={() => setActiveTab('all')}\r\n          >\r\n            All Suppliers\r\n          </Button>\r\n          <Button\r\n            variant={activeTab === 'pending' ? 'primary' : 'outline'}\r\n            size=\"sm\"\r\n            onClick={() => setActiveTab('pending')}\r\n          >\r\n            Pending Verification\r\n          </Button>\r\n          <Button\r\n            variant={activeTab === 'verified' ? 'primary' : 'outline'}\r\n            size=\"sm\"\r\n            onClick={() => setActiveTab('verified')}\r\n          >\r\n            Verified\r\n          </Button>\r\n          <Button\r\n            variant={activeTab === 'rejected' ? 'primary' : 'outline'}\r\n            size=\"sm\"\r\n            onClick={() => setActiveTab('rejected')}\r\n          >\r\n            Rejected\r\n          </Button>\r\n        </div>\r\n\r\n        {suppliersLoading ? (\r\n          <div className=\"flex justify-center py-8\">\r\n            <LoadingSpinner />\r\n          </div>\r\n        ) : (\r\n          <SupplierList\r\n            suppliers={filteredSuppliers}\r\n            onSupplierClick={handleSupplierClick}\r\n            onViewSupplier={handleViewSupplier}\r\n            onDeleteSupplier={handleDeleteSupplier}\r\n            title={`${activeTab ? activeTab.charAt(0).toUpperCase() + activeTab.slice(1) : 'All'} Suppliers (${filteredSuppliers.length})`}\r\n          />\r\n        )}\r\n      </Card>\r\n\r\n      {/* Add Supplier Modal */}\r\n      <Modal\r\n        isOpen={isAddSupplierModalOpen}\r\n        onClose={() => setIsAddSupplierModalOpen(false)}\r\n        title=\"Add New Supplier\"\r\n        size=\"lg\"\r\n      >\r\n        <AddSupplierForm\r\n          onSubmit={handleAddSupplier}\r\n          onCancel={() => setIsAddSupplierModalOpen(false)}\r\n          isLoading={isLoading}\r\n        />\r\n      </Modal>\r\n\r\n      {/* Supplier Details Modal */}\r\n      {selectedSupplier && (\r\n        <Modal\r\n          isOpen={isSupplierDetailsModalOpen}\r\n          onClose={() => setIsSupplierDetailsModalOpen(false)}\r\n          title=\"Supplier Details\"\r\n          size=\"lg\"\r\n          footer={\r\n            <>\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={() => setIsSupplierDetailsModalOpen(false)}\r\n              >\r\n                Close\r\n              </Button>\r\n              {selectedSupplier.verificationStatus === 'pending' && (\r\n                <Button\r\n                  variant=\"success\"\r\n                  onClick={() => handleVerifySupplier(selectedSupplier.id, 'verified')}\r\n                >\r\n                  Verify\r\n                </Button>\r\n              )}\r\n              {selectedSupplier.verificationStatus === 'verified' && (\r\n                <Button\r\n                  variant=\"outline\"\r\n                  onClick={() => handleVerifySupplier(selectedSupplier.id, 'pending')}\r\n                >\r\n                  Set to Pending\r\n                </Button>\r\n              )}\r\n            </>\r\n          }\r\n        >\r\n          <SupplierDetails supplier={selectedSupplier} />\r\n        </Modal>\r\n      )}\r\n\r\n      {/* Delete Confirmation Modal */}\r\n      {supplierToDelete && (\r\n        <Modal\r\n          isOpen={isDeleteModalOpen}\r\n          onClose={() => setIsDeleteModalOpen(false)}\r\n          title=\"Delete Supplier\"\r\n          size=\"sm\"\r\n          footer={\r\n            <>\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={() => setIsDeleteModalOpen(false)}\r\n                disabled={isDeleting}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button\r\n                variant=\"danger\"\r\n                onClick={confirmDeleteSupplier}\r\n                loading={isDeleting}\r\n              >\r\n                Delete Supplier\r\n              </Button>\r\n            </>\r\n          }\r\n        >\r\n          <div className=\"text-sm text-gray-500\">\r\n            Are you sure you want to delete \"{supplierToDelete.name}\"? This action cannot be undone.\r\n          </div>\r\n        </Modal>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SuppliersPage;"], "names": ["options", "arguments", "length", "undefined", "enableNotifications", "enableReporting", "onError", "showNotification", "useNotification", "errorState", "setErrorState", "useState", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorType", "clearError", "useCallback", "handleApiErrorWithState", "context", "apiError", "handleApiError", "notification", "type", "title", "message", "Error", "reportError", "handleValidationErrorWithState", "field", "code", "validationError", "handleValidationError", "handleFormErrorWithState", "setFieldError", "handleFormError", "handleGeneralError", "errorObj", "String", "logError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "async", "operation", "withFormError<PERSON>andling", "isApiError", "isValidationError", "PhotoIcon", "_ref", "svgRef", "titleId", "props", "React", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "id", "strokeLinecap", "strokeLinejoin", "d", "Card", "subtitle", "children", "className", "bodyClassName", "headerClassName", "footerClassName", "icon", "footer", "onClick", "hoverable", "noPadding", "bordered", "loading", "testId", "cardClasses", "headerClasses", "bodyClasses", "footerClasses", "_jsxs", "_jsx", "memo", "label", "name", "value", "onChange", "required", "placeholder", "disabled", "inputClasses", "htmlFor", "renderField", "map", "option", "checked", "isValidEmail", "email", "test", "isValidPhone", "phone", "isValidUrl", "url", "URL", "isRequired", "trim", "Array", "isArray", "isNumeric", "isDecimal", "isAlphanumeric", "isValidDate", "dateString", "date", "Date", "isNaN", "getTime", "doPasswordsMatch", "password", "confirmPassword", "isStrongPassword", "validateForm", "values", "validationRules", "errors", "entries", "for<PERSON>ach", "fieldName", "rules", "key", "validateField", "_name", "formData", "ruleArray", "rule", "validator", "<PERSON><PERSON><PERSON><PERSON>", "min", "max<PERSON><PERSON><PERSON>", "max", "numeric", "decimal", "alphanumeric", "passwordMatch", "confirmPasswordMatch", "sku", "price", "stock", "Number", "isInteger", "minimumStock", "stockConsistency", "arrayNotEmpty", "imageArray", "maxFiles", "maxSize", "allowedTypes", "isDragOver", "setIsDragOver", "preview", "setPreview", "fileInputRef", "useRef", "File", "createObjectURL", "revokeObjectURL", "handleFileSelect", "file", "validation", "validateFile", "valid", "console", "onDragOver", "e", "preventDefault", "onDragLeave", "onDrop", "_e$dataTransfer$files", "dataTransfer", "files", "handleClick", "current", "click", "accept", "join", "_e$target$files", "target", "src", "alt", "stopPropagation", "XMarkIcon", "Math", "round", "MagnifyingGlassIcon", "ChevronUpIcon", "DataTable", "columns", "data", "onRowClick", "description", "pagination", "pageSize", "CONFIG", "DEFAULT_PAGE_SIZE", "selectable", "onSelectionChange", "actions", "emptyMessage", "rowClassName", "initialSortKey", "initialSortDirection", "sortConfig", "setSortConfig", "direction", "searchTerm", "setSearchTerm", "currentPage", "setCurrentPage", "selectedRows", "setSelectedRows", "hoveredRow", "setHoveredRow", "sortedData", "useMemo", "sort", "a", "b", "aValue", "bValue", "localeCompare", "filteredData", "filter", "row", "some", "_ref2", "_key", "toLowerCase", "includes", "totalPages", "ceil", "paginatedData", "startIndex", "slice", "handlePageChange", "page", "renderStatusBadge", "status", "bgColor", "statusLower", "LoadingSpinner", "size", "variant", "event", "newSelectedRows", "from", "_", "i", "selectedItems", "idx", "item", "column", "align", "sortable", "width", "handleSort", "ChevronDownIcon", "index", "onMouseEnter", "onMouseLeave", "handleRowSelect", "indexOf", "splice", "push", "render", "colSpan", "pageNum", "Modal", "isOpen", "onClose", "closeOnEsc", "closeOnBackdropClick", "showCloseButton", "centered", "backdropClassName", "modalRef", "useEffect", "handleEscape", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "focusableElements", "querySelectorAll", "firstElement", "lastElement", "handleTabKey", "shift<PERSON>ey", "activeElement", "focus", "modalContent", "Fragment", "xs", "sm", "md", "lg", "xl", "full", "createPortal", "EnvelopeIcon", "TrashIcon", "EyeIcon", "PhoneIcon", "BuildingOffice2Icon", "suppliers", "onViewSupplier", "onDeleteSupplier", "onSupplierClick", "_onSupplierClick", "navigate", "useNavigate", "_value", "supplier", "Avatar", "logo", "ClockIcon", "CheckCircleIcon", "XCircleIcon", "char<PERSON>t", "toUpperCase", "ROUTES", "getSupplierProfileRoute", "verificationStatus", "website", "href", "rel", "<PERSON><PERSON><PERSON>", "address", "categories", "category", "onSubmit", "onCancel", "isLoading", "setFormData", "supplierName", "businessType", "image", "setErrors", "setCategories", "loadingCategories", "setLoadingCategories", "mockCategories", "productCount", "subcategoryCount", "createdAt", "toISOString", "updatedAt", "visibleInSupplierApp", "visibleInCustomerApp", "fetchCategories", "handleChange", "prev", "validateFormData", "formValidationRules", "validationErrors", "keys", "FormField", "ImageUpload", "<PERSON><PERSON>", "SuppliersPage", "activeTab", "setActiveTab", "setIsLoading", "isAddSupplierModalOpen", "setIsAddSupplierModalOpen", "selectedSupplier", "setSelectedSupplier", "isSupplierDetailsModalOpen", "setIsSupplierDetailsModalOpen", "isDeleteModalOpen", "setIsDeleteModalOpen", "supplierToDelete", "setSupplierToDelete", "isDeleting", "setIsDeleting", "suppliersLoading", "createEntity", "createSupplier", "deleteEntity", "deleteSupplier", "updateVerificationStatus", "useSuppliers", "useErrorHandler", "filteredSuppliers", "handleViewSupplier", "handleDeleteSupplier", "confirmDeleteSupplier", "result", "handleAddSupplier", "supplierData", "newSupplier", "log", "handleVerifySupplier", "supplierId", "newStatus", "SupplierList", "AddSupplierForm", "_Fragment", "SupplierDetails"], "sourceRoot": ""}