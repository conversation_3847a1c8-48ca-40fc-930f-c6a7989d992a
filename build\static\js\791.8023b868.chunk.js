"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[791],{233:(e,t,r)=>{r.d(t,{B:()=>n});var s=r(5043),a=r(9705);const n=(e,t)=>{const[r,n]=(0,s.useState)([]),[l,i]=(0,s.useState)(!1),[o,c]=(0,s.useState)(null),{showNotification:d}=(0,a.A)(),u=(0,s.useRef)(e),m=(0,s.useRef)(d),g=(0,s.useRef)(t.entityName),h=(0,s.useRef)(!1);(0,s.useEffect)((()=>{u.current=e,m.current=d,g.current=t.entityName}));const y=(0,s.useCallback)((async e=>{i(!0),c(null);try{const t=await u.current.getAll(e);return n(t),t}catch(t){const e=t;throw c(e),m.current({type:"error",title:"Error",message:`Failed to fetch ${g.current}`}),e}finally{i(!1)}}),[]),p=(0,s.useCallback)((async e=>{i(!0),c(null);try{return await u.current.getById(e)}catch(t){const e=t;throw c(e),m.current({type:"error",title:"Error",message:`Failed to fetch ${g.current}`}),e}finally{i(!1)}}),[]),x=(0,s.useCallback)((async e=>{i(!0),c(null);try{const t=await u.current.create(e);return n((e=>[...e,t])),m.current({type:"success",title:"Success",message:`${g.current} created successfully`}),t}catch(t){const e=t;throw c(e),m.current({type:"error",title:"Error",message:`Failed to create ${g.current}`}),e}finally{i(!1)}}),[]),f=(0,s.useCallback)((async(e,t)=>{i(!0),c(null);try{const r=await u.current.update(e,t);return n((t=>t.map((t=>t.id===e?r:t)))),m.current({type:"success",title:"Success",message:`${g.current} updated successfully`}),r}catch(r){const e=r;throw c(e),m.current({type:"error",title:"Error",message:`Failed to update ${g.current}`}),e}finally{i(!1)}}),[]),v=(0,s.useCallback)((async e=>{i(!0),c(null);try{await u.current.delete(e),n((t=>t.filter((t=>t.id!==e)))),m.current({type:"success",title:"Success",message:`${g.current} deleted successfully`})}catch(t){const e=t;throw c(e),m.current({type:"error",title:"Error",message:`Failed to delete ${g.current}`}),e}finally{i(!1)}}),[]);return(0,s.useEffect)((()=>{if(!1!==t.initialFetch&&!h.current){console.log(`[useEntityData] Starting initial fetch for ${t.entityName}`),h.current=!0;const r=async()=>{i(!0),c(null);try{console.log(`[useEntityData] Calling API for ${t.entityName}`);const r=await e.getAll();console.log(`[useEntityData] Received data for ${t.entityName}:`,r),n(r)}catch(r){const e=r;console.error(`[useEntityData] Error fetching ${t.entityName}:`,e),c(e),m.current({type:"error",title:"Error",message:`Failed to fetch ${t.entityName}`})}finally{console.log(`[useEntityData] Finished fetch for ${t.entityName}`),i(!1)}};r()}}),[e,t.entityName,t.initialFetch]),{entities:r,isLoading:l,error:o,fetchEntities:y,getEntityById:p,createEntity:x,updateEntity:f,deleteEntity:v,setEntities:n}}},2811:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(5043);function a(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))}const n=s.forwardRef(a)},3593:(e,t,r)=>{r.d(t,{A:()=>l});var s=r(5043),a=r(579);const n=e=>{let{title:t,subtitle:r,children:s,className:n="",bodyClassName:l="",headerClassName:i="",footerClassName:o="",icon:c,footer:d,onClick:u,hoverable:m=!1,noPadding:g=!1,bordered:h=!0,loading:y=!1,testId:p}=e;const x=`\n    bg-white rounded-xl ${h?"border border-gray-100":""} overflow-hidden transition-all duration-300\n    ${m?"hover:shadow-md hover:border-gray-200 transform hover:-translate-y-1":"shadow-sm"}\n    ${u?"cursor-pointer":""}\n    ${n}\n  `,f=`\n    px-6 py-4 border-b border-gray-100 flex items-center justify-between\n    ${i}\n  `,v=`\n    ${g?"":"p-6"}\n    ${l}\n  `,b=`\n    px-6 py-4 bg-gray-50 border-t border-gray-100\n    ${o}\n  `;return y?(0,a.jsxs)("div",{className:x,"data-testid":p,children:[(t||r||c)&&(0,a.jsxs)("div",{className:f,children:[(0,a.jsxs)("div",{className:"w-full",children:[t&&(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 animate-pulse"}),r&&(0,a.jsx)("div",{className:"h-4 mt-2 bg-gray-200 rounded w-1/2 animate-pulse"})]}),c&&(0,a.jsx)("div",{className:"h-8 w-8 bg-gray-200 rounded-full animate-pulse"})]}),(0,a.jsx)("div",{className:v,children:(0,a.jsx)("div",{className:"h-24 bg-gray-200 rounded animate-pulse"})}),d&&(0,a.jsx)("div",{className:b,children:(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 animate-pulse"})})]}):(0,a.jsxs)("div",{className:x,onClick:u,"data-testid":p,children:[(t||r||c)&&(0,a.jsxs)("div",{className:f,children:[(0,a.jsxs)("div",{children:["string"===typeof t?(0,a.jsx)("h3",{className:"text-lg font-semibold text-primary",children:t}):t,"string"===typeof r?(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:r}):r]}),c&&(0,a.jsx)("div",{className:"text-primary",children:c})]}),(0,a.jsx)("div",{className:v,children:s}),d&&(0,a.jsx)("div",{className:b,children:d})]})},l=(0,s.memo)(n)},3927:(e,t,r)=>{r.d(t,{A:()=>a});r(5043);var s=r(579);const a=e=>{let{size:t="md",className:r="",variant:a="spinner",color:n="#F28B22",useCurrentColor:l=!1}=e;const i={sm:{spinner:"w-5 h-5",dots:"w-1 h-1",pulse:"w-4 h-4",ripple:"w-6 h-6"},md:{spinner:"w-8 h-8",dots:"w-1.5 h-1.5",pulse:"w-6 h-6",ripple:"w-10 h-10"},lg:{spinner:"w-12 h-12",dots:"w-2 h-2",pulse:"w-8 h-8",ripple:"w-16 h-16"}},o=l?"currentColor":n;return"spinner"===a?(0,s.jsxs)("div",{className:`flex justify-center items-center ${r}`,role:"status","aria-label":"Loading",children:[(0,s.jsx)("div",{className:`spinner-smooth rounded-full border-2 border-gray-200 ${i[t].spinner}`,style:{borderTopColor:o,borderRightColor:o}}),(0,s.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"dots"===a?(0,s.jsxs)("div",{className:`flex justify-center items-center space-x-1 dots-bounce ${r}`,role:"status","aria-label":"Loading",children:[(0,s.jsx)("div",{className:`${i[t].dots} rounded-full dot`,style:{backgroundColor:o}}),(0,s.jsx)("div",{className:`${i[t].dots} rounded-full dot`,style:{backgroundColor:o}}),(0,s.jsx)("div",{className:`${i[t].dots} rounded-full dot`,style:{backgroundColor:o}}),(0,s.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"pulse"===a?(0,s.jsxs)("div",{className:`flex justify-center items-center ${r}`,role:"status","aria-label":"Loading",children:[(0,s.jsx)("div",{className:`${i[t].pulse} rounded-full pulse-smooth`,style:{backgroundColor:o}}),(0,s.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"ripple"===a?(0,s.jsxs)("div",{className:`flex justify-center items-center ${r}`,role:"status","aria-label":"Loading",children:[(0,s.jsx)("div",{className:`${i[t].ripple} rounded-full ripple-effect`,style:{color:o},children:(0,s.jsx)("div",{className:`${i[t].pulse} rounded-full pulse-smooth mx-auto`,style:{backgroundColor:o}})}),(0,s.jsx)("span",{className:"sr-only",children:"Loading..."})]}):null}},6773:(e,t,r)=>{r.d(t,{l:()=>g,tU:()=>h});const s=e=>/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(e),a=e=>/^\+?[0-9]{10,15}$/.test(e),n=e=>{try{return new URL(e),!0}catch(t){return!1}},l=e=>null!==e&&void 0!==e&&("string"===typeof e?e.trim().length>0:!Array.isArray(e)||e.length>0),i=e=>/^[0-9]+$/.test(e),o=e=>/^[0-9]+(\.[0-9]+)?$/.test(e),c=e=>/^[a-zA-Z0-9]+$/.test(e),d=e=>{const t=new Date(e);return!isNaN(t.getTime())},u=(e,t)=>e===t,m=e=>!(e.length<8)&&(!!/[A-Z]/.test(e)&&(!!/[a-z]/.test(e)&&(!!/[0-9]/.test(e)&&!!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(e)))),g=(e,t)=>{const r={};return Object.entries(t).forEach((t=>{let[s,a]=t;const n=s,l=((e,t,r,s)=>{const a=Array.isArray(r)?r:[r];for(const n of a)if(!n.validator(t,s))return n.message;return""})(0,e[n],a,e);l&&(r[n]=l)})),r},h={required:function(){return{validator:l,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"This field is required"}},email:function(){return{validator:s,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid email address"}},phone:function(){return{validator:a,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid phone number"}},url:function(){return{validator:n,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid URL"}},minLength:(e,t)=>({validator:t=>((e,t)=>e.length>=t)(t,e),message:t||`Must be at least ${e} characters`}),maxLength:(e,t)=>({validator:t=>((e,t)=>e.length<=t)(t,e),message:t||`Must be no more than ${e} characters`}),numeric:function(){return{validator:i,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a numeric value"}},decimal:function(){return{validator:o,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid decimal number"}},alphanumeric:function(){return{validator:c,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please use only letters and numbers"}},date:function(){return{validator:d,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid date"}},password:function(){return{validator:m,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Password must be at least 8 characters and include uppercase, lowercase, number, and special character"}},passwordMatch:function(){return{validator:(e,t)=>u(e,null===t||void 0===t?void 0:t.confirmPassword),message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Passwords do not match"}},confirmPasswordMatch:function(){return{validator:(e,t)=>u(e,null===t||void 0===t?void 0:t.password),message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Passwords do not match"}},sku:function(){return{validator:e=>/^[A-Z0-9-_]{3,20}$/i.test(e),message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid SKU"}},price:function(){return{validator:e=>e>0&&e<=999999,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid price"}},stock:function(){return{validator:e=>Number.isInteger(e)&&e>=0,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid stock quantity"}},minimumStock:function(){return{validator:e=>Number.isInteger(e)&&e>=0,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid minimum stock level"}},stockConsistency:function(){return{validator:(e,t)=>!t||!t.stock||e<=t.stock,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Minimum stock cannot be greater than current stock"}},arrayNotEmpty:function(){return{validator:e=>Array.isArray(e)&&e.length>0,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"At least one item is required"}},imageArray:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;return{validator:t=>!!Array.isArray(t)&&t.length<=e,message:(arguments.length>1?arguments[1]:void 0)||`Maximum ${e} images allowed`}}}},7422:(e,t,r)=>{r.d(t,{A:()=>g});var s=r(5043);function a(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}const n=s.forwardRef(a);function l(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"}))}const i=s.forwardRef(l);var o=r(2517),c=r(3927),d=r(1308),u=r(579);function m(e){let{columns:t,data:r,onRowClick:a,title:l,description:m,loading:g=!1,pagination:h=!0,pageSize:y=d.PI.DEFAULT_PAGE_SIZE,selectable:p=!0,onSelectionChange:x,actions:f,emptyMessage:v="No results found",className:b="",headerClassName:w="",bodyClassName:N="",footerClassName:j="",rowClassName:k,initialSortKey:$,initialSortDirection:C="asc",testId:E}=e;const[A,L]=(0,s.useState)($?{key:$,direction:C}:null),[P,S]=(0,s.useState)(""),[M,I]=(0,s.useState)(1),[R,F]=(0,s.useState)([]),[z,B]=(0,s.useState)(null),Z=(0,s.useMemo)((()=>A?[...r].sort(((e,t)=>{const r=e[A.key],s=t[A.key];return null==r&&null==s?0:null==r?"asc"===A.direction?-1:1:null==s?"asc"===A.direction?1:-1:"string"===typeof r&&"string"===typeof s?"asc"===A.direction?r.localeCompare(s):s.localeCompare(r):r<s?"asc"===A.direction?-1:1:r>s?"asc"===A.direction?1:-1:0})):r),[r,A]),D=(0,s.useMemo)((()=>P?Z.filter((e=>Object.entries(e).some((e=>{let[t,r]=e;return null!==r&&void 0!==r&&("object"!==typeof r&&String(r).toLowerCase().includes(P.toLowerCase()))})))):Z),[Z,P]),_=Math.ceil(D.length/y),O=(0,s.useMemo)((()=>{const e=(M-1)*y;return D.slice(e,e+y)}),[D,M,y]),W=e=>{I(e)},U=e=>{let t="bg-gray-100 text-gray-800";if("string"===typeof e){const r=e.toLowerCase();r.includes("active")||r.includes("approved")||r.includes("verified")||r.includes("completed")||r.includes("success")?t="bg-green-100 text-green-800":r.includes("pending")||r.includes("processing")?t="bg-yellow-100 text-yellow-800":r.includes("rejected")||r.includes("banned")||r.includes("failed")||r.includes("error")?t="bg-red-100 text-red-800":r.includes("inactive")&&(t="bg-gray-100 text-gray-800")}return(0,u.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${t}`,children:e})};return(0,u.jsxs)("div",{className:`bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-md ${b}`,"data-testid":E,children:[(l||m)&&(0,u.jsxs)("div",{className:`px-6 py-4 border-b border-gray-100 ${w}`,children:["string"===typeof l?(0,u.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:l}):l,"string"===typeof m?(0,u.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:m}):m]}),(0,u.jsxs)("div",{className:"p-4 border-b border-gray-100 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,u.jsxs)("div",{className:"relative flex-1",children:[(0,u.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,u.jsx)(n,{className:"h-5 w-5 text-gray-400"})}),(0,u.jsx)("input",{type:"text",placeholder:"Search...",className:"block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200",value:P,onChange:e=>{S(e.target.value),I(1)},"data-testid":`${E}-search`})]}),(0,u.jsxs)("div",{className:"flex items-center space-x-2",children:[R.length>0&&(0,u.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,u.jsxs)("span",{className:"text-sm text-gray-500",children:[R.length," selected"]}),(0,u.jsx)("button",{className:"px-3 py-1.5 bg-red-50 text-red-600 rounded-md text-sm font-medium hover:bg-red-100 transition-colors",onClick:()=>{F([]),x&&x([])},"data-testid":`${E}-clear-selection`,children:"Clear"})]}),f]})]}),(0,u.jsx)("div",{className:`overflow-x-auto ${N}`,children:g?(0,u.jsx)("div",{className:"flex justify-center items-center py-20",children:(0,u.jsx)(c.A,{size:"lg",variant:"spinner"})}):(0,u.jsxs)("table",{className:"min-w-full divide-y divide-gray-100",children:[(0,u.jsx)("thead",{className:"bg-gray-50",children:(0,u.jsxs)("tr",{children:[p&&(0,u.jsx)("th",{className:"w-12 px-6 py-3",children:(0,u.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary",onChange:e=>{const t=e.target.checked?Array.from({length:O.length},((e,t)=>t)):[];if(F(t),x){const e=t.map((e=>O[e])).filter((e=>void 0!==e));x(e)}},checked:R.length===O.length&&O.length>0,"data-testid":`${E}-select-all`})}),t.map((e=>(0,u.jsx)("th",{className:`px-6 py-3 text-${e.align||"left"} text-xs font-medium text-gray-500 uppercase tracking-wider ${e.sortable?"cursor-pointer hover:bg-gray-100":""} transition-colors duration-200 ${e.width?e.width:""} ${e.className||""}`,onClick:()=>e.sortable&&(e=>{let t="asc";A&&A.key===e&&"asc"===A.direction&&(t="desc"),L({key:e,direction:t})})(e.key),"data-testid":`${E}-column-${e.key}`,children:(0,u.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,u.jsx)("span",{children:e.label}),e.sortable&&(0,u.jsx)("span",{className:"transition-colors duration-200 "+((null===A||void 0===A?void 0:A.key)===e.key?"text-primary":"text-gray-400"),children:(null===A||void 0===A?void 0:A.key)===e.key&&"asc"===A.direction?(0,u.jsx)(i,{className:"h-4 w-4"}):(null===A||void 0===A?void 0:A.key)===e.key&&"desc"===A.direction?(0,u.jsx)(o.A,{className:"h-4 w-4"}):(0,u.jsx)("span",{className:"text-gray-300",children:"\u2195"})})]})},e.key)))]})}),(0,u.jsx)("tbody",{className:"bg-white divide-y divide-gray-100",children:O.length>0?O.map(((e,r)=>(0,u.jsxs)("tr",{className:`group transition-all duration-200 ${a?"cursor-pointer":""} ${R.includes(r)?"bg-primary bg-opacity-5":""}\n                    ${z===r?"bg-gray-50":""}\n                    ${k?k(e,r):""}`,onClick:()=>a&&a(e),onMouseEnter:()=>B(r),onMouseLeave:()=>B(null),"data-testid":`${E}-row-${r}`,children:[p&&(0,u.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,u.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary",checked:R.includes(r),onChange:()=>{},onClick:e=>((e,t)=>{t.stopPropagation();const r=[...R];if(R.includes(e)){const t=r.indexOf(e);r.splice(t,1)}else r.push(e);if(F(r),x){const e=r.map((e=>O[e])).filter((e=>void 0!==e));x(e)}})(r,e),"data-testid":`${E}-row-${r}-checkbox`})}),t.map((t=>(0,u.jsx)("td",{className:`px-6 py-4 whitespace-nowrap text-sm text-gray-600 group-hover:text-gray-900 text-${t.align||"left"} ${t.className||""}`,"data-testid":`${E}-row-${r}-cell-${t.key}`,children:t.render?t.render(e[t.key],e):t.key.toLowerCase().includes("status")?U(e[t.key]):e[t.key]},t.key)))]},r))):(0,u.jsx)("tr",{children:(0,u.jsx)("td",{colSpan:t.length+(p?1:0),className:"px-6 py-10 text-center text-gray-500","data-testid":`${E}-empty-message`,children:v})})})]})}),h&&_>1&&(0,u.jsxs)("div",{className:`px-6 py-4 border-t border-gray-100 flex items-center justify-between ${j}`,children:[(0,u.jsxs)("div",{className:"text-sm text-gray-500",children:["Showing ",(M-1)*y+1," to ",Math.min(M*y,D.length)," of ",D.length," entries"]}),(0,u.jsxs)("div",{className:"flex space-x-1",children:[(0,u.jsx)("button",{onClick:()=>W(Math.max(1,M-1)),disabled:1===M,className:"px-3 py-1 rounded-md text-sm "+(1===M?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"),"data-testid":`${E}-pagination-prev`,children:"Previous"}),Array.from({length:Math.min(5,_)},((e,t)=>{let r;return r=_<=5||M<=3?t+1:M>=_-2?_-4+t:M-2+t,(0,u.jsx)("button",{onClick:()=>W(r),className:"px-3 py-1 rounded-md text-sm "+(M===r?"bg-primary text-white":"text-gray-700 hover:bg-gray-100"),"data-testid":`${E}-pagination-${r}`,children:r},r)})),(0,u.jsx)("button",{onClick:()=>W(Math.min(_,M+1)),disabled:M===_,className:"px-3 py-1 rounded-md text-sm "+(M===_?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"),"data-testid":`${E}-pagination-next`,children:"Next"})]})]})]})}const g=(0,s.memo)(m)},7907:(e,t,r)=>{r.d(t,{A:()=>l});var s=r(5043),a=r(579);const n=e=>{let{children:t,variant:r="primary",size:s="md",className:n="",onClick:l,disabled:i=!1,type:o="button",icon:c,iconPosition:d="left",fullWidth:u=!1,loading:m=!1,rounded:g=!1,href:h,target:y,rel:p,title:x,ariaLabel:f,testId:v}=e;const b=`\n    inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2\n    ${{primary:"bg-primary text-white hover:bg-primary/90 focus-visible:ring-primary",secondary:"bg-gray-200 text-gray-800 hover:bg-gray-300 focus-visible:ring-gray-300",outline:"bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus-visible:ring-primary",danger:"bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500",success:"bg-green-600 text-white hover:bg-green-700 focus-visible:ring-green-500",text:"bg-transparent text-primary hover:bg-gray-100 focus-visible:ring-primary",link:"bg-transparent text-primary hover:underline focus-visible:ring-transparent p-0"}[r]}\n    ${{xs:"text-xs px-2 py-1",sm:"text-xs px-3 py-1.5",md:"text-sm px-4 py-2",lg:"text-base px-5 py-2.5",xl:"text-lg px-6 py-3"}[s]}\n    ${i?"opacity-60 cursor-not-allowed":"cursor-pointer"}\n    ${u?"w-full":""}\n    ${g?"rounded-full":"rounded-lg"}\n    ${n}\n  `,w=(0,a.jsxs)(a.Fragment,{children:[m&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-current",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","aria-hidden":"true",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),c&&"left"===d&&!m&&(0,a.jsx)("span",{className:"mr-2",children:c}),t,c&&"right"===d&&(0,a.jsx)("span",{className:"ml-2",children:c})]});return h?(0,a.jsx)("a",{href:h,className:b,target:y,rel:p||("_blank"===y?"noopener noreferrer":void 0),onClick:l,title:x,"aria-label":f,"data-testid":v,children:w}):(0,a.jsx)("button",{type:o,className:b,onClick:l,disabled:i||m,title:x,"aria-label":f,"data-testid":v,children:w})},l=(0,s.memo)(n)},9248:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(5043);function a(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))}const n=s.forwardRef(a)}}]);
//# sourceMappingURL=791.8023b868.chunk.js.map