"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[814],{1512:(e,t,r)=>{r.d(t,{A:()=>i});var s=r(5043),a=r(4703),n=r(9705);const i=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{enableNotifications:t=!0,enableReporting:r=!0,onError:i}=e,{showNotification:l}=(0,n.A)(),[o,d]=(0,s.useState)({hasError:!1,error:null,errorType:null}),c=(0,s.useCallback)((()=>{d({hasError:!1,error:null,errorType:null})}),[]),u=(0,s.useCallback)(((e,s)=>{const n=(0,a.hS)(e,t?e=>{l({type:e.type,title:e.title,message:e.message})}:void 0);return d({hasError:!0,error:n,errorType:"api",...s&&{context:s}}),r&&e instanceof Error&&(0,a.N7)(e,s),i&&i(e,s),n}),[t,r,l,i]),m=(0,s.useCallback)(((e,r,s,n)=>{const o=(0,a.co)(e,r,s);return d({hasError:!0,error:o,errorType:"validation",...n&&{context:n}}),t&&l({type:"error",title:"Validation Error",message:o.message}),i&&i(o,n),o}),[t,l,i]),p=(0,s.useCallback)(((e,s,n)=>{(0,a.NC)(e,s,t?e=>{l({type:e.type,title:e.title,message:e.message})}:void 0),d({hasError:!0,error:e,errorType:"form",...n&&{context:n}}),r&&e instanceof Error&&(0,a.N7)(e,n),i&&i(e,n)}),[t,r,l,i]),x=(0,s.useCallback)(((e,s)=>{const n=e instanceof Error?e:new Error(String(e));return d({hasError:!0,error:n,errorType:"general",...s&&{context:s}}),t&&l({type:"error",title:"Error",message:n.message}),r&&(0,a.N7)(n,s),(0,a.vV)(n,s),i&&i(e,s),n}),[t,r,l,i]),h=(0,s.useCallback)((async(e,t)=>{try{return c(),await e()}catch(r){return u(r,t),null}}),[c,u]),g=(0,s.useCallback)((async(e,t,r)=>{try{return c(),await e()}catch(s){return p(s,t,r),null}}),[c,p]);return{...o,handleApiError:u,handleValidationError:m,handleFormError:p,handleGeneralError:x,clearError:c,withErrorHandling:h,withFormErrorHandling:g,isApiError:e=>e&&"object"===typeof e&&"status"in e,isValidationError:e=>e&&"object"===typeof e&&"field"in e}}},1602:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(5043);function a(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"}))}const n=s.forwardRef(a)},3593:(e,t,r)=>{r.d(t,{A:()=>i});var s=r(5043),a=r(579);const n=e=>{let{title:t,subtitle:r,children:s,className:n="",bodyClassName:i="",headerClassName:l="",footerClassName:o="",icon:d,footer:c,onClick:u,hoverable:m=!1,noPadding:p=!1,bordered:x=!0,loading:h=!1,testId:g}=e;const v=`\n    bg-white rounded-xl ${x?"border border-gray-100":""} overflow-hidden transition-all duration-300\n    ${m?"hover:shadow-md hover:border-gray-200 transform hover:-translate-y-1":"shadow-sm"}\n    ${u?"cursor-pointer":""}\n    ${n}\n  `,y=`\n    px-6 py-4 border-b border-gray-100 flex items-center justify-between\n    ${l}\n  `,f=`\n    ${p?"":"p-6"}\n    ${i}\n  `,b=`\n    px-6 py-4 bg-gray-50 border-t border-gray-100\n    ${o}\n  `;return h?(0,a.jsxs)("div",{className:v,"data-testid":g,children:[(t||r||d)&&(0,a.jsxs)("div",{className:y,children:[(0,a.jsxs)("div",{className:"w-full",children:[t&&(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 animate-pulse"}),r&&(0,a.jsx)("div",{className:"h-4 mt-2 bg-gray-200 rounded w-1/2 animate-pulse"})]}),d&&(0,a.jsx)("div",{className:"h-8 w-8 bg-gray-200 rounded-full animate-pulse"})]}),(0,a.jsx)("div",{className:f,children:(0,a.jsx)("div",{className:"h-24 bg-gray-200 rounded animate-pulse"})}),c&&(0,a.jsx)("div",{className:b,children:(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 animate-pulse"})})]}):(0,a.jsxs)("div",{className:v,onClick:u,"data-testid":g,children:[(t||r||d)&&(0,a.jsxs)("div",{className:y,children:[(0,a.jsxs)("div",{children:["string"===typeof t?(0,a.jsx)("h3",{className:"text-lg font-semibold text-primary",children:t}):t,"string"===typeof r?(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:r}):r]}),d&&(0,a.jsx)("div",{className:"text-primary",children:d})]}),(0,a.jsx)("div",{className:f,children:s}),c&&(0,a.jsx)("div",{className:b,children:c})]})},i=(0,s.memo)(n)},5901:(e,t,r)=>{r.d(t,{A:()=>a});r(5043);var s=r(579);const a=e=>{let{label:t,name:r,type:a="text",value:n,onChange:i,error:l,required:o=!1,placeholder:d="",options:c=[],className:u="",disabled:m=!1,loading:p=!1}=e;const x="mt-1 block w-full rounded-md shadow-sm sm:text-sm "+(l?"border-red-300 focus:border-red-500 focus:ring-red-500":"border-gray-300 focus:border-primary focus:ring-primary");return(0,s.jsxs)("div",{className:`${u}`,children:[(0,s.jsxs)("label",{htmlFor:r,className:"block text-sm font-medium text-gray-700",children:[t," ",o&&(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(()=>{switch(a){case"textarea":return(0,s.jsx)("textarea",{id:r,name:r,value:n,onChange:i,className:x,placeholder:d,disabled:m});case"select":return(0,s.jsx)("select",{id:r,name:r,value:n,onChange:i,className:x,disabled:m||p,children:p?(0,s.jsx)("option",{value:"",children:"Loading..."}):c.map((e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value)))});case"checkbox":return(0,s.jsx)("input",{type:"checkbox",id:r,name:r,checked:n,onChange:i,className:"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary",disabled:m});default:return(0,s.jsx)("input",{type:a,id:r,name:r,value:n,onChange:i,className:x,placeholder:d,disabled:m})}})(),l&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:l})]})}},6773:(e,t,r)=>{r.d(t,{l:()=>p,tU:()=>x});const s=e=>/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(e),a=e=>/^\+?[0-9]{10,15}$/.test(e),n=e=>{try{return new URL(e),!0}catch(t){return!1}},i=e=>null!==e&&void 0!==e&&("string"===typeof e?e.trim().length>0:!Array.isArray(e)||e.length>0),l=e=>/^[0-9]+$/.test(e),o=e=>/^[0-9]+(\.[0-9]+)?$/.test(e),d=e=>/^[a-zA-Z0-9]+$/.test(e),c=e=>{const t=new Date(e);return!isNaN(t.getTime())},u=(e,t)=>e===t,m=e=>!(e.length<8)&&(!!/[A-Z]/.test(e)&&(!!/[a-z]/.test(e)&&(!!/[0-9]/.test(e)&&!!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(e)))),p=(e,t)=>{const r={};return Object.entries(t).forEach((t=>{let[s,a]=t;const n=s,i=((e,t,r,s)=>{const a=Array.isArray(r)?r:[r];for(const n of a)if(!n.validator(t,s))return n.message;return""})(0,e[n],a,e);i&&(r[n]=i)})),r},x={required:function(){return{validator:i,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"This field is required"}},email:function(){return{validator:s,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid email address"}},phone:function(){return{validator:a,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid phone number"}},url:function(){return{validator:n,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid URL"}},minLength:(e,t)=>({validator:t=>((e,t)=>e.length>=t)(t,e),message:t||`Must be at least ${e} characters`}),maxLength:(e,t)=>({validator:t=>((e,t)=>e.length<=t)(t,e),message:t||`Must be no more than ${e} characters`}),numeric:function(){return{validator:l,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a numeric value"}},decimal:function(){return{validator:o,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid decimal number"}},alphanumeric:function(){return{validator:d,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please use only letters and numbers"}},date:function(){return{validator:c,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid date"}},password:function(){return{validator:m,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Password must be at least 8 characters and include uppercase, lowercase, number, and special character"}},passwordMatch:function(){return{validator:(e,t)=>u(e,null===t||void 0===t?void 0:t.confirmPassword),message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Passwords do not match"}},confirmPasswordMatch:function(){return{validator:(e,t)=>u(e,null===t||void 0===t?void 0:t.password),message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Passwords do not match"}},sku:function(){return{validator:e=>/^[A-Z0-9-_]{3,20}$/i.test(e),message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid SKU"}},price:function(){return{validator:e=>e>0&&e<=999999,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid price"}},stock:function(){return{validator:e=>Number.isInteger(e)&&e>=0,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid stock quantity"}},minimumStock:function(){return{validator:e=>Number.isInteger(e)&&e>=0,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Please enter a valid minimum stock level"}},stockConsistency:function(){return{validator:(e,t)=>!t||!t.stock||e<=t.stock,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Minimum stock cannot be greater than current stock"}},arrayNotEmpty:function(){return{validator:e=>Array.isArray(e)&&e.length>0,message:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"At least one item is required"}},imageArray:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;return{validator:t=>!!Array.isArray(t)&&t.length<=e,message:(arguments.length>1?arguments[1]:void 0)||`Maximum ${e} images allowed`}}}},7147:(e,t,r)=>{r.d(t,{A:()=>o});var s=r(5043),a=r(7591),n=r(1602),i=r(4703),l=r(579);const o=e=>{let{label:t,name:r,value:o,onChange:d,error:c,required:u=!1,disabled:m=!1,maxSize:p=5242880,allowedTypes:x=["image/jpeg","image/png","image/gif","image/webp"],className:h=""}=e;const[g,v]=(0,s.useState)(!1),[y,f]=(0,s.useState)(null),b=(0,s.useRef)(null);s.useEffect((()=>{if(o instanceof File){const e=URL.createObjectURL(o);return f(e),()=>URL.revokeObjectURL(e)}return"string"===typeof o&&o?void f(o):void f(null)}),[o]);const j=(0,s.useCallback)((e=>{const t=(0,i.nJ)(e,{maxSize:p,allowedTypes:x});t.valid?d(e):console.error("File validation failed:",t.error)}),[p,x,d]);return(0,l.jsxs)("div",{className:h,children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[t," ",u&&(0,l.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,l.jsxs)("div",{className:`\n          relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors\n          ${g?"border-primary bg-primary bg-opacity-5":"border-gray-300"}\n          ${c?"border-red-300":""}\n          ${m?"opacity-50 cursor-not-allowed":"hover:border-primary hover:bg-gray-50"}\n        `,onDragOver:e=>{e.preventDefault(),m||v(!0)},onDragLeave:e=>{e.preventDefault(),v(!1)},onDrop:e=>{var t;if(e.preventDefault(),v(!1),m)return;const r=null===(t=e.dataTransfer.files)||void 0===t?void 0:t[0];r&&j(r)},onClick:()=>{!m&&b.current&&b.current.click()},children:[(0,l.jsx)("input",{ref:b,type:"file",name:r,accept:x.join(","),onChange:e=>{var t;const r=null===(t=e.target.files)||void 0===t?void 0:t[0];r&&j(r)},className:"hidden",disabled:m}),y?(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("img",{src:y,alt:"Preview",className:"mx-auto h-32 w-32 object-cover rounded-lg"}),!m&&(0,l.jsx)("button",{type:"button",onClick:e=>{e.stopPropagation(),d(null),b.current&&(b.current.value="")},className:"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors",children:(0,l.jsx)(a.A,{className:"h-4 w-4"})})]}):(0,l.jsxs)("div",{children:[(0,l.jsx)(n.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,l.jsxs)("div",{className:"mt-4",children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:g?"Drop image here":"Click to upload or drag and drop"}),(0,l.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["PNG, JPG, GIF up to ",Math.round(p/1024/1024),"MB"]})]})]})]}),c&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:c})]})}},7422:(e,t,r)=>{r.d(t,{A:()=>p});var s=r(5043);function a(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}const n=s.forwardRef(a);function i(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"}))}const l=s.forwardRef(i);var o=r(2517),d=r(3927),c=r(1308),u=r(579);function m(e){let{columns:t,data:r,onRowClick:a,title:i,description:m,loading:p=!1,pagination:x=!0,pageSize:h=c.PI.DEFAULT_PAGE_SIZE,selectable:g=!0,onSelectionChange:v,actions:y,emptyMessage:f="No results found",className:b="",headerClassName:j="",bodyClassName:w="",footerClassName:N="",rowClassName:k,initialSortKey:C,initialSortDirection:A="asc",testId:S}=e;const[E,$]=(0,s.useState)(C?{key:C,direction:A}:null),[I,P]=(0,s.useState)(""),[L,D]=(0,s.useState)(1),[O,M]=(0,s.useState)([]),[q,U]=(0,s.useState)(null),R=(0,s.useMemo)((()=>E?[...r].sort(((e,t)=>{const r=e[E.key],s=t[E.key];return null==r&&null==s?0:null==r?"asc"===E.direction?-1:1:null==s?"asc"===E.direction?1:-1:"string"===typeof r&&"string"===typeof s?"asc"===E.direction?r.localeCompare(s):s.localeCompare(r):r<s?"asc"===E.direction?-1:1:r>s?"asc"===E.direction?1:-1:0})):r),[r,E]),T=(0,s.useMemo)((()=>I?R.filter((e=>Object.entries(e).some((e=>{let[t,r]=e;return null!==r&&void 0!==r&&("object"!==typeof r&&String(r).toLowerCase().includes(I.toLowerCase()))})))):R),[R,I]),z=Math.ceil(T.length/h),V=(0,s.useMemo)((()=>{const e=(L-1)*h;return T.slice(e,e+h)}),[T,L,h]),Z=e=>{D(e)},B=e=>{let t="bg-gray-100 text-gray-800";if("string"===typeof e){const r=e.toLowerCase();r.includes("active")||r.includes("approved")||r.includes("verified")||r.includes("completed")||r.includes("success")?t="bg-green-100 text-green-800":r.includes("pending")||r.includes("processing")?t="bg-yellow-100 text-yellow-800":r.includes("rejected")||r.includes("banned")||r.includes("failed")||r.includes("error")?t="bg-red-100 text-red-800":r.includes("inactive")&&(t="bg-gray-100 text-gray-800")}return(0,u.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${t}`,children:e})};return(0,u.jsxs)("div",{className:`bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-md ${b}`,"data-testid":S,children:[(i||m)&&(0,u.jsxs)("div",{className:`px-6 py-4 border-b border-gray-100 ${j}`,children:["string"===typeof i?(0,u.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:i}):i,"string"===typeof m?(0,u.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:m}):m]}),(0,u.jsxs)("div",{className:"p-4 border-b border-gray-100 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,u.jsxs)("div",{className:"relative flex-1",children:[(0,u.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,u.jsx)(n,{className:"h-5 w-5 text-gray-400"})}),(0,u.jsx)("input",{type:"text",placeholder:"Search...",className:"block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200",value:I,onChange:e=>{P(e.target.value),D(1)},"data-testid":`${S}-search`})]}),(0,u.jsxs)("div",{className:"flex items-center space-x-2",children:[O.length>0&&(0,u.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,u.jsxs)("span",{className:"text-sm text-gray-500",children:[O.length," selected"]}),(0,u.jsx)("button",{className:"px-3 py-1.5 bg-red-50 text-red-600 rounded-md text-sm font-medium hover:bg-red-100 transition-colors",onClick:()=>{M([]),v&&v([])},"data-testid":`${S}-clear-selection`,children:"Clear"})]}),y]})]}),(0,u.jsx)("div",{className:`overflow-x-auto ${w}`,children:p?(0,u.jsx)("div",{className:"flex justify-center items-center py-20",children:(0,u.jsx)(d.A,{size:"lg",variant:"spinner"})}):(0,u.jsxs)("table",{className:"min-w-full divide-y divide-gray-100",children:[(0,u.jsx)("thead",{className:"bg-gray-50",children:(0,u.jsxs)("tr",{children:[g&&(0,u.jsx)("th",{className:"w-12 px-6 py-3",children:(0,u.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary",onChange:e=>{const t=e.target.checked?Array.from({length:V.length},((e,t)=>t)):[];if(M(t),v){const e=t.map((e=>V[e])).filter((e=>void 0!==e));v(e)}},checked:O.length===V.length&&V.length>0,"data-testid":`${S}-select-all`})}),t.map((e=>(0,u.jsx)("th",{className:`px-6 py-3 text-${e.align||"left"} text-xs font-medium text-gray-500 uppercase tracking-wider ${e.sortable?"cursor-pointer hover:bg-gray-100":""} transition-colors duration-200 ${e.width?e.width:""} ${e.className||""}`,onClick:()=>e.sortable&&(e=>{let t="asc";E&&E.key===e&&"asc"===E.direction&&(t="desc"),$({key:e,direction:t})})(e.key),"data-testid":`${S}-column-${e.key}`,children:(0,u.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,u.jsx)("span",{children:e.label}),e.sortable&&(0,u.jsx)("span",{className:"transition-colors duration-200 "+((null===E||void 0===E?void 0:E.key)===e.key?"text-primary":"text-gray-400"),children:(null===E||void 0===E?void 0:E.key)===e.key&&"asc"===E.direction?(0,u.jsx)(l,{className:"h-4 w-4"}):(null===E||void 0===E?void 0:E.key)===e.key&&"desc"===E.direction?(0,u.jsx)(o.A,{className:"h-4 w-4"}):(0,u.jsx)("span",{className:"text-gray-300",children:"\u2195"})})]})},e.key)))]})}),(0,u.jsx)("tbody",{className:"bg-white divide-y divide-gray-100",children:V.length>0?V.map(((e,r)=>(0,u.jsxs)("tr",{className:`group transition-all duration-200 ${a?"cursor-pointer":""} ${O.includes(r)?"bg-primary bg-opacity-5":""}\n                    ${q===r?"bg-gray-50":""}\n                    ${k?k(e,r):""}`,onClick:()=>a&&a(e),onMouseEnter:()=>U(r),onMouseLeave:()=>U(null),"data-testid":`${S}-row-${r}`,children:[g&&(0,u.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,u.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary",checked:O.includes(r),onChange:()=>{},onClick:e=>((e,t)=>{t.stopPropagation();const r=[...O];if(O.includes(e)){const t=r.indexOf(e);r.splice(t,1)}else r.push(e);if(M(r),v){const e=r.map((e=>V[e])).filter((e=>void 0!==e));v(e)}})(r,e),"data-testid":`${S}-row-${r}-checkbox`})}),t.map((t=>(0,u.jsx)("td",{className:`px-6 py-4 whitespace-nowrap text-sm text-gray-600 group-hover:text-gray-900 text-${t.align||"left"} ${t.className||""}`,"data-testid":`${S}-row-${r}-cell-${t.key}`,children:t.render?t.render(e[t.key],e):t.key.toLowerCase().includes("status")?B(e[t.key]):e[t.key]},t.key)))]},r))):(0,u.jsx)("tr",{children:(0,u.jsx)("td",{colSpan:t.length+(g?1:0),className:"px-6 py-10 text-center text-gray-500","data-testid":`${S}-empty-message`,children:f})})})]})}),x&&z>1&&(0,u.jsxs)("div",{className:`px-6 py-4 border-t border-gray-100 flex items-center justify-between ${N}`,children:[(0,u.jsxs)("div",{className:"text-sm text-gray-500",children:["Showing ",(L-1)*h+1," to ",Math.min(L*h,T.length)," of ",T.length," entries"]}),(0,u.jsxs)("div",{className:"flex space-x-1",children:[(0,u.jsx)("button",{onClick:()=>Z(Math.max(1,L-1)),disabled:1===L,className:"px-3 py-1 rounded-md text-sm "+(1===L?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"),"data-testid":`${S}-pagination-prev`,children:"Previous"}),Array.from({length:Math.min(5,z)},((e,t)=>{let r;return r=z<=5||L<=3?t+1:L>=z-2?z-4+t:L-2+t,(0,u.jsx)("button",{onClick:()=>Z(r),className:"px-3 py-1 rounded-md text-sm "+(L===r?"bg-primary text-white":"text-gray-700 hover:bg-gray-100"),"data-testid":`${S}-pagination-${r}`,children:r},r)})),(0,u.jsx)("button",{onClick:()=>Z(Math.min(z,L+1)),disabled:L===z,className:"px-3 py-1 rounded-md text-sm "+(L===z?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"),"data-testid":`${S}-pagination-next`,children:"Next"})]})]})]})}const p=(0,s.memo)(m)},8100:(e,t,r)=>{r.d(t,{A:()=>o});var s=r(5043),a=r(7591),n=r(7950),i=r(579);const l=e=>{let{isOpen:t,onClose:r,title:l,children:o,size:d="md",footer:c,closeOnEsc:u=!0,closeOnBackdropClick:m=!0,showCloseButton:p=!0,centered:x=!0,className:h="",bodyClassName:g="",headerClassName:v="",footerClassName:y="",backdropClassName:f="",testId:b}=e;const j=(0,s.useRef)(null);if((0,s.useEffect)((()=>{const e=e=>{u&&"Escape"===e.key&&r()};return t&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="auto"}}),[t,r,u]),(0,s.useEffect)((()=>{if(!t||!j.current)return;const e=j.current.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');if(0===e.length)return;const r=e[0],s=e[e.length-1],a=e=>{"Tab"===e.key&&(e.shiftKey?document.activeElement===r&&(s.focus(),e.preventDefault()):document.activeElement===s&&(r.focus(),e.preventDefault()))};return document.addEventListener("keydown",a),r.focus(),()=>{document.removeEventListener("keydown",a)}}),[t]),!t)return null;const w=(0,i.jsxs)(s.Fragment,{children:[(0,i.jsx)("div",{className:`fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity ${f}`,onClick:m?r:void 0,"data-testid":`${b}-backdrop`}),(0,i.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,i.jsx)("div",{className:`flex min-h-full items-${x?"center":"start"} justify-center p-4 text-center`,children:(0,i.jsxs)("div",{ref:j,className:`${{xs:"max-w-xs",sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[d]} w-full transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all ${h}`,onClick:e=>e.stopPropagation(),"data-testid":b,children:[(0,i.jsxs)("div",{className:`flex items-center justify-between px-6 py-4 border-b border-gray-100 ${v}`,children:["string"===typeof l?(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:l}):l,p&&(0,i.jsx)("button",{type:"button",className:"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary rounded-full p-1",onClick:r,"aria-label":"Close modal","data-testid":`${b}-close-button`,children:(0,i.jsx)(a.A,{className:"h-6 w-6"})})]}),(0,i.jsx)("div",{className:`px-6 py-4 ${g}`,children:o}),c&&(0,i.jsx)("div",{className:`px-6 py-4 bg-gray-50 border-t border-gray-100 flex justify-end space-x-3 ${y}`,children:c})]})})})]});return(0,n.createPortal)(w,document.body)},o=(0,s.memo)(l)},8682:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(5043);function a(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))}const n=s.forwardRef(a)},9248:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(5043);function a(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))}const n=s.forwardRef(a)},9531:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(5043);function a(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const n=s.forwardRef(a)},9850:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(5043);function a(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))}const n=s.forwardRef(a)},9913:(e,t,r)=>{r.r(t),r.d(t,{default:()=>I});var s=r(5043),a=r(7907),n=r(3593),i=r(8100),l=r(3927);function o(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z"}))}const d=s.forwardRef(o);var c=r(3216),u=r(7422),m=r(8300),p=r(8682),x=r(9850),h=r(7012),g=r(4538),v=r(7098),y=r(9531),f=r(9248),b=r(724),j=r(579);const w=e=>{let{suppliers:t,onViewSupplier:r,onDeleteSupplier:s,onSupplierClick:a,title:n="Suppliers"}=e;const i=(0,c.Zp)(),l=[{key:"name",label:"Supplier Name",sortable:!0,render:(e,t)=>(0,j.jsxs)("div",{className:"flex items-center",children:[(0,j.jsx)("div",{className:"mr-3",children:(0,j.jsx)(m.A,{...t.logo&&{src:t.logo},alt:t.name,name:t.name,size:"sm"})}),(0,j.jsxs)("div",{children:[(0,j.jsx)("div",{className:"font-medium text-gray-900",children:t.name}),(0,j.jsxs)("div",{className:"text-xs text-gray-500",children:["ID: ",t.id]})]})]})},{key:"email",label:"Email",sortable:!0,render:e=>(0,j.jsxs)("div",{className:"flex items-center",children:[(0,j.jsx)(p.A,{className:"w-4 h-4 text-gray-400 mr-2"}),(0,j.jsx)("span",{children:e})]})},{key:"phone",label:"Phone",sortable:!0,render:e=>(0,j.jsxs)("div",{className:"flex items-center",children:[(0,j.jsx)(x.A,{className:"w-4 h-4 text-gray-400 mr-2"}),(0,j.jsx)("span",{children:e})]})},{key:"verificationStatus",label:"Status",sortable:!0,render:e=>{if(!e)return(0,j.jsxs)("div",{className:"flex items-center",children:[(0,j.jsx)(h.A,{className:"w-4 h-4 text-gray-400 mr-1"}),(0,j.jsx)("span",{children:"Unknown"})]});let t;switch(e){case"verified":t=(0,j.jsx)(g.A,{className:"w-4 h-4 text-green-500 mr-1"});break;case"pending":t=(0,j.jsx)(h.A,{className:"w-4 h-4 text-yellow-500 mr-1"});break;case"rejected":t=(0,j.jsx)(v.A,{className:"w-4 h-4 text-red-500 mr-1"});break;default:t=(0,j.jsx)(h.A,{className:"w-4 h-4 text-gray-400 mr-1"})}return(0,j.jsxs)("div",{className:"flex items-center",children:[t,(0,j.jsx)("span",{children:e?e.charAt(0).toUpperCase()+e.slice(1):"Unknown"})]})}},{key:"actions",label:"Actions",render:(e,t)=>(0,j.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,j.jsx)("button",{className:"p-1 text-gray-500 hover:text-primary rounded-full hover:bg-gray-100",onClick:e=>{e.stopPropagation(),r(t)},title:"View supplier details",children:(0,j.jsx)(y.A,{className:"w-5 h-5"})}),(0,j.jsx)("button",{className:"p-1 text-gray-500 hover:text-red-600 rounded-full hover:bg-gray-100",onClick:e=>{e.stopPropagation(),s(t)},title:"Delete supplier",children:(0,j.jsx)(f.A,{className:"w-5 h-5"})})]})}];return(0,j.jsx)(u.A,{columns:l,data:t,onRowClick:e=>{i(b.b.getSupplierProfileRoute(e.id))},title:n,pagination:!0})},N=e=>{let{supplier:t}=e;return(0,j.jsxs)("div",{className:"space-y-6",children:[(0,j.jsxs)("div",{className:"flex items-center justify-between",children:[(0,j.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,j.jsx)(m.A,{...t.logo&&{src:t.logo},alt:t.name,name:t.name,size:"xl"}),(0,j.jsxs)("div",{children:[(0,j.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:t.name}),(0,j.jsxs)("p",{className:"text-sm text-gray-500",children:["ID: ",t.id]}),(0,j.jsx)("div",{className:"mt-1",children:(0,j.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium "+("verified"===t.verificationStatus?"bg-green-100 text-green-800":"pending"===t.verificationStatus?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:t.verificationStatus?t.verificationStatus.charAt(0).toUpperCase()+t.verificationStatus.slice(1):"Unknown"})})]})]}),t.website&&(0,j.jsx)("a",{href:t.website,target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline text-sm",children:"Visit Website"})]}),(0,j.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 border-t border-gray-200 pt-4",children:[(0,j.jsxs)("div",{children:[(0,j.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-3",children:"Contact Information"}),(0,j.jsxs)("dl",{className:"space-y-3",children:[(0,j.jsxs)("div",{children:[(0,j.jsx)("dt",{className:"text-xs text-gray-500",children:"Contact Person"}),(0,j.jsx)("dd",{className:"text-sm text-gray-900",children:t.contactPerson})]}),(0,j.jsxs)("div",{children:[(0,j.jsx)("dt",{className:"text-xs text-gray-500",children:"Email"}),(0,j.jsx)("dd",{className:"text-sm text-gray-900",children:t.email})]}),(0,j.jsxs)("div",{children:[(0,j.jsx)("dt",{className:"text-xs text-gray-500",children:"Phone"}),(0,j.jsx)("dd",{className:"text-sm text-gray-900",children:t.phone})]})]})]}),(0,j.jsxs)("div",{children:[(0,j.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-3",children:"Address"}),(0,j.jsx)("address",{className:"not-italic text-sm text-gray-900",children:t.address})]})]}),(0,j.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,j.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Categories"}),(0,j.jsx)("div",{className:"flex flex-wrap gap-2",children:t.categories&&t.categories.length>0?t.categories.map(((e,t)=>(0,j.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e},t))):(0,j.jsx)("span",{className:"text-gray-500 text-sm",children:"No categories assigned"})})]}),(0,j.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,j.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Verification Status"}),(0,j.jsxs)("div",{className:"flex items-center space-x-2",children:["verified"===t.verificationStatus?(0,j.jsx)(g.A,{className:"w-5 h-5 text-green-500"}):"pending"===t.verificationStatus?(0,j.jsx)(h.A,{className:"w-5 h-5 text-yellow-500"}):(0,j.jsx)(v.A,{className:"w-5 h-5 text-red-500"}),(0,j.jsx)("span",{className:"text-sm text-gray-700",children:"verified"===t.verificationStatus?"Verified":"pending"===t.verificationStatus?"Pending verification":"Rejected"})]})]})]})};var k=r(5901),C=r(7147),A=r(6773);const S=e=>{let{onSubmit:t,onCancel:r,isLoading:n=!1}=e;const[i,l]=(0,s.useState)({supplierName:"",email:"",phone:"",address:"",businessType:"",password:"",confirmPassword:"",contactPerson:"",image:null}),[o,d]=(0,s.useState)({}),[c,u]=(0,s.useState)([]),[m,p]=(0,s.useState)(!0);(0,s.useEffect)((()=>{(async()=>{try{p(!0);const e=[{id:"1",name:"Retail",description:"Retail business",productCount:0,subcategoryCount:0,status:"active",createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString(),visibleInSupplierApp:!0,visibleInCustomerApp:!0},{id:"2",name:"Wholesale",description:"Wholesale business",productCount:0,subcategoryCount:0,status:"active",createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString(),visibleInSupplierApp:!0,visibleInCustomerApp:!0},{id:"3",name:"Manufacturing",description:"Manufacturing business",productCount:0,subcategoryCount:0,status:"active",createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString(),visibleInSupplierApp:!0,visibleInCustomerApp:!0},{id:"4",name:"Technology",description:"Technology business",productCount:0,subcategoryCount:0,status:"active",createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString(),visibleInSupplierApp:!0,visibleInCustomerApp:!0},{id:"5",name:"Healthcare",description:"Healthcare business",productCount:0,subcategoryCount:0,status:"active",createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString(),visibleInSupplierApp:!0,visibleInCustomerApp:!0},{id:"6",name:"Food & Beverage",description:"Food & Beverage business",productCount:0,subcategoryCount:0,status:"active",createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString(),visibleInSupplierApp:!0,visibleInCustomerApp:!0},{id:"7",name:"Automotive",description:"Automotive business",productCount:0,subcategoryCount:0,status:"active",createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString(),visibleInSupplierApp:!0,visibleInCustomerApp:!0}];u(e)}catch(e){console.error("Error fetching categories:",e)}finally{p(!1)}})()}),[]);const x=e=>{const{name:t,value:r}=e.target;l((e=>({...e,[t]:r}))),o[t]&&d((e=>({...e,[t]:""})))},h=(e,t)=>{d((r=>({...r,[e]:t})))};return(0,j.jsxs)("form",{onSubmit:e=>{e.preventDefault(),(()=>{const e={supplierName:[A.tU.required("Supplier name is required")],email:[A.tU.required("Email is required"),A.tU.email()],phone:[A.tU.required("Phone number is required"),A.tU.phone()],address:[A.tU.required("Address is required")],businessType:[A.tU.required("Business type is required")],password:[A.tU.required("Password is required"),A.tU.password()],confirmPassword:[A.tU.required("Confirm password is required"),A.tU.passwordMatch()]},t=(0,A.l)(i,e);return d(t),0===Object.keys(t).length})()&&t(i,h)},className:"space-y-6",children:[(0,j.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[(0,j.jsx)(k.A,{label:"Supplier Name",name:"supplierName",value:i.supplierName,onChange:x,error:o.supplierName,required:!0}),(0,j.jsx)(k.A,{label:"Business Type",name:"businessType",type:"select",value:i.businessType,onChange:x,error:o.businessType,required:!0,loading:m,options:[{value:"",label:"Select a business type"},...c.map((e=>({value:e.name,label:e.name})))]}),(0,j.jsx)(k.A,{label:"Email Address",name:"email",type:"email",value:i.email,onChange:x,error:o.email,required:!0}),(0,j.jsx)(k.A,{label:"Phone Number",name:"phone",type:"tel",value:i.phone,onChange:x,error:o.phone,required:!0}),(0,j.jsx)(k.A,{label:"Password",name:"password",type:"password",value:i.password,onChange:x,error:o.password,required:!0}),(0,j.jsx)(k.A,{label:"Confirm Password",name:"confirmPassword",type:"password",value:i.confirmPassword,onChange:x,error:o.confirmPassword,required:!0}),(0,j.jsx)(k.A,{label:"Address",name:"address",value:i.address,onChange:x,error:o.address,required:!0,className:"sm:col-span-2"})]}),(0,j.jsx)(C.A,{label:"Supplier Image",name:"image",value:i.image||null,onChange:e=>{l((t=>({...t,image:e}))),o.image&&d((e=>({...e,image:""})))},error:o.image,maxSize:5242880,allowedTypes:["image/jpeg","image/png","image/gif","image/webp"]}),(0,j.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,j.jsx)(a.A,{type:"button",variant:"outline",onClick:r,disabled:n,children:"Cancel"}),(0,j.jsx)(a.A,{type:"submit",loading:n,children:"Add Supplier"})]})]})};var E=r(8736),$=r(1512);const I=()=>{const[e,t]=(0,s.useState)("all"),[r,o]=(0,s.useState)(!1),[c,u]=(0,s.useState)(!1),[m,p]=(0,s.useState)(null),[x,h]=(0,s.useState)(!1),[g,v]=(0,s.useState)(!1),[y,f]=(0,s.useState)(null),[b,k]=(0,s.useState)(!1),{suppliers:C,isLoading:A,createEntity:I,deleteEntity:P,updateVerificationStatus:L}=(0,E.h)(),{withFormErrorHandling:D,clearError:O}=(0,$.A)({enableNotifications:!0,enableReporting:!0}),M=(0,s.useMemo)((()=>"all"===e?C:C.filter((t=>t.verificationStatus===e))),[C,e]),q=e=>{p(e),h(!0)},U=(0,s.useCallback)((e=>{f(e),v(!0)}),[]),R=(0,s.useCallback)((async()=>{if(!y)return;k(!0);const e=await D((async()=>(await P(y.id),!0)),void 0,`Delete supplier ${y.name}`);k(!1),e?(v(!1),f(null)):console.error("Failed to delete supplier")}),[y,D,P]),T=(0,s.useCallback)((async(e,t)=>{o(!0),O();const r=await D((async()=>{const t=await I(e,!1);return u(!1),t}),t,"Add Supplier");o(!1),r&&console.log("Supplier added successfully:",r)}),[D,I,O]),z=async(e,t)=>{try{await L(e,t),h(!1)}catch(r){console.error("Error updating verification status:",r)}};return(0,j.jsxs)("div",{className:"space-y-6",children:[(0,j.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,j.jsxs)("div",{children:[(0,j.jsx)("h1",{className:"text-2xl font-bold text-gray-800",children:"Suppliers"}),(0,j.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Manage your suppliers and verify applications"})]}),(0,j.jsx)(a.A,{icon:(0,j.jsx)(d,{className:"h-5 w-5"}),onClick:()=>u(!0),children:"Add Supplier"})]}),(0,j.jsxs)(n.A,{children:[(0,j.jsxs)("div",{className:"flex flex-wrap gap-3 mb-6",children:[(0,j.jsx)(a.A,{variant:"all"===e?"primary":"outline",size:"sm",onClick:()=>t("all"),children:"All Suppliers"}),(0,j.jsx)(a.A,{variant:"pending"===e?"primary":"outline",size:"sm",onClick:()=>t("pending"),children:"Pending Verification"}),(0,j.jsx)(a.A,{variant:"verified"===e?"primary":"outline",size:"sm",onClick:()=>t("verified"),children:"Verified"}),(0,j.jsx)(a.A,{variant:"rejected"===e?"primary":"outline",size:"sm",onClick:()=>t("rejected"),children:"Rejected"})]}),A?(0,j.jsx)("div",{className:"flex justify-center py-8",children:(0,j.jsx)(l.A,{})}):(0,j.jsx)(w,{suppliers:M,onSupplierClick:e=>{q(e)},onViewSupplier:q,onDeleteSupplier:U,title:`${e?e.charAt(0).toUpperCase()+e.slice(1):"All"} Suppliers (${M.length})`})]}),(0,j.jsx)(i.A,{isOpen:c,onClose:()=>u(!1),title:"Add New Supplier",size:"lg",children:(0,j.jsx)(S,{onSubmit:T,onCancel:()=>u(!1),isLoading:r})}),m&&(0,j.jsx)(i.A,{isOpen:x,onClose:()=>h(!1),title:"Supplier Details",size:"lg",footer:(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(a.A,{variant:"outline",onClick:()=>h(!1),children:"Close"}),"pending"===m.verificationStatus&&(0,j.jsx)(a.A,{variant:"success",onClick:()=>z(m.id,"verified"),children:"Verify"}),"verified"===m.verificationStatus&&(0,j.jsx)(a.A,{variant:"outline",onClick:()=>z(m.id,"pending"),children:"Set to Pending"})]}),children:(0,j.jsx)(N,{supplier:m})}),y&&(0,j.jsx)(i.A,{isOpen:g,onClose:()=>v(!1),title:"Delete Supplier",size:"sm",footer:(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(a.A,{variant:"outline",onClick:()=>v(!1),disabled:b,children:"Cancel"}),(0,j.jsx)(a.A,{variant:"danger",onClick:R,loading:b,children:"Delete Supplier"})]}),children:(0,j.jsxs)("div",{className:"text-sm text-gray-500",children:['Are you sure you want to delete "',y.name,'"? This action cannot be undone.']})})]})}}}]);
//# sourceMappingURL=814.5630d42e.chunk.js.map