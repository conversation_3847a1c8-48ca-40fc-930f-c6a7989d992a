"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[14],{312:(e,t,s)=>{s.d(t,{A:()=>a});s(5043);var r=s(579);const a=e=>{let{children:t,className:s=""}=e;return(0,r.jsx)("dl",{className:`sm:divide-y sm:divide-gray-200 ${s}`,children:t})}},1602:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(5043);function a(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"}))}const l=r.forwardRef(a)},3593:(e,t,s)=>{s.d(t,{A:()=>i});var r=s(5043),a=s(579);const l=e=>{let{title:t,subtitle:s,children:r,className:l="",bodyClassName:i="",headerClassName:n="",footerClassName:d="",icon:c,footer:o,onClick:m,hoverable:x=!1,noPadding:h=!1,bordered:u=!0,loading:p=!1,testId:g}=e;const y=`\n    bg-white rounded-xl ${u?"border border-gray-100":""} overflow-hidden transition-all duration-300\n    ${x?"hover:shadow-md hover:border-gray-200 transform hover:-translate-y-1":"shadow-sm"}\n    ${m?"cursor-pointer":""}\n    ${l}\n  `,f=`\n    px-6 py-4 border-b border-gray-100 flex items-center justify-between\n    ${n}\n  `,v=`\n    ${h?"":"p-6"}\n    ${i}\n  `,j=`\n    px-6 py-4 bg-gray-50 border-t border-gray-100\n    ${d}\n  `;return p?(0,a.jsxs)("div",{className:y,"data-testid":g,children:[(t||s||c)&&(0,a.jsxs)("div",{className:f,children:[(0,a.jsxs)("div",{className:"w-full",children:[t&&(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 animate-pulse"}),s&&(0,a.jsx)("div",{className:"h-4 mt-2 bg-gray-200 rounded w-1/2 animate-pulse"})]}),c&&(0,a.jsx)("div",{className:"h-8 w-8 bg-gray-200 rounded-full animate-pulse"})]}),(0,a.jsx)("div",{className:v,children:(0,a.jsx)("div",{className:"h-24 bg-gray-200 rounded animate-pulse"})}),o&&(0,a.jsx)("div",{className:j,children:(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 animate-pulse"})})]}):(0,a.jsxs)("div",{className:y,onClick:m,"data-testid":g,children:[(t||s||c)&&(0,a.jsxs)("div",{className:f,children:[(0,a.jsxs)("div",{children:["string"===typeof t?(0,a.jsx)("h3",{className:"text-lg font-semibold text-primary",children:t}):t,"string"===typeof s?(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:s}):s]}),c&&(0,a.jsx)("div",{className:"text-primary",children:c})]}),(0,a.jsx)("div",{className:v,children:r}),o&&(0,a.jsx)("div",{className:j,children:o})]})},i=(0,r.memo)(l)},3927:(e,t,s)=>{s.d(t,{A:()=>a});s(5043);var r=s(579);const a=e=>{let{size:t="md",className:s="",variant:a="spinner",color:l="#F28B22",useCurrentColor:i=!1}=e;const n={sm:{spinner:"w-5 h-5",dots:"w-1 h-1",pulse:"w-4 h-4",ripple:"w-6 h-6"},md:{spinner:"w-8 h-8",dots:"w-1.5 h-1.5",pulse:"w-6 h-6",ripple:"w-10 h-10"},lg:{spinner:"w-12 h-12",dots:"w-2 h-2",pulse:"w-8 h-8",ripple:"w-16 h-16"}},d=i?"currentColor":l;return"spinner"===a?(0,r.jsxs)("div",{className:`flex justify-center items-center ${s}`,role:"status","aria-label":"Loading",children:[(0,r.jsx)("div",{className:`spinner-smooth rounded-full border-2 border-gray-200 ${n[t].spinner}`,style:{borderTopColor:d,borderRightColor:d}}),(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"dots"===a?(0,r.jsxs)("div",{className:`flex justify-center items-center space-x-1 dots-bounce ${s}`,role:"status","aria-label":"Loading",children:[(0,r.jsx)("div",{className:`${n[t].dots} rounded-full dot`,style:{backgroundColor:d}}),(0,r.jsx)("div",{className:`${n[t].dots} rounded-full dot`,style:{backgroundColor:d}}),(0,r.jsx)("div",{className:`${n[t].dots} rounded-full dot`,style:{backgroundColor:d}}),(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"pulse"===a?(0,r.jsxs)("div",{className:`flex justify-center items-center ${s}`,role:"status","aria-label":"Loading",children:[(0,r.jsx)("div",{className:`${n[t].pulse} rounded-full pulse-smooth`,style:{backgroundColor:d}}),(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"ripple"===a?(0,r.jsxs)("div",{className:`flex justify-center items-center ${s}`,role:"status","aria-label":"Loading",children:[(0,r.jsx)("div",{className:`${n[t].ripple} rounded-full ripple-effect`,style:{color:d},children:(0,r.jsx)("div",{className:`${n[t].pulse} rounded-full pulse-smooth mx-auto`,style:{backgroundColor:d}})}),(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})]}):null}},5149:(e,t,s)=>{s.d(t,{A:()=>a});s(5043);var r=s(579);const a=e=>{let{label:t,value:s,className:a=""}=e;return(0,r.jsxs)("div",{className:`py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 ${a}`,children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:t}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:s})]})}},5442:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(5043);function a(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))}const l=r.forwardRef(a)},5889:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(5043);function a(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"}))}const l=r.forwardRef(a)},6861:(e,t,s)=>{s.r(t),s.d(t,{default:()=>C});var r=s(5043),a=s(3216),l=s(2806),i=s(3927),n=s(3593),d=s(7907),c=s(8100),o=s(4692),m=s(6887),x=s(312),h=s(5149),u=s(245),p=s(3893),g=s(7012),y=s(4538),f=s(5889),v=s(7098),j=s(5442),b=s(9248),N=s(1602);function w(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}const k=r.forwardRef(w);var A=s(579);const C=()=>{const{id:e}=(0,a.g)(),t=(0,a.Zp)(),s=e||"",{getOrderById:w,deleteOrder:C}=(0,u.h)(),[$,E]=(0,r.useState)(null),[L,O]=(0,r.useState)(!0),[S,I]=(0,r.useState)(null),[M,B]=(0,r.useState)(!1),[D,P]=(0,r.useState)(!1);(0,r.useEffect)((()=>{if(!s)return O(!1),void I("No order ID provided");(async()=>{try{O(!0),I(null);const e=await w(s);E(e)}catch(S){console.error("Error fetching order data:",S);const t=S instanceof Error?S.message:"Failed to fetch order data";I(t)}finally{O(!1)}})()}),[s,w]);const W=()=>{B(!1)};return L?(0,A.jsx)("div",{className:"flex justify-center items-center min-h-screen",children:(0,A.jsx)(i.A,{size:"lg"})}):S&&!$?(0,A.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[400px] space-y-4",children:[(0,A.jsx)("div",{className:"text-red-600 text-lg font-medium",children:"Error Loading Order"}),(0,A.jsx)("div",{className:"text-gray-600",children:S}),(0,A.jsx)(d.A,{onClick:()=>t(-1),icon:(0,A.jsx)(j.A,{className:"w-4 h-4"}),variant:"outline",children:"Go Back"})]}):$?(0,A.jsxs)("div",{className:"space-y-6",children:[(0,A.jsx)(l.A,{title:`Order #${$.id}`,description:"Complete order information and details",breadcrumbs:[{label:"Orders",path:"/orders"},{label:`Order #${$.id}`}],actions:(0,A.jsxs)("div",{className:"flex space-x-3",children:[(0,A.jsx)(d.A,{onClick:()=>{B(!0)},icon:(0,A.jsx)(b.A,{className:"w-4 h-4"}),variant:"danger",disabled:D,children:"Delete Order"}),(0,A.jsx)(d.A,{onClick:()=>t(-1),icon:(0,A.jsx)(j.A,{className:"w-4 h-4"}),variant:"outline",children:"Go Back"})]})}),(0,A.jsx)(n.A,{className:"bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200",children:(0,A.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,A.jsxs)("div",{className:"flex items-center space-x-3",children:[(e=>{switch(e){case"pending":return(0,A.jsx)(g.A,{className:"w-5 h-5 text-yellow-500"});case"approved":return(0,A.jsx)(y.A,{className:"w-5 h-5 text-blue-500"});case"completed":return(0,A.jsx)(f.A,{className:"w-5 h-5 text-green-500"});case"rejected":return(0,A.jsx)(v.A,{className:"w-5 h-5 text-red-500"});default:return null}})($.status),(0,A.jsxs)("div",{children:[(0,A.jsxs)("h2",{className:"text-xl font-semibold text-gray-900",children:["Order #",$.id]}),(0,A.jsx)(o.A,{status:$.status,type:"order"})]})]}),(0,A.jsxs)("div",{className:"text-right",children:[(0,A.jsx)("div",{className:"text-sm text-gray-500",children:"Total Amount"}),(0,A.jsx)("div",{className:"text-2xl font-bold text-primary",children:(0,p.vv)($.totalAmount)})]})]})}),(0,A.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,A.jsx)(m.A,{title:"Customer Information",description:"Details about the customer who placed this order",children:(0,A.jsxs)(x.A,{children:[(0,A.jsx)(h.A,{label:"Customer Name",value:$.customerName}),(0,A.jsx)(h.A,{label:"Order Date",value:(0,p.Yq)($.orderDate)}),(0,A.jsx)(h.A,{label:"Delivery Date",value:(0,p.Yq)($.deliveryDate)})]})}),(0,A.jsx)(m.A,{title:"Supplier Information",description:"Details about the supplier fulfilling this order",children:(0,A.jsxs)(x.A,{children:[(0,A.jsx)(h.A,{label:"Supplier Name",value:$.supplierName}),(0,A.jsx)(h.A,{label:"Order Status",value:(0,A.jsx)(o.A,{status:$.status,type:"order"})})]})})]}),$.items&&$.items.length>0&&(0,A.jsxs)(m.A,{title:"Order Items",description:"Products and services included in this order",children:[(0,A.jsx)("div",{className:"overflow-x-auto",children:(0,A.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,A.jsx)("thead",{className:"bg-gray-50",children:(0,A.jsxs)("tr",{children:[(0,A.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,A.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"SKU"}),(0,A.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Quantity"}),(0,A.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Unit Price"}),(0,A.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total"}),(0,A.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Image"})]})}),(0,A.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:$.items.map((e=>(0,A.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,A.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,A.jsxs)("div",{children:[(0,A.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),e.description&&(0,A.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})}),(0,A.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.sku||"N/A"}),(0,A.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium",children:e.quantity}),(0,A.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,p.vv)(e.unitPrice)}),(0,A.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:(0,p.vv)(e.quantity*e.unitPrice)}),(0,A.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,A.jsx)("div",{className:"flex items-center justify-center w-12 h-12 bg-gray-100 rounded-lg",children:(0,A.jsx)(N.A,{className:"w-6 h-6 text-gray-400"})})})]},e.id)))})]})}),(0,A.jsx)("div",{className:"mt-6 bg-gray-50 rounded-lg p-6",children:(0,A.jsx)("div",{className:"flex justify-between items-center",children:(0,A.jsxs)("div",{className:"space-y-2",children:[(0,A.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,A.jsx)("span",{className:"text-gray-600",children:"Subtotal:"}),(0,A.jsx)("span",{className:"text-gray-900",children:(0,p.vv)($.totalAmount)})]}),(0,A.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,A.jsx)("span",{className:"text-gray-600",children:"Tax:"}),(0,A.jsx)("span",{className:"text-gray-900",children:"Included"})]}),(0,A.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,A.jsx)("span",{className:"text-gray-600",children:"Shipping:"}),(0,A.jsx)("span",{className:"text-gray-900",children:"Free"})]}),(0,A.jsxs)("div",{className:"border-t pt-2 flex justify-between text-base font-medium",children:[(0,A.jsx)("span",{className:"text-gray-900",children:"Total:"}),(0,A.jsx)("span",{className:"text-primary font-bold",children:(0,p.vv)($.totalAmount)})]})]})})})]}),(0,A.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[$.shippingAddress&&(0,A.jsx)(m.A,{title:"Shipping Address",description:"Where this order will be delivered",children:(0,A.jsxs)("div",{className:"px-4 py-5 text-sm text-gray-900 space-y-1",children:[(0,A.jsx)("div",{children:$.shippingAddress.street}),(0,A.jsxs)("div",{children:[$.shippingAddress.city,", ",$.shippingAddress.state," ",$.shippingAddress.postalCode]}),(0,A.jsx)("div",{children:$.shippingAddress.country})]})}),$.billingAddress&&(0,A.jsx)(m.A,{title:"Billing Address",description:"Billing information for this order",children:(0,A.jsxs)("div",{className:"px-4 py-5 text-sm text-gray-900 space-y-1",children:[(0,A.jsx)("div",{children:$.billingAddress.street}),(0,A.jsxs)("div",{children:[$.billingAddress.city,", ",$.billingAddress.state," ",$.billingAddress.postalCode]}),(0,A.jsx)("div",{children:$.billingAddress.country})]})})]}),$.notes&&(0,A.jsx)(m.A,{title:"Order Notes",description:"Additional information and special instructions",children:(0,A.jsx)("div",{className:"px-4 py-5",children:(0,A.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,A.jsx)("p",{className:"text-sm text-gray-900",children:$.notes})})})}),(0,A.jsx)(c.A,{isOpen:M,onClose:W,title:(0,A.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,A.jsx)("div",{className:"flex-shrink-0",children:(0,A.jsx)(k,{className:"h-6 w-6 text-red-600"})}),(0,A.jsx)("div",{children:(0,A.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Delete Order"})})]}),size:"sm",footer:(0,A.jsxs)("div",{className:"flex space-x-3",children:[(0,A.jsx)(d.A,{onClick:W,variant:"outline",disabled:D,children:"Cancel"}),(0,A.jsx)(d.A,{onClick:async()=>{if($){P(!0);try{await C($.id),B(!1),t("/orders",{replace:!0})}catch(S){console.error("Failed to delete order:",S)}finally{P(!1)}}},variant:"danger",loading:D,icon:(0,A.jsx)(b.A,{className:"w-4 h-4"}),children:D?"Deleting...":"Delete Order"})]}),children:(0,A.jsxs)("div",{className:"space-y-4",children:[(0,A.jsxs)("p",{className:"text-sm text-gray-600",children:["Are you sure you want to delete order ",(0,A.jsxs)("strong",{children:["#",null===$||void 0===$?void 0:$.id]}),"? This action cannot be undone."]}),(0,A.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,A.jsxs)("div",{className:"flex",children:[(0,A.jsx)("div",{className:"flex-shrink-0",children:(0,A.jsx)(k,{className:"h-5 w-5 text-red-400"})}),(0,A.jsxs)("div",{className:"ml-3",children:[(0,A.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Warning"}),(0,A.jsx)("div",{className:"mt-2 text-sm text-red-700",children:(0,A.jsxs)("ul",{className:"list-disc pl-5 space-y-1",children:[(0,A.jsx)("li",{children:"This will permanently delete the order and all associated data"}),(0,A.jsx)("li",{children:"Customer and supplier records will remain intact"}),(0,A.jsx)("li",{children:"This action cannot be reversed"})]})})]})]})})]})})]}):(0,A.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[400px] space-y-4",children:[(0,A.jsx)("div",{className:"text-gray-600 text-lg font-medium",children:"Order not found"}),(0,A.jsx)(d.A,{onClick:()=>t(-1),icon:(0,A.jsx)(j.A,{className:"w-4 h-4"}),variant:"outline",children:"Go Back"})]})}},6887:(e,t,s)=>{s.d(t,{A:()=>a});s(5043);var r=s(579);const a=e=>{let{title:t,description:s,children:a,className:l=""}=e;return(0,r.jsxs)("div",{className:`bg-white shadow overflow-hidden sm:rounded-lg ${l}`,children:[(0,r.jsxs)("div",{className:"px-4 py-5 sm:px-6",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:t}),s&&(0,r.jsx)("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:s})]}),(0,r.jsx)("div",{className:"border-t border-gray-200",children:a})]})}},7012:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(5043);function a(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const l=r.forwardRef(a)},7098:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(5043);function a(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const l=r.forwardRef(a)},7422:(e,t,s)=>{s.d(t,{A:()=>h});var r=s(5043);function a(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}const l=r.forwardRef(a);function i(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"}))}const n=r.forwardRef(i);var d=s(2517),c=s(3927),o=s(1308),m=s(579);function x(e){let{columns:t,data:s,onRowClick:a,title:i,description:x,loading:h=!1,pagination:u=!0,pageSize:p=o.PI.DEFAULT_PAGE_SIZE,selectable:g=!0,onSelectionChange:y,actions:f,emptyMessage:v="No results found",className:j="",headerClassName:b="",bodyClassName:N="",footerClassName:w="",rowClassName:k,initialSortKey:A,initialSortDirection:C="asc",testId:$}=e;const[E,L]=(0,r.useState)(A?{key:A,direction:C}:null),[O,S]=(0,r.useState)(""),[I,M]=(0,r.useState)(1),[B,D]=(0,r.useState)([]),[P,W]=(0,r.useState)(null),R=(0,r.useMemo)((()=>E?[...s].sort(((e,t)=>{const s=e[E.key],r=t[E.key];return null==s&&null==r?0:null==s?"asc"===E.direction?-1:1:null==r?"asc"===E.direction?1:-1:"string"===typeof s&&"string"===typeof r?"asc"===E.direction?s.localeCompare(r):r.localeCompare(s):s<r?"asc"===E.direction?-1:1:s>r?"asc"===E.direction?1:-1:0})):s),[s,E]),z=(0,r.useMemo)((()=>O?R.filter((e=>Object.entries(e).some((e=>{let[t,s]=e;return null!==s&&void 0!==s&&("object"!==typeof s&&String(s).toLowerCase().includes(O.toLowerCase()))})))):R),[R,O]),T=Math.ceil(z.length/p),Z=(0,r.useMemo)((()=>{const e=(I-1)*p;return z.slice(e,e+p)}),[z,I,p]),F=e=>{M(e)},H=e=>{let t="bg-gray-100 text-gray-800";if("string"===typeof e){const s=e.toLowerCase();s.includes("active")||s.includes("approved")||s.includes("verified")||s.includes("completed")||s.includes("success")?t="bg-green-100 text-green-800":s.includes("pending")||s.includes("processing")?t="bg-yellow-100 text-yellow-800":s.includes("rejected")||s.includes("banned")||s.includes("failed")||s.includes("error")?t="bg-red-100 text-red-800":s.includes("inactive")&&(t="bg-gray-100 text-gray-800")}return(0,m.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${t}`,children:e})};return(0,m.jsxs)("div",{className:`bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-md ${j}`,"data-testid":$,children:[(i||x)&&(0,m.jsxs)("div",{className:`px-6 py-4 border-b border-gray-100 ${b}`,children:["string"===typeof i?(0,m.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:i}):i,"string"===typeof x?(0,m.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:x}):x]}),(0,m.jsxs)("div",{className:"p-4 border-b border-gray-100 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,m.jsxs)("div",{className:"relative flex-1",children:[(0,m.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,m.jsx)(l,{className:"h-5 w-5 text-gray-400"})}),(0,m.jsx)("input",{type:"text",placeholder:"Search...",className:"block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200",value:O,onChange:e=>{S(e.target.value),M(1)},"data-testid":`${$}-search`})]}),(0,m.jsxs)("div",{className:"flex items-center space-x-2",children:[B.length>0&&(0,m.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,m.jsxs)("span",{className:"text-sm text-gray-500",children:[B.length," selected"]}),(0,m.jsx)("button",{className:"px-3 py-1.5 bg-red-50 text-red-600 rounded-md text-sm font-medium hover:bg-red-100 transition-colors",onClick:()=>{D([]),y&&y([])},"data-testid":`${$}-clear-selection`,children:"Clear"})]}),f]})]}),(0,m.jsx)("div",{className:`overflow-x-auto ${N}`,children:h?(0,m.jsx)("div",{className:"flex justify-center items-center py-20",children:(0,m.jsx)(c.A,{size:"lg",variant:"spinner"})}):(0,m.jsxs)("table",{className:"min-w-full divide-y divide-gray-100",children:[(0,m.jsx)("thead",{className:"bg-gray-50",children:(0,m.jsxs)("tr",{children:[g&&(0,m.jsx)("th",{className:"w-12 px-6 py-3",children:(0,m.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary",onChange:e=>{const t=e.target.checked?Array.from({length:Z.length},((e,t)=>t)):[];if(D(t),y){const e=t.map((e=>Z[e])).filter((e=>void 0!==e));y(e)}},checked:B.length===Z.length&&Z.length>0,"data-testid":`${$}-select-all`})}),t.map((e=>(0,m.jsx)("th",{className:`px-6 py-3 text-${e.align||"left"} text-xs font-medium text-gray-500 uppercase tracking-wider ${e.sortable?"cursor-pointer hover:bg-gray-100":""} transition-colors duration-200 ${e.width?e.width:""} ${e.className||""}`,onClick:()=>e.sortable&&(e=>{let t="asc";E&&E.key===e&&"asc"===E.direction&&(t="desc"),L({key:e,direction:t})})(e.key),"data-testid":`${$}-column-${e.key}`,children:(0,m.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,m.jsx)("span",{children:e.label}),e.sortable&&(0,m.jsx)("span",{className:"transition-colors duration-200 "+((null===E||void 0===E?void 0:E.key)===e.key?"text-primary":"text-gray-400"),children:(null===E||void 0===E?void 0:E.key)===e.key&&"asc"===E.direction?(0,m.jsx)(n,{className:"h-4 w-4"}):(null===E||void 0===E?void 0:E.key)===e.key&&"desc"===E.direction?(0,m.jsx)(d.A,{className:"h-4 w-4"}):(0,m.jsx)("span",{className:"text-gray-300",children:"\u2195"})})]})},e.key)))]})}),(0,m.jsx)("tbody",{className:"bg-white divide-y divide-gray-100",children:Z.length>0?Z.map(((e,s)=>(0,m.jsxs)("tr",{className:`group transition-all duration-200 ${a?"cursor-pointer":""} ${B.includes(s)?"bg-primary bg-opacity-5":""}\n                    ${P===s?"bg-gray-50":""}\n                    ${k?k(e,s):""}`,onClick:()=>a&&a(e),onMouseEnter:()=>W(s),onMouseLeave:()=>W(null),"data-testid":`${$}-row-${s}`,children:[g&&(0,m.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,m.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary",checked:B.includes(s),onChange:()=>{},onClick:e=>((e,t)=>{t.stopPropagation();const s=[...B];if(B.includes(e)){const t=s.indexOf(e);s.splice(t,1)}else s.push(e);if(D(s),y){const e=s.map((e=>Z[e])).filter((e=>void 0!==e));y(e)}})(s,e),"data-testid":`${$}-row-${s}-checkbox`})}),t.map((t=>(0,m.jsx)("td",{className:`px-6 py-4 whitespace-nowrap text-sm text-gray-600 group-hover:text-gray-900 text-${t.align||"left"} ${t.className||""}`,"data-testid":`${$}-row-${s}-cell-${t.key}`,children:t.render?t.render(e[t.key],e):t.key.toLowerCase().includes("status")?H(e[t.key]):e[t.key]},t.key)))]},s))):(0,m.jsx)("tr",{children:(0,m.jsx)("td",{colSpan:t.length+(g?1:0),className:"px-6 py-10 text-center text-gray-500","data-testid":`${$}-empty-message`,children:v})})})]})}),u&&T>1&&(0,m.jsxs)("div",{className:`px-6 py-4 border-t border-gray-100 flex items-center justify-between ${w}`,children:[(0,m.jsxs)("div",{className:"text-sm text-gray-500",children:["Showing ",(I-1)*p+1," to ",Math.min(I*p,z.length)," of ",z.length," entries"]}),(0,m.jsxs)("div",{className:"flex space-x-1",children:[(0,m.jsx)("button",{onClick:()=>F(Math.max(1,I-1)),disabled:1===I,className:"px-3 py-1 rounded-md text-sm "+(1===I?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"),"data-testid":`${$}-pagination-prev`,children:"Previous"}),Array.from({length:Math.min(5,T)},((e,t)=>{let s;return s=T<=5||I<=3?t+1:I>=T-2?T-4+t:I-2+t,(0,m.jsx)("button",{onClick:()=>F(s),className:"px-3 py-1 rounded-md text-sm "+(I===s?"bg-primary text-white":"text-gray-700 hover:bg-gray-100"),"data-testid":`${$}-pagination-${s}`,children:s},s)})),(0,m.jsx)("button",{onClick:()=>F(Math.min(T,I+1)),disabled:I===T,className:"px-3 py-1 rounded-md text-sm "+(I===T?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"),"data-testid":`${$}-pagination-next`,children:"Next"})]})]})]})}const h=(0,r.memo)(x)},7907:(e,t,s)=>{s.d(t,{A:()=>i});var r=s(5043),a=s(579);const l=e=>{let{children:t,variant:s="primary",size:r="md",className:l="",onClick:i,disabled:n=!1,type:d="button",icon:c,iconPosition:o="left",fullWidth:m=!1,loading:x=!1,rounded:h=!1,href:u,target:p,rel:g,title:y,ariaLabel:f,testId:v}=e;const j=`\n    inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2\n    ${{primary:"bg-primary text-white hover:bg-primary/90 focus-visible:ring-primary",secondary:"bg-gray-200 text-gray-800 hover:bg-gray-300 focus-visible:ring-gray-300",outline:"bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus-visible:ring-primary",danger:"bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500",success:"bg-green-600 text-white hover:bg-green-700 focus-visible:ring-green-500",text:"bg-transparent text-primary hover:bg-gray-100 focus-visible:ring-primary",link:"bg-transparent text-primary hover:underline focus-visible:ring-transparent p-0"}[s]}\n    ${{xs:"text-xs px-2 py-1",sm:"text-xs px-3 py-1.5",md:"text-sm px-4 py-2",lg:"text-base px-5 py-2.5",xl:"text-lg px-6 py-3"}[r]}\n    ${n?"opacity-60 cursor-not-allowed":"cursor-pointer"}\n    ${m?"w-full":""}\n    ${h?"rounded-full":"rounded-lg"}\n    ${l}\n  `,b=(0,a.jsxs)(a.Fragment,{children:[x&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-current",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","aria-hidden":"true",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),c&&"left"===o&&!x&&(0,a.jsx)("span",{className:"mr-2",children:c}),t,c&&"right"===o&&(0,a.jsx)("span",{className:"ml-2",children:c})]});return u?(0,a.jsx)("a",{href:u,className:j,target:p,rel:g||("_blank"===p?"noopener noreferrer":void 0),onClick:i,title:y,"aria-label":f,"data-testid":v,children:b}):(0,a.jsx)("button",{type:d,className:j,onClick:i,disabled:n||x,title:y,"aria-label":f,"data-testid":v,children:b})},i=(0,r.memo)(l)},8100:(e,t,s)=>{s.d(t,{A:()=>d});var r=s(5043),a=s(7591),l=s(7950),i=s(579);const n=e=>{let{isOpen:t,onClose:s,title:n,children:d,size:c="md",footer:o,closeOnEsc:m=!0,closeOnBackdropClick:x=!0,showCloseButton:h=!0,centered:u=!0,className:p="",bodyClassName:g="",headerClassName:y="",footerClassName:f="",backdropClassName:v="",testId:j}=e;const b=(0,r.useRef)(null);if((0,r.useEffect)((()=>{const e=e=>{m&&"Escape"===e.key&&s()};return t&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="auto"}}),[t,s,m]),(0,r.useEffect)((()=>{if(!t||!b.current)return;const e=b.current.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');if(0===e.length)return;const s=e[0],r=e[e.length-1],a=e=>{"Tab"===e.key&&(e.shiftKey?document.activeElement===s&&(r.focus(),e.preventDefault()):document.activeElement===r&&(s.focus(),e.preventDefault()))};return document.addEventListener("keydown",a),s.focus(),()=>{document.removeEventListener("keydown",a)}}),[t]),!t)return null;const N=(0,i.jsxs)(r.Fragment,{children:[(0,i.jsx)("div",{className:`fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity ${v}`,onClick:x?s:void 0,"data-testid":`${j}-backdrop`}),(0,i.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,i.jsx)("div",{className:`flex min-h-full items-${u?"center":"start"} justify-center p-4 text-center`,children:(0,i.jsxs)("div",{ref:b,className:`${{xs:"max-w-xs",sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[c]} w-full transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all ${p}`,onClick:e=>e.stopPropagation(),"data-testid":j,children:[(0,i.jsxs)("div",{className:`flex items-center justify-between px-6 py-4 border-b border-gray-100 ${y}`,children:["string"===typeof n?(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:n}):n,h&&(0,i.jsx)("button",{type:"button",className:"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary rounded-full p-1",onClick:s,"aria-label":"Close modal","data-testid":`${j}-close-button`,children:(0,i.jsx)(a.A,{className:"h-6 w-6"})})]}),(0,i.jsx)("div",{className:`px-6 py-4 ${g}`,children:d}),o&&(0,i.jsx)("div",{className:`px-6 py-4 bg-gray-50 border-t border-gray-100 flex justify-end space-x-3 ${f}`,children:o})]})})})]});return(0,l.createPortal)(N,document.body)},d=(0,r.memo)(n)},9248:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(5043);function a(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))}const l=r.forwardRef(a)}}]);
//# sourceMappingURL=14.8fe68a2e.chunk.js.map