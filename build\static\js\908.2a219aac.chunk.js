"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[908],{3593:(e,t,r)=>{r.d(t,{A:()=>l});var s=r(5043),a=r(579);const n=e=>{let{title:t,subtitle:r,children:s,className:n="",bodyClassName:l="",headerClassName:i="",footerClassName:o="",icon:d,footer:c,onClick:m,hoverable:u=!1,noPadding:x=!1,bordered:h=!0,loading:g=!1,testId:p}=e;const y=`\n    bg-white rounded-xl ${h?"border border-gray-100":""} overflow-hidden transition-all duration-300\n    ${u?"hover:shadow-md hover:border-gray-200 transform hover:-translate-y-1":"shadow-sm"}\n    ${m?"cursor-pointer":""}\n    ${n}\n  `,b=`\n    px-6 py-4 border-b border-gray-100 flex items-center justify-between\n    ${i}\n  `,f=`\n    ${x?"":"p-6"}\n    ${l}\n  `,v=`\n    px-6 py-4 bg-gray-50 border-t border-gray-100\n    ${o}\n  `;return g?(0,a.jsxs)("div",{className:y,"data-testid":p,children:[(t||r||d)&&(0,a.jsxs)("div",{className:b,children:[(0,a.jsxs)("div",{className:"w-full",children:[t&&(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 animate-pulse"}),r&&(0,a.jsx)("div",{className:"h-4 mt-2 bg-gray-200 rounded w-1/2 animate-pulse"})]}),d&&(0,a.jsx)("div",{className:"h-8 w-8 bg-gray-200 rounded-full animate-pulse"})]}),(0,a.jsx)("div",{className:f,children:(0,a.jsx)("div",{className:"h-24 bg-gray-200 rounded animate-pulse"})}),c&&(0,a.jsx)("div",{className:v,children:(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 animate-pulse"})})]}):(0,a.jsxs)("div",{className:y,onClick:m,"data-testid":p,children:[(t||r||d)&&(0,a.jsxs)("div",{className:b,children:[(0,a.jsxs)("div",{children:["string"===typeof t?(0,a.jsx)("h3",{className:"text-lg font-semibold text-primary",children:t}):t,"string"===typeof r?(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:r}):r]}),d&&(0,a.jsx)("div",{className:"text-primary",children:d})]}),(0,a.jsx)("div",{className:f,children:s}),c&&(0,a.jsx)("div",{className:v,children:c})]})},l=(0,s.memo)(n)},3927:(e,t,r)=>{r.d(t,{A:()=>a});r(5043);var s=r(579);const a=e=>{let{size:t="md",className:r="",variant:a="spinner",color:n="#F28B22",useCurrentColor:l=!1}=e;const i={sm:{spinner:"w-5 h-5",dots:"w-1 h-1",pulse:"w-4 h-4",ripple:"w-6 h-6"},md:{spinner:"w-8 h-8",dots:"w-1.5 h-1.5",pulse:"w-6 h-6",ripple:"w-10 h-10"},lg:{spinner:"w-12 h-12",dots:"w-2 h-2",pulse:"w-8 h-8",ripple:"w-16 h-16"}},o=l?"currentColor":n;return"spinner"===a?(0,s.jsxs)("div",{className:`flex justify-center items-center ${r}`,role:"status","aria-label":"Loading",children:[(0,s.jsx)("div",{className:`spinner-smooth rounded-full border-2 border-gray-200 ${i[t].spinner}`,style:{borderTopColor:o,borderRightColor:o}}),(0,s.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"dots"===a?(0,s.jsxs)("div",{className:`flex justify-center items-center space-x-1 dots-bounce ${r}`,role:"status","aria-label":"Loading",children:[(0,s.jsx)("div",{className:`${i[t].dots} rounded-full dot`,style:{backgroundColor:o}}),(0,s.jsx)("div",{className:`${i[t].dots} rounded-full dot`,style:{backgroundColor:o}}),(0,s.jsx)("div",{className:`${i[t].dots} rounded-full dot`,style:{backgroundColor:o}}),(0,s.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"pulse"===a?(0,s.jsxs)("div",{className:`flex justify-center items-center ${r}`,role:"status","aria-label":"Loading",children:[(0,s.jsx)("div",{className:`${i[t].pulse} rounded-full pulse-smooth`,style:{backgroundColor:o}}),(0,s.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"ripple"===a?(0,s.jsxs)("div",{className:`flex justify-center items-center ${r}`,role:"status","aria-label":"Loading",children:[(0,s.jsx)("div",{className:`${i[t].ripple} rounded-full ripple-effect`,style:{color:o},children:(0,s.jsx)("div",{className:`${i[t].pulse} rounded-full pulse-smooth mx-auto`,style:{backgroundColor:o}})}),(0,s.jsx)("span",{className:"sr-only",children:"Loading..."})]}):null}},4791:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(5043);function a(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"}))}const n=s.forwardRef(a)},5889:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(5043);function a(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"}))}const n=s.forwardRef(a)},7012:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(5043);function a(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const n=s.forwardRef(a)},7098:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(5043);function a(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const n=s.forwardRef(a)},7422:(e,t,r)=>{r.d(t,{A:()=>x});var s=r(5043);function a(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}const n=s.forwardRef(a);function l(e,t){let{title:r,titleId:a,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"}))}const i=s.forwardRef(l);var o=r(2517),d=r(3927),c=r(1308),m=r(579);function u(e){let{columns:t,data:r,onRowClick:a,title:l,description:u,loading:x=!1,pagination:h=!0,pageSize:g=c.PI.DEFAULT_PAGE_SIZE,selectable:p=!0,onSelectionChange:y,actions:b,emptyMessage:f="No results found",className:v="",headerClassName:j="",bodyClassName:w="",footerClassName:N="",rowClassName:k,initialSortKey:$,initialSortDirection:C="asc",testId:L}=e;const[A,E]=(0,s.useState)($?{key:$,direction:C}:null),[M,S]=(0,s.useState)(""),[O,I]=(0,s.useState)(1),[R,B]=(0,s.useState)([]),[W,z]=(0,s.useState)(null),F=(0,s.useMemo)((()=>A?[...r].sort(((e,t)=>{const r=e[A.key],s=t[A.key];return null==r&&null==s?0:null==r?"asc"===A.direction?-1:1:null==s?"asc"===A.direction?1:-1:"string"===typeof r&&"string"===typeof s?"asc"===A.direction?r.localeCompare(s):s.localeCompare(r):r<s?"asc"===A.direction?-1:1:r>s?"asc"===A.direction?1:-1:0})):r),[r,A]),P=(0,s.useMemo)((()=>M?F.filter((e=>Object.entries(e).some((e=>{let[t,r]=e;return null!==r&&void 0!==r&&("object"!==typeof r&&String(r).toLowerCase().includes(M.toLowerCase()))})))):F),[F,M]),Z=Math.ceil(P.length/g),_=(0,s.useMemo)((()=>{const e=(O-1)*g;return P.slice(e,e+g)}),[P,O,g]),V=e=>{I(e)},D=e=>{let t="bg-gray-100 text-gray-800";if("string"===typeof e){const r=e.toLowerCase();r.includes("active")||r.includes("approved")||r.includes("verified")||r.includes("completed")||r.includes("success")?t="bg-green-100 text-green-800":r.includes("pending")||r.includes("processing")?t="bg-yellow-100 text-yellow-800":r.includes("rejected")||r.includes("banned")||r.includes("failed")||r.includes("error")?t="bg-red-100 text-red-800":r.includes("inactive")&&(t="bg-gray-100 text-gray-800")}return(0,m.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${t}`,children:e})};return(0,m.jsxs)("div",{className:`bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-md ${v}`,"data-testid":L,children:[(l||u)&&(0,m.jsxs)("div",{className:`px-6 py-4 border-b border-gray-100 ${j}`,children:["string"===typeof l?(0,m.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:l}):l,"string"===typeof u?(0,m.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:u}):u]}),(0,m.jsxs)("div",{className:"p-4 border-b border-gray-100 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,m.jsxs)("div",{className:"relative flex-1",children:[(0,m.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,m.jsx)(n,{className:"h-5 w-5 text-gray-400"})}),(0,m.jsx)("input",{type:"text",placeholder:"Search...",className:"block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200",value:M,onChange:e=>{S(e.target.value),I(1)},"data-testid":`${L}-search`})]}),(0,m.jsxs)("div",{className:"flex items-center space-x-2",children:[R.length>0&&(0,m.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,m.jsxs)("span",{className:"text-sm text-gray-500",children:[R.length," selected"]}),(0,m.jsx)("button",{className:"px-3 py-1.5 bg-red-50 text-red-600 rounded-md text-sm font-medium hover:bg-red-100 transition-colors",onClick:()=>{B([]),y&&y([])},"data-testid":`${L}-clear-selection`,children:"Clear"})]}),b]})]}),(0,m.jsx)("div",{className:`overflow-x-auto ${w}`,children:x?(0,m.jsx)("div",{className:"flex justify-center items-center py-20",children:(0,m.jsx)(d.A,{size:"lg",variant:"spinner"})}):(0,m.jsxs)("table",{className:"min-w-full divide-y divide-gray-100",children:[(0,m.jsx)("thead",{className:"bg-gray-50",children:(0,m.jsxs)("tr",{children:[p&&(0,m.jsx)("th",{className:"w-12 px-6 py-3",children:(0,m.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary",onChange:e=>{const t=e.target.checked?Array.from({length:_.length},((e,t)=>t)):[];if(B(t),y){const e=t.map((e=>_[e])).filter((e=>void 0!==e));y(e)}},checked:R.length===_.length&&_.length>0,"data-testid":`${L}-select-all`})}),t.map((e=>(0,m.jsx)("th",{className:`px-6 py-3 text-${e.align||"left"} text-xs font-medium text-gray-500 uppercase tracking-wider ${e.sortable?"cursor-pointer hover:bg-gray-100":""} transition-colors duration-200 ${e.width?e.width:""} ${e.className||""}`,onClick:()=>e.sortable&&(e=>{let t="asc";A&&A.key===e&&"asc"===A.direction&&(t="desc"),E({key:e,direction:t})})(e.key),"data-testid":`${L}-column-${e.key}`,children:(0,m.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,m.jsx)("span",{children:e.label}),e.sortable&&(0,m.jsx)("span",{className:"transition-colors duration-200 "+((null===A||void 0===A?void 0:A.key)===e.key?"text-primary":"text-gray-400"),children:(null===A||void 0===A?void 0:A.key)===e.key&&"asc"===A.direction?(0,m.jsx)(i,{className:"h-4 w-4"}):(null===A||void 0===A?void 0:A.key)===e.key&&"desc"===A.direction?(0,m.jsx)(o.A,{className:"h-4 w-4"}):(0,m.jsx)("span",{className:"text-gray-300",children:"\u2195"})})]})},e.key)))]})}),(0,m.jsx)("tbody",{className:"bg-white divide-y divide-gray-100",children:_.length>0?_.map(((e,r)=>(0,m.jsxs)("tr",{className:`group transition-all duration-200 ${a?"cursor-pointer":""} ${R.includes(r)?"bg-primary bg-opacity-5":""}\n                    ${W===r?"bg-gray-50":""}\n                    ${k?k(e,r):""}`,onClick:()=>a&&a(e),onMouseEnter:()=>z(r),onMouseLeave:()=>z(null),"data-testid":`${L}-row-${r}`,children:[p&&(0,m.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,m.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary",checked:R.includes(r),onChange:()=>{},onClick:e=>((e,t)=>{t.stopPropagation();const r=[...R];if(R.includes(e)){const t=r.indexOf(e);r.splice(t,1)}else r.push(e);if(B(r),y){const e=r.map((e=>_[e])).filter((e=>void 0!==e));y(e)}})(r,e),"data-testid":`${L}-row-${r}-checkbox`})}),t.map((t=>(0,m.jsx)("td",{className:`px-6 py-4 whitespace-nowrap text-sm text-gray-600 group-hover:text-gray-900 text-${t.align||"left"} ${t.className||""}`,"data-testid":`${L}-row-${r}-cell-${t.key}`,children:t.render?t.render(e[t.key],e):t.key.toLowerCase().includes("status")?D(e[t.key]):e[t.key]},t.key)))]},r))):(0,m.jsx)("tr",{children:(0,m.jsx)("td",{colSpan:t.length+(p?1:0),className:"px-6 py-10 text-center text-gray-500","data-testid":`${L}-empty-message`,children:f})})})]})}),h&&Z>1&&(0,m.jsxs)("div",{className:`px-6 py-4 border-t border-gray-100 flex items-center justify-between ${N}`,children:[(0,m.jsxs)("div",{className:"text-sm text-gray-500",children:["Showing ",(O-1)*g+1," to ",Math.min(O*g,P.length)," of ",P.length," entries"]}),(0,m.jsxs)("div",{className:"flex space-x-1",children:[(0,m.jsx)("button",{onClick:()=>V(Math.max(1,O-1)),disabled:1===O,className:"px-3 py-1 rounded-md text-sm "+(1===O?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"),"data-testid":`${L}-pagination-prev`,children:"Previous"}),Array.from({length:Math.min(5,Z)},((e,t)=>{let r;return r=Z<=5||O<=3?t+1:O>=Z-2?Z-4+t:O-2+t,(0,m.jsx)("button",{onClick:()=>V(r),className:"px-3 py-1 rounded-md text-sm "+(O===r?"bg-primary text-white":"text-gray-700 hover:bg-gray-100"),"data-testid":`${L}-pagination-${r}`,children:r},r)})),(0,m.jsx)("button",{onClick:()=>V(Math.min(Z,O+1)),disabled:O===Z,className:"px-3 py-1 rounded-md text-sm "+(O===Z?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"),"data-testid":`${L}-pagination-next`,children:"Next"})]})]})]})}const x=(0,s.memo)(u)},7907:(e,t,r)=>{r.d(t,{A:()=>l});var s=r(5043),a=r(579);const n=e=>{let{children:t,variant:r="primary",size:s="md",className:n="",onClick:l,disabled:i=!1,type:o="button",icon:d,iconPosition:c="left",fullWidth:m=!1,loading:u=!1,rounded:x=!1,href:h,target:g,rel:p,title:y,ariaLabel:b,testId:f}=e;const v=`\n    inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2\n    ${{primary:"bg-primary text-white hover:bg-primary/90 focus-visible:ring-primary",secondary:"bg-gray-200 text-gray-800 hover:bg-gray-300 focus-visible:ring-gray-300",outline:"bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus-visible:ring-primary",danger:"bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500",success:"bg-green-600 text-white hover:bg-green-700 focus-visible:ring-green-500",text:"bg-transparent text-primary hover:bg-gray-100 focus-visible:ring-primary",link:"bg-transparent text-primary hover:underline focus-visible:ring-transparent p-0"}[r]}\n    ${{xs:"text-xs px-2 py-1",sm:"text-xs px-3 py-1.5",md:"text-sm px-4 py-2",lg:"text-base px-5 py-2.5",xl:"text-lg px-6 py-3"}[s]}\n    ${i?"opacity-60 cursor-not-allowed":"cursor-pointer"}\n    ${m?"w-full":""}\n    ${x?"rounded-full":"rounded-lg"}\n    ${n}\n  `,j=(0,a.jsxs)(a.Fragment,{children:[u&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-current",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","aria-hidden":"true",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),d&&"left"===c&&!u&&(0,a.jsx)("span",{className:"mr-2",children:d}),t,d&&"right"===c&&(0,a.jsx)("span",{className:"ml-2",children:d})]});return h?(0,a.jsx)("a",{href:h,className:v,target:g,rel:p||("_blank"===g?"noopener noreferrer":void 0),onClick:l,title:y,"aria-label":b,"data-testid":f,children:j}):(0,a.jsx)("button",{type:o,className:v,onClick:l,disabled:i||u,title:y,"aria-label":b,"data-testid":f,children:j})},l=(0,s.memo)(n)},8761:(e,t,r)=>{r.r(t),r.d(t,{default:()=>h});var s=r(5043),a=r(3216),n=r(3593),l=r(7907),i=r(2806),o=r(3927),d=r(4791),c=r(724),m=r(245),u=r(579);const x=()=>{const e=(0,a.Zp)(),{orders:t,isLoading:r}=(0,m.h)(),[x,h]=(0,s.useState)("all"),[g,p]=(0,s.useState)(!1),y=(0,s.useMemo)((()=>"all"===x?t:t.filter((e=>e.status===x))),[t,x]),b=(0,s.useCallback)((t=>{e(c.b.getOrderDetailsRoute(t.id))}),[e]),f=(0,s.useCallback)((()=>{p(!0),setTimeout((()=>{p(!1),console.log("Exporting orders...")}),1500)}),[]);return(0,u.jsxs)("div",{className:"space-y-6",children:[(0,u.jsx)(i.A,{title:"Orders",description:"Manage and track all orders in the system",actions:(0,u.jsx)(l.A,{variant:"outline",icon:(0,u.jsx)(d.A,{className:"h-5 w-5"}),onClick:f,loading:g,children:"Export Orders"})}),(0,u.jsxs)(n.A,{children:[(0,u.jsx)(m.Ji,{activeFilter:x,onFilterChange:h}),r?(0,u.jsx)("div",{className:"flex justify-center py-8",children:(0,u.jsx)(o.A,{})}):(0,u.jsx)(m.F9,{orders:y,onOrderClick:b,title:`${x.charAt(0).toUpperCase()+x.slice(1)} Orders (${y.length})`})]})]})},h=(0,s.memo)(x)}}]);
//# sourceMappingURL=908.2a219aac.chunk.js.map