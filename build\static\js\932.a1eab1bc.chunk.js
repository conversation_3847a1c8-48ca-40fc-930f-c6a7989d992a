"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[932],{312:(e,t,s)=>{s.d(t,{A:()=>a});s(5043);var r=s(579);const a=e=>{let{children:t,className:s=""}=e;return(0,r.jsx)("dl",{className:`sm:divide-y sm:divide-gray-200 ${s}`,children:t})}},2659:(e,t,s)=>{s.d(t,{A:()=>a});s(5043);var r=s(579);const a=e=>{let{tabs:t,activeTab:s,onChange:a,className:n=""}=e;return(0,r.jsx)("div",{className:`border-b border-gray-200 ${n}`,children:(0,r.jsx)("nav",{className:"-mb-px flex space-x-8",children:t.map((e=>{const t=e.id===s;return(0,r.jsx)("button",{onClick:()=>!e.disabled&&a(e.id),className:`\n                whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm\n                focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2\n                ${t?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}\n                ${e.disabled?"opacity-50 cursor-not-allowed":"cursor-pointer"}\n              `,disabled:e.disabled,children:e.label},e.id)}))})})}},2806:(e,t,s)=>{s.d(t,{A:()=>o});var r=s(5043),a=s(5475),n=s(5501),i=s(6365),l=s(579);const c=e=>{let{title:t,description:s,actions:r,breadcrumbs:c,className:o="",testId:d}=e;return(0,l.jsxs)("div",{className:`mb-6 ${o}`,"data-testid":d,children:[c&&c.length>0&&(0,l.jsx)("nav",{className:"flex mb-4","aria-label":"Breadcrumb",children:(0,l.jsxs)("ol",{className:"flex items-center space-x-1 text-sm text-gray-500",children:[(0,l.jsx)("li",{children:(0,l.jsx)(a.N_,{to:"/",className:"flex items-center hover:text-primary","aria-label":"Home",children:(0,l.jsx)(n.A,{className:"h-4 w-4"})})}),c.map(((e,t)=>(0,l.jsxs)("li",{className:"flex items-center",children:[(0,l.jsx)(i.A,{className:"h-4 w-4 mx-1 text-gray-400"}),e.path&&t<c.length-1?(0,l.jsx)(a.N_,{to:e.path,className:"hover:text-primary",children:e.label}):(0,l.jsx)("span",{className:"font-medium text-gray-700",children:e.label})]},t)))]})}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-800",children:t}),s&&"string"===typeof s?(0,l.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:s}):s]}),r&&(0,l.jsx)("div",{className:"flex flex-wrap gap-3 mt-2 sm:mt-0",children:r})]})]})},o=(0,r.memo)(c)},2811:(e,t,s)=>{s.d(t,{A:()=>n});var r=s(5043);function a(e,t){let{title:s,titleId:a,...n}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))}const n=r.forwardRef(a)},3211:(e,t,s)=>{s.r(t),s.d(t,{default:()=>F});var r=s(5043),a=s(3216),n=s(2806),i=s(2659),l=s(3927),c=s(7907),o=s(8100),d=s(9705),m=s(4538);function u(e,t){let{title:s,titleId:a,...n}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18.364 18.364A9 9 0 0 0 5.636 5.636m12.728 12.728A9 9 0 0 1 5.636 5.636m12.728 12.728L5.636 5.636"}))}const x=r.forwardRef(u);var p=s(9248),h=s(6887),g=s(312),f=s(5149),y=s(4692),v=s(8300),j=s(7012),b=s(7098),w=s(4129),N=s(8682),k=s(9850);function A(e,t){let{title:s,titleId:a,...n}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}))}const C=r.forwardRef(A);function S(e,t){let{title:s,titleId:a,...n}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"}))}const E=r.forwardRef(S);var $=s(579);const L=e=>{let{supplier:t}=e;return(0,$.jsxs)("div",{className:"space-y-6",children:[(0,$.jsx)(h.A,{title:"Supplier Overview",description:"Basic supplier information and verification status",children:(0,$.jsx)("div",{className:"px-6 py-4",children:(0,$.jsx)("div",{className:"flex items-center justify-between",children:(0,$.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,$.jsx)(v.A,{...t.logo&&{src:t.logo},alt:t.name,name:t.name,size:"xl"}),(0,$.jsxs)("div",{children:[(0,$.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:t.name}),(0,$.jsxs)("p",{className:"text-sm text-gray-500",children:["ID: ",t.id]}),(0,$.jsxs)("div",{className:"mt-2 flex items-center space-x-3",children:[(0,$.jsx)(y.A,{status:t.status,type:"supplier"}),(0,$.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium "+("verified"===t.verificationStatus?"bg-green-100 text-green-800":"pending"===t.verificationStatus?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:t.verificationStatus?t.verificationStatus.charAt(0).toUpperCase()+t.verificationStatus.slice(1):"Unknown"})]})]})]})})})}),(0,$.jsx)(h.A,{title:"Contact Information",description:"Primary contact details and communication preferences",children:(0,$.jsxs)(g.A,{children:[(0,$.jsx)(f.A,{label:"Contact Person",value:(0,$.jsxs)("div",{className:"flex items-center",children:[(0,$.jsx)(w.A,{className:"w-4 h-4 text-gray-400 mr-2"}),t.contactPerson]})}),(0,$.jsx)(f.A,{label:"Email Address",value:(0,$.jsxs)("div",{className:"flex items-center",children:[(0,$.jsx)(N.A,{className:"w-4 h-4 text-gray-400 mr-2"}),(0,$.jsx)("a",{href:`mailto:${t.email}`,className:"text-primary hover:text-primary-dark",children:t.email})]})}),(0,$.jsx)(f.A,{label:"Phone Number",value:(0,$.jsxs)("div",{className:"flex items-center",children:[(0,$.jsx)(k.A,{className:"w-4 h-4 text-gray-400 mr-2"}),(0,$.jsx)("a",{href:`tel:${t.phone}`,className:"text-primary hover:text-primary-dark",children:t.phone})]})}),(0,$.jsx)(f.A,{label:"Address",value:(0,$.jsxs)("div",{className:"flex items-start",children:[(0,$.jsx)(C,{className:"w-4 h-4 text-gray-400 mr-2 mt-0.5"}),(0,$.jsx)("span",{children:t.address})]})}),t.website&&(0,$.jsx)(f.A,{label:"Website",value:(0,$.jsxs)("div",{className:"flex items-center",children:[(0,$.jsx)(E,{className:"w-4 h-4 text-gray-400 mr-2"}),(0,$.jsx)("a",{href:t.website,target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:text-primary-dark",children:t.website})]})})]})}),(0,$.jsx)(h.A,{title:"Business Information",description:"Business categories and operational details",children:(0,$.jsxs)(g.A,{children:[(0,$.jsx)(f.A,{label:"Business Categories",value:(0,$.jsx)("div",{className:"flex flex-wrap gap-2",children:t.categories&&t.categories.length>0?t.categories.map(((e,t)=>(0,$.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e},t))):(0,$.jsx)("span",{className:"text-gray-500 text-sm",children:"No categories assigned"})})}),(0,$.jsx)(f.A,{label:"Account Status",value:(0,$.jsx)(y.A,{status:t.status,type:"supplier"})})]})}),(0,$.jsx)(h.A,{title:"Verification Status",description:"Current verification status and history",children:(0,$.jsx)("div",{className:"px-6 py-4",children:(0,$.jsxs)("div",{className:"flex items-center space-x-3",children:[(e=>{switch(e){case"verified":return(0,$.jsx)(m.A,{className:"w-5 h-5 text-green-500"});case"pending":return(0,$.jsx)(j.A,{className:"w-5 h-5 text-yellow-500"});case"rejected":return(0,$.jsx)(b.A,{className:"w-5 h-5 text-red-500"});default:return(0,$.jsx)(j.A,{className:"w-5 h-5 text-gray-500"})}})(t.verificationStatus||"pending"),(0,$.jsxs)("div",{children:[(0,$.jsx)("div",{className:"text-sm font-medium text-gray-900",children:"verified"===t.verificationStatus?"Verified Supplier":"pending"===t.verificationStatus?"Verification Pending":"Verification Rejected"}),(0,$.jsx)("div",{className:"text-sm text-gray-500",children:(e=>{switch(e){case"verified":return"Verified";case"pending":return"Pending verification";case"rejected":return"Rejected";default:return"Unknown status"}})(t.verificationStatus||"pending")})]})]})})})]})};var I=s(7422),P=s(724),M=s(8267),R=s(9531),B=s(2811),O=s(8662),D=s(3893);const U=e=>{let{products:t,supplierId:s,onProductUpdate:n}=e;const i=(0,a.Zp)(),{showSuccess:l,showError:m,showInfo:u}=(0,d.A)(),[x,g]=(0,r.useState)(null),[f,v]=(0,r.useState)(!1),j=e=>{switch(e){case"active":return"active";case"inactive":default:return"pending";case"out_of_stock":return"rejected"}},b=e=>{i(P.b.getProductDetailsRoute(e.id))},w=[{key:"name",label:"Product Name",sortable:!0,render:(e,t)=>(0,$.jsxs)("div",{className:"flex items-center",children:[t.image?(0,$.jsx)("img",{src:t.image,alt:t.name,className:"h-10 w-10 rounded-lg object-cover mr-3"}):(0,$.jsx)("div",{className:"h-10 w-10 bg-gray-200 rounded-lg flex items-center justify-center mr-3",children:(0,$.jsx)(M.A,{className:"h-5 w-5 text-gray-400"})}),(0,$.jsxs)("div",{children:[(0,$.jsx)("div",{className:"font-medium text-gray-900",children:t.name}),(0,$.jsxs)("div",{className:"text-xs text-gray-500",children:["ID: ",t.id]})]})]})},{key:"sku",label:"SKU",sortable:!0,render:e=>(0,$.jsx)("span",{className:"font-mono text-sm text-gray-600",children:e})},{key:"category",label:"Category",sortable:!0,render:e=>(0,$.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e})},{key:"price",label:"Price",sortable:!0,render:e=>(0,$.jsx)("span",{className:"font-medium text-gray-900",children:(0,D.vv)(e)})},{key:"stock",label:"Stock",sortable:!0,render:(e,t)=>{const s=(r=e,a=t.minimumStock||10,"out_of_stock"===t.status||0===r?{text:"Out of Stock",color:"text-red-600"}:r<=a?{text:"Low Stock",color:"text-yellow-600"}:{text:"In Stock",color:"text-green-600"});var r,a;return(0,$.jsxs)("div",{children:[(0,$.jsx)("div",{className:"font-medium text-gray-900",children:e}),(0,$.jsx)("div",{className:`text-xs ${s.color}`,children:s.text})]})}},{key:"status",label:"Status",sortable:!0,render:e=>(0,$.jsx)(y.A,{status:j(e),type:"supplier"})},{key:"actions",label:"Actions",sortable:!1,render:(e,t)=>(0,$.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,$.jsx)(c.A,{variant:"outline",size:"xs",onClick:()=>b(t),icon:(0,$.jsx)(R.A,{className:"w-4 h-4"}),title:"View product details",children:"View"}),(0,$.jsx)(c.A,{variant:"outline",size:"xs",onClick:()=>(e=>{u(`Edit product: ${e.name}`),console.log("Edit product:",e)})(t),icon:(0,$.jsx)(B.A,{className:"w-4 h-4"}),title:"Edit product",children:"Edit"}),(0,$.jsx)(c.A,{variant:"danger",size:"xs",onClick:()=>(e=>{g(e),v(!0)})(t),icon:(0,$.jsx)(p.A,{className:"w-4 h-4"}),title:"Delete product",children:"Delete"})]})}];return 0===t.length?(0,$.jsx)(h.A,{title:"Products",description:"Products offered by this supplier",children:(0,$.jsxs)("div",{className:"px-6 py-8 text-center",children:[(0,$.jsx)(M.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,$.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No products found"}),(0,$.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"This supplier has not added any products yet."}),(0,$.jsx)("div",{className:"mt-6",children:(0,$.jsx)(c.A,{variant:"primary",icon:(0,$.jsx)(O.A,{className:"w-4 h-4"}),onClick:()=>u("Add product functionality coming soon"),children:"Add Product"})})]})}):(0,$.jsxs)("div",{className:"space-y-6",children:[(0,$.jsx)(h.A,{title:"Products",description:`${t.length} products offered by this supplier`,children:(0,$.jsx)(I.A,{columns:w,data:t,onRowClick:b,pagination:!0,pageSize:10,emptyMessage:"No products found",className:"border-0"})}),x&&(0,$.jsx)(o.A,{isOpen:f,onClose:()=>v(!1),title:"Delete Product",size:"sm",footer:(0,$.jsxs)($.Fragment,{children:[(0,$.jsx)(c.A,{variant:"outline",onClick:()=>v(!1),children:"Cancel"}),(0,$.jsx)(c.A,{variant:"danger",onClick:async()=>{if(x)try{l(`Product "${x.name}" deleted successfully`),v(!1),g(null),null===n||void 0===n||n()}catch(e){m("Failed to delete product")}},children:"Delete Product"})]}),children:(0,$.jsxs)("div",{className:"text-sm text-gray-500",children:['Are you sure you want to delete "',x.name,'"? This action cannot be undone.']})})]})};var z=s(8736);const F=()=>{const{id:e}=(0,a.g)(),t=(0,a.Zp)(),{showError:s,showSuccess:u}=(0,d.A)(),h=(0,r.useRef)(s),g=(0,r.useRef)(u),f=(0,r.useRef)(!0);(0,r.useEffect)((()=>{h.current=s,g.current=u}),[s,u]),(0,r.useEffect)((()=>()=>{f.current=!1}),[]);const[y,v]=(0,r.useState)("personal"),[j,b]=(0,r.useState)(null),[w,N]=(0,r.useState)([]),[k,A]=(0,r.useState)(!0),[C,S]=(0,r.useState)(null),[E,I]=(0,r.useState)(!1),[M,R]=(0,r.useState)(!1),[B,O]=(0,r.useState)(!1),[D,F]=(0,r.useState)(!1),{getSupplierById:W,getSupplierProducts:Z,deleteSupplier:_,banSupplier:V,unbanSupplier:T,isLoading:H}=(0,z.h)(),K=(0,r.useCallback)((async()=>{if(!e)return S("No supplier ID provided"),void A(!1);try{A(!0),S(null);const t=await W(e);if(!f.current)return;b(t);const s=await Z(e);if(!f.current)return;N(s)}catch(C){if(!f.current)return;console.error("Error fetching supplier data:",C);const t=C instanceof Error?C.message:"Failed to fetch supplier data";S(t),h.current("Failed to load supplier data")}finally{f.current&&A(!1)}}),[e,W,Z]);(0,r.useEffect)((()=>{K()}),[K]);return k||H?(0,$.jsx)("div",{className:"flex justify-center items-center min-h-screen",children:(0,$.jsx)(l.A,{size:"lg"})}):C||!j?(0,$.jsxs)("div",{className:"space-y-6",children:[(0,$.jsx)(n.A,{title:"Supplier Profile",description:"Supplier not found",breadcrumbs:[{label:"Suppliers",path:P.b.SUPPLIERS},{label:"Profile"}]}),(0,$.jsx)("div",{className:"text-center py-12",children:(0,$.jsx)("p",{className:"text-gray-500",children:C||"Supplier not found"})})]}):(0,$.jsxs)("div",{className:"space-y-6",children:[(0,$.jsx)(n.A,{title:`Supplier: ${j.name}`,description:"Comprehensive supplier profile and management",breadcrumbs:[{label:"Suppliers",path:P.b.SUPPLIERS},{label:j.name}],actions:(0,$.jsxs)("div",{className:"flex gap-2",children:["banned"===j.status?(0,$.jsx)(c.A,{variant:"success",size:"sm",onClick:async()=>{if(j)try{F(!0),await T(j.id),g.current(`Supplier "${j.name}" has been unbanned successfully`),await K()}catch(C){console.error("Error unbanning supplier:",C),h.current("Failed to unban supplier")}finally{F(!1)}},icon:(0,$.jsx)(m.A,{className:"h-4 w-4"}),disabled:k||D||M,loading:D,children:"Unban Supplier"}):(0,$.jsx)(c.A,{variant:"secondary",size:"sm",onClick:()=>O(!0),icon:(0,$.jsx)(x,{className:"h-4 w-4"}),disabled:k||D||M,children:"Ban Supplier"}),(0,$.jsx)(c.A,{variant:"danger",size:"sm",onClick:()=>I(!0),icon:(0,$.jsx)(p.A,{className:"h-4 w-4"}),disabled:k||M||D,children:"Delete Supplier"})]})}),(0,$.jsx)(i.A,{tabs:[{id:"personal",label:"Personal Information"},{id:"products",label:"Products"}],activeTab:y,onChange:e=>{v(e)}}),"personal"===y&&(0,$.jsx)(L,{supplier:j}),"products"===y&&(0,$.jsx)(U,{products:w,supplierId:j.id,onProductUpdate:K}),(0,$.jsx)(o.A,{isOpen:B,onClose:()=>O(!1),title:"Ban Supplier",size:"sm",footer:(0,$.jsxs)($.Fragment,{children:[(0,$.jsx)(c.A,{variant:"outline",onClick:()=>O(!1),disabled:D,children:"Cancel"}),(0,$.jsx)(c.A,{variant:"secondary",onClick:async()=>{if(j)try{F(!0),await V(j.id),g.current(`Supplier "${j.name}" has been banned successfully`),O(!1),await K()}catch(C){console.error("Error banning supplier:",C),h.current("Failed to ban supplier")}finally{F(!1)}},loading:D,icon:(0,$.jsx)(x,{className:"h-4 w-4"}),children:"Ban Supplier"})]}),children:(0,$.jsxs)("div",{className:"text-sm text-gray-500",children:[(0,$.jsxs)("p",{className:"mb-3",children:["Are you sure you want to ban ",(0,$.jsxs)("strong",{children:['"',j.name,'"']}),"?"]}),(0,$.jsx)("p",{className:"text-orange-600 font-medium",children:"This action will:"}),(0,$.jsxs)("ul",{className:"mt-2 list-disc list-inside text-orange-600",children:[(0,$.jsx)("li",{children:"Change the supplier's status to 'banned'"}),(0,$.jsx)("li",{children:"Prevent them from receiving new orders"}),(0,$.jsx)("li",{children:"Restrict their access to the platform"}),(0,$.jsx)("li",{children:"Allow for potential future reactivation"})]}),(0,$.jsx)("p",{className:"mt-3 text-gray-600",children:"Unlike deletion, this action can be reversed by changing the supplier's status back to 'active'."})]})}),(0,$.jsx)(o.A,{isOpen:E,onClose:()=>I(!1),title:"Delete Supplier",size:"sm",footer:(0,$.jsxs)($.Fragment,{children:[(0,$.jsx)(c.A,{variant:"outline",onClick:()=>I(!1),disabled:M,children:"Cancel"}),(0,$.jsx)(c.A,{variant:"danger",onClick:async()=>{if(j)try{R(!0),await _(j.id),g.current(`Supplier "${j.name}" has been deleted successfully`),I(!1),t(P.b.SUPPLIERS)}catch(C){console.error("Error deleting supplier:",C),h.current("Failed to delete supplier")}finally{R(!1)}},loading:M,icon:(0,$.jsx)(p.A,{className:"h-4 w-4"}),children:"Delete Supplier"})]}),children:(0,$.jsxs)("div",{className:"text-sm text-gray-500",children:[(0,$.jsxs)("p",{className:"mb-3",children:["Are you sure you want to delete ",(0,$.jsxs)("strong",{children:['"',j.name,'"']}),"?"]}),(0,$.jsx)("p",{className:"text-red-600 font-medium",children:"This action cannot be undone and will permanently remove:"}),(0,$.jsxs)("ul",{className:"mt-2 list-disc list-inside text-red-600",children:[(0,$.jsx)("li",{children:"All supplier information"}),(0,$.jsx)("li",{children:"Associated products and documents"}),(0,$.jsx)("li",{children:"Order history and analytics"})]})]})})]})}},3893:(e,t,s)=>{s.d(t,{Yq:()=>r,vv:()=>a});const r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return"-";try{const s=new Date(e),r={year:"numeric",month:"short",day:"numeric",...t};return new Intl.DateTimeFormat("en-US",r).format(s)}catch(s){return console.error("Error formatting date:",s),e}},a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"en-US";try{return new Intl.NumberFormat(s,{style:"currency",currency:t,minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}catch(r){return console.error("Error formatting currency:",r),`${t} ${e.toFixed(2)}`}}},4129:(e,t,s)=>{s.d(t,{A:()=>n});var r=s(5043);function a(e,t){let{title:s,titleId:a,...n}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))}const n=r.forwardRef(a)},4692:(e,t,s)=>{s.d(t,{A:()=>o});s(5043);var r=s(4538),a=s(7012),n=s(7098),i=s(5889),l=s(3867),c=s(579);const o=e=>{let{status:t,type:s="user",className:o=""}=e;if(!t)return(0,c.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 ${o}`,children:"Unknown"});const d=t.toLowerCase();let m="",u=null;"active"===d||"verified"===d||"completed"===d?(m="bg-green-100 text-green-800",u=(0,c.jsx)(r.A,{className:"w-4 h-4 mr-1"})):"pending"===d||"processing"===d?(m="bg-blue-100 text-blue-800",u=(0,c.jsx)(a.A,{className:"w-4 h-4 mr-1"})):"banned"===d||"rejected"===d?(m="bg-red-100 text-red-800",u=(0,c.jsx)(n.A,{className:"w-4 h-4 mr-1"})):"shipped"===d?(m="bg-purple-100 text-purple-800",u=(0,c.jsx)(i.A,{className:"w-4 h-4 mr-1"})):"warning"===d?(m="bg-yellow-100 text-yellow-800",u=(0,c.jsx)(l.A,{className:"w-4 h-4 mr-1"})):m="bg-gray-100 text-gray-800";const x=t?t.charAt(0).toUpperCase()+t.slice(1):"Unknown";return(0,c.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${m} ${o}`,children:[u,x]})}},5149:(e,t,s)=>{s.d(t,{A:()=>a});s(5043);var r=s(579);const a=e=>{let{label:t,value:s,className:a=""}=e;return(0,r.jsxs)("div",{className:`py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 ${a}`,children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:t}),(0,r.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:s})]})}},5889:(e,t,s)=>{s.d(t,{A:()=>n});var r=s(5043);function a(e,t){let{title:s,titleId:a,...n}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"}))}const n=r.forwardRef(a)},6365:(e,t,s)=>{s.d(t,{A:()=>n});var r=s(5043);function a(e,t){let{title:s,titleId:a,...n}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))}const n=r.forwardRef(a)},6887:(e,t,s)=>{s.d(t,{A:()=>a});s(5043);var r=s(579);const a=e=>{let{title:t,description:s,children:a,className:n=""}=e;return(0,r.jsxs)("div",{className:`bg-white shadow overflow-hidden sm:rounded-lg ${n}`,children:[(0,r.jsxs)("div",{className:"px-4 py-5 sm:px-6",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:t}),s&&(0,r.jsx)("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:s})]}),(0,r.jsx)("div",{className:"border-t border-gray-200",children:a})]})}},7422:(e,t,s)=>{s.d(t,{A:()=>x});var r=s(5043);function a(e,t){let{title:s,titleId:a,...n}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}const n=r.forwardRef(a);function i(e,t){let{title:s,titleId:a,...n}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"}))}const l=r.forwardRef(i);var c=s(2517),o=s(3927),d=s(1308),m=s(579);function u(e){let{columns:t,data:s,onRowClick:a,title:i,description:u,loading:x=!1,pagination:p=!0,pageSize:h=d.PI.DEFAULT_PAGE_SIZE,selectable:g=!0,onSelectionChange:f,actions:y,emptyMessage:v="No results found",className:j="",headerClassName:b="",bodyClassName:w="",footerClassName:N="",rowClassName:k,initialSortKey:A,initialSortDirection:C="asc",testId:S}=e;const[E,$]=(0,r.useState)(A?{key:A,direction:C}:null),[L,I]=(0,r.useState)(""),[P,M]=(0,r.useState)(1),[R,B]=(0,r.useState)([]),[O,D]=(0,r.useState)(null),U=(0,r.useMemo)((()=>E?[...s].sort(((e,t)=>{const s=e[E.key],r=t[E.key];return null==s&&null==r?0:null==s?"asc"===E.direction?-1:1:null==r?"asc"===E.direction?1:-1:"string"===typeof s&&"string"===typeof r?"asc"===E.direction?s.localeCompare(r):r.localeCompare(s):s<r?"asc"===E.direction?-1:1:s>r?"asc"===E.direction?1:-1:0})):s),[s,E]),z=(0,r.useMemo)((()=>L?U.filter((e=>Object.entries(e).some((e=>{let[t,s]=e;return null!==s&&void 0!==s&&("object"!==typeof s&&String(s).toLowerCase().includes(L.toLowerCase()))})))):U),[U,L]),F=Math.ceil(z.length/h),W=(0,r.useMemo)((()=>{const e=(P-1)*h;return z.slice(e,e+h)}),[z,P,h]),Z=e=>{M(e)},_=e=>{let t="bg-gray-100 text-gray-800";if("string"===typeof e){const s=e.toLowerCase();s.includes("active")||s.includes("approved")||s.includes("verified")||s.includes("completed")||s.includes("success")?t="bg-green-100 text-green-800":s.includes("pending")||s.includes("processing")?t="bg-yellow-100 text-yellow-800":s.includes("rejected")||s.includes("banned")||s.includes("failed")||s.includes("error")?t="bg-red-100 text-red-800":s.includes("inactive")&&(t="bg-gray-100 text-gray-800")}return(0,m.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${t}`,children:e})};return(0,m.jsxs)("div",{className:`bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-md ${j}`,"data-testid":S,children:[(i||u)&&(0,m.jsxs)("div",{className:`px-6 py-4 border-b border-gray-100 ${b}`,children:["string"===typeof i?(0,m.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:i}):i,"string"===typeof u?(0,m.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:u}):u]}),(0,m.jsxs)("div",{className:"p-4 border-b border-gray-100 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,m.jsxs)("div",{className:"relative flex-1",children:[(0,m.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,m.jsx)(n,{className:"h-5 w-5 text-gray-400"})}),(0,m.jsx)("input",{type:"text",placeholder:"Search...",className:"block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200",value:L,onChange:e=>{I(e.target.value),M(1)},"data-testid":`${S}-search`})]}),(0,m.jsxs)("div",{className:"flex items-center space-x-2",children:[R.length>0&&(0,m.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,m.jsxs)("span",{className:"text-sm text-gray-500",children:[R.length," selected"]}),(0,m.jsx)("button",{className:"px-3 py-1.5 bg-red-50 text-red-600 rounded-md text-sm font-medium hover:bg-red-100 transition-colors",onClick:()=>{B([]),f&&f([])},"data-testid":`${S}-clear-selection`,children:"Clear"})]}),y]})]}),(0,m.jsx)("div",{className:`overflow-x-auto ${w}`,children:x?(0,m.jsx)("div",{className:"flex justify-center items-center py-20",children:(0,m.jsx)(o.A,{size:"lg",variant:"spinner"})}):(0,m.jsxs)("table",{className:"min-w-full divide-y divide-gray-100",children:[(0,m.jsx)("thead",{className:"bg-gray-50",children:(0,m.jsxs)("tr",{children:[g&&(0,m.jsx)("th",{className:"w-12 px-6 py-3",children:(0,m.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary",onChange:e=>{const t=e.target.checked?Array.from({length:W.length},((e,t)=>t)):[];if(B(t),f){const e=t.map((e=>W[e])).filter((e=>void 0!==e));f(e)}},checked:R.length===W.length&&W.length>0,"data-testid":`${S}-select-all`})}),t.map((e=>(0,m.jsx)("th",{className:`px-6 py-3 text-${e.align||"left"} text-xs font-medium text-gray-500 uppercase tracking-wider ${e.sortable?"cursor-pointer hover:bg-gray-100":""} transition-colors duration-200 ${e.width?e.width:""} ${e.className||""}`,onClick:()=>e.sortable&&(e=>{let t="asc";E&&E.key===e&&"asc"===E.direction&&(t="desc"),$({key:e,direction:t})})(e.key),"data-testid":`${S}-column-${e.key}`,children:(0,m.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,m.jsx)("span",{children:e.label}),e.sortable&&(0,m.jsx)("span",{className:"transition-colors duration-200 "+((null===E||void 0===E?void 0:E.key)===e.key?"text-primary":"text-gray-400"),children:(null===E||void 0===E?void 0:E.key)===e.key&&"asc"===E.direction?(0,m.jsx)(l,{className:"h-4 w-4"}):(null===E||void 0===E?void 0:E.key)===e.key&&"desc"===E.direction?(0,m.jsx)(c.A,{className:"h-4 w-4"}):(0,m.jsx)("span",{className:"text-gray-300",children:"\u2195"})})]})},e.key)))]})}),(0,m.jsx)("tbody",{className:"bg-white divide-y divide-gray-100",children:W.length>0?W.map(((e,s)=>(0,m.jsxs)("tr",{className:`group transition-all duration-200 ${a?"cursor-pointer":""} ${R.includes(s)?"bg-primary bg-opacity-5":""}\n                    ${O===s?"bg-gray-50":""}\n                    ${k?k(e,s):""}`,onClick:()=>a&&a(e),onMouseEnter:()=>D(s),onMouseLeave:()=>D(null),"data-testid":`${S}-row-${s}`,children:[g&&(0,m.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,m.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary",checked:R.includes(s),onChange:()=>{},onClick:e=>((e,t)=>{t.stopPropagation();const s=[...R];if(R.includes(e)){const t=s.indexOf(e);s.splice(t,1)}else s.push(e);if(B(s),f){const e=s.map((e=>W[e])).filter((e=>void 0!==e));f(e)}})(s,e),"data-testid":`${S}-row-${s}-checkbox`})}),t.map((t=>(0,m.jsx)("td",{className:`px-6 py-4 whitespace-nowrap text-sm text-gray-600 group-hover:text-gray-900 text-${t.align||"left"} ${t.className||""}`,"data-testid":`${S}-row-${s}-cell-${t.key}`,children:t.render?t.render(e[t.key],e):t.key.toLowerCase().includes("status")?_(e[t.key]):e[t.key]},t.key)))]},s))):(0,m.jsx)("tr",{children:(0,m.jsx)("td",{colSpan:t.length+(g?1:0),className:"px-6 py-10 text-center text-gray-500","data-testid":`${S}-empty-message`,children:v})})})]})}),p&&F>1&&(0,m.jsxs)("div",{className:`px-6 py-4 border-t border-gray-100 flex items-center justify-between ${N}`,children:[(0,m.jsxs)("div",{className:"text-sm text-gray-500",children:["Showing ",(P-1)*h+1," to ",Math.min(P*h,z.length)," of ",z.length," entries"]}),(0,m.jsxs)("div",{className:"flex space-x-1",children:[(0,m.jsx)("button",{onClick:()=>Z(Math.max(1,P-1)),disabled:1===P,className:"px-3 py-1 rounded-md text-sm "+(1===P?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"),"data-testid":`${S}-pagination-prev`,children:"Previous"}),Array.from({length:Math.min(5,F)},((e,t)=>{let s;return s=F<=5||P<=3?t+1:P>=F-2?F-4+t:P-2+t,(0,m.jsx)("button",{onClick:()=>Z(s),className:"px-3 py-1 rounded-md text-sm "+(P===s?"bg-primary text-white":"text-gray-700 hover:bg-gray-100"),"data-testid":`${S}-pagination-${s}`,children:s},s)})),(0,m.jsx)("button",{onClick:()=>Z(Math.min(F,P+1)),disabled:P===F,className:"px-3 py-1 rounded-md text-sm "+(P===F?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"),"data-testid":`${S}-pagination-next`,children:"Next"})]})]})]})}const x=(0,r.memo)(u)},8100:(e,t,s)=>{s.d(t,{A:()=>c});var r=s(5043),a=s(7591),n=s(7950),i=s(579);const l=e=>{let{isOpen:t,onClose:s,title:l,children:c,size:o="md",footer:d,closeOnEsc:m=!0,closeOnBackdropClick:u=!0,showCloseButton:x=!0,centered:p=!0,className:h="",bodyClassName:g="",headerClassName:f="",footerClassName:y="",backdropClassName:v="",testId:j}=e;const b=(0,r.useRef)(null);if((0,r.useEffect)((()=>{const e=e=>{m&&"Escape"===e.key&&s()};return t&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="auto"}}),[t,s,m]),(0,r.useEffect)((()=>{if(!t||!b.current)return;const e=b.current.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');if(0===e.length)return;const s=e[0],r=e[e.length-1],a=e=>{"Tab"===e.key&&(e.shiftKey?document.activeElement===s&&(r.focus(),e.preventDefault()):document.activeElement===r&&(s.focus(),e.preventDefault()))};return document.addEventListener("keydown",a),s.focus(),()=>{document.removeEventListener("keydown",a)}}),[t]),!t)return null;const w=(0,i.jsxs)(r.Fragment,{children:[(0,i.jsx)("div",{className:`fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity ${v}`,onClick:u?s:void 0,"data-testid":`${j}-backdrop`}),(0,i.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,i.jsx)("div",{className:`flex min-h-full items-${p?"center":"start"} justify-center p-4 text-center`,children:(0,i.jsxs)("div",{ref:b,className:`${{xs:"max-w-xs",sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[o]} w-full transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all ${h}`,onClick:e=>e.stopPropagation(),"data-testid":j,children:[(0,i.jsxs)("div",{className:`flex items-center justify-between px-6 py-4 border-b border-gray-100 ${f}`,children:["string"===typeof l?(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:l}):l,x&&(0,i.jsx)("button",{type:"button",className:"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary rounded-full p-1",onClick:s,"aria-label":"Close modal","data-testid":`${j}-close-button`,children:(0,i.jsx)(a.A,{className:"h-6 w-6"})})]}),(0,i.jsx)("div",{className:`px-6 py-4 ${g}`,children:c}),d&&(0,i.jsx)("div",{className:`px-6 py-4 bg-gray-50 border-t border-gray-100 flex justify-end space-x-3 ${y}`,children:d})]})})})]});return(0,n.createPortal)(w,document.body)},c=(0,r.memo)(l)},8267:(e,t,s)=>{s.d(t,{A:()=>n});var r=s(5043);function a(e,t){let{title:s,titleId:a,...n}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 7.5-9-5.25L3 7.5m18 0-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9"}))}const n=r.forwardRef(a)},8662:(e,t,s)=>{s.d(t,{A:()=>n});var r=s(5043);function a(e,t){let{title:s,titleId:a,...n}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))}const n=r.forwardRef(a)},8682:(e,t,s)=>{s.d(t,{A:()=>n});var r=s(5043);function a(e,t){let{title:s,titleId:a,...n}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))}const n=r.forwardRef(a)},9248:(e,t,s)=>{s.d(t,{A:()=>n});var r=s(5043);function a(e,t){let{title:s,titleId:a,...n}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))}const n=r.forwardRef(a)},9531:(e,t,s)=>{s.d(t,{A:()=>n});var r=s(5043);function a(e,t){let{title:s,titleId:a,...n}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const n=r.forwardRef(a)},9850:(e,t,s)=>{s.d(t,{A:()=>n});var r=s(5043);function a(e,t){let{title:s,titleId:a,...n}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},n),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))}const n=r.forwardRef(a)}}]);
//# sourceMappingURL=932.a1eab1bc.chunk.js.map