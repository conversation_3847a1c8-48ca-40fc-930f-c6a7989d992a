{"version": 3, "file": "static/js/892.8e5a2e8f.chunk.js", "mappings": "sJAQA,MAWA,EAX8CA,IAGvC,IAHwC,SAC7CC,EAAQ,UACRC,EAAY,IACbF,EACC,OACEG,EAAAA,EAAAA,KAAA,MAAID,UAAW,kCAAkCA,IAAYD,SAC1DA,GACE,C,gDCdT,SAASG,EAASJ,EAIfK,GAAQ,IAJQ,MACjBC,EAAK,QACLC,KACGC,GACJR,EACC,OAAoBS,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,4TAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBL,E,uDCflD,MAaA,EAb8CJ,IAIvC,IAJwC,MAC7CsB,EAAK,MACLC,EAAK,UACLrB,EAAY,IACbF,EACC,OACEwB,EAAAA,EAAAA,MAAA,OAAKtB,UAAW,wDAAwDA,IAAYD,SAAA,EAClFE,EAAAA,EAAAA,KAAA,MAAID,UAAU,oCAAmCD,SAAEqB,KACnDnB,EAAAA,EAAAA,KAAA,MAAID,UAAU,mDAAkDD,SAAEsB,MAC9D,C,8KCPH,MAAME,EAAmB,CAK9BC,iBAAkBC,UAChB,IACE,MAAMC,QAAiBC,EAAAA,EAAUC,IAAoB,mBACrD,OAAOC,EAAAA,GAAmBC,QAAQJ,EAAU,iBAC9C,CAAE,MAAOK,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAQFE,oBAAqBR,UACnB,IACE,MAAMC,QAAiBC,EAAAA,EAAUC,IAAkB,mBAAmBZ,KACtE,OAAOa,EAAAA,GAAmBK,QAAQR,EAAU,gBAAiBV,EAC/D,CAAE,MAAOe,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,KAKS,iBACXP,EAAgB,oBAChBS,GACEV,E,aCxBJ,MAiOA,EAjOgDzB,IAAgD,IAA/C,SAAEqC,EAAQ,SAAEC,EAAQ,UAAEC,GAAY,GAAOvC,EACxF,MAAOwC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,CACvCC,KAAM,GACNC,MAAO,GACPC,KAAM,WACNC,MAAO,GACPC,QAAS,GACTC,aAAc,GACdC,SAAU,GACVC,gBAAiB,GACjBC,YAAY,EACZC,MAAO,QAGFC,EAAQC,IAAaZ,EAAAA,EAAAA,UAAiC,CAAC,IACvDa,EAAeC,IAAoBd,EAAAA,EAAAA,UAAyB,KAC5De,EAAsBC,IAA2BhB,EAAAA,EAAAA,WAAS,IAGjEiB,EAAAA,EAAAA,YAAU,KACkBhC,WACxB+B,GAAwB,GACxB,IACE,MAAME,QAAclC,IACpB8B,EAAiBI,EACnB,CAAE,MAAO3B,GACP4B,QAAQ5B,MAAM,iCAAkCA,EAElD,CAAC,QACCyB,GAAwB,EAC1B,GAGFI,EAAmB,GAClB,IAEH,MAAMC,EAAgBC,IACpB,MAAM,KAAErB,EAAI,MAAEpB,EAAK,KAAEsB,GAASmB,EAAEC,OAEhC,GAAa,aAATpB,EAAqB,CACvB,MAAMqB,EAAWF,EAAEC,OAA4BC,QAC/CzB,GAAY0B,IAAI,IAAUA,EAAM,CAACxB,GAAOuB,KAC1C,MACEzB,GAAY0B,IAAI,IAAUA,EAAM,CAACxB,GAAOpB,MAItC8B,EAAOV,IACTW,GAAUa,IAAI,IAAUA,EAAM,CAACxB,GAAO,MACxC,EAyCF,OACEnB,EAAAA,EAAAA,MAAA,QAAMa,SAdc2B,IAGpB,GAFAA,EAAEI,iBAjBqBC,MACvB,MAAMC,EAAsB,CAC1B3B,KAAM,CAAC4B,EAAAA,GAAgBC,SAAS,qBAChC5B,MAAO,CAAC2B,EAAAA,GAAgBC,SAAS,qBAAsBD,EAAAA,GAAgB3B,SACvEC,KAAM,CAAC0B,EAAAA,GAAgBC,SAAS,0BAChCvB,SAAU,CAACsB,EAAAA,GAAgBC,SAAS,wBAAyBD,EAAAA,GAAgBtB,YAC7EC,gBAAiB,CAACqB,EAAAA,GAAgBC,SAAS,gCAAiCD,EAAAA,GAAgBE,iBAC5F1B,QAAS,CAACwB,EAAAA,GAAgBC,SAAS,wBACnCxB,aAAc,CAACuB,EAAAA,GAAgBC,SAAS,+BAGpCE,GAAYC,EAAAA,EAAAA,GAAanC,EAAU8B,GAEzC,OADAhB,EAAUoB,GAC+B,IAAlChE,OAAOkE,KAAKF,GAAWG,MAAY,EAMtCR,GAAoB,CACtB,MAAMS,EAAa,IACdtC,EAEHY,MAAOZ,EAASY,OAElBf,EAASyC,EACX,GAI8B5E,UAAU,YAAWD,SAAA,EACjDuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,wCAAuCD,SAAA,EACpDE,EAAAA,EAAAA,KAAC4E,EAAAA,EAAS,CACRzD,MAAM,YACNqB,KAAK,OACLpB,MAAOiB,EAASG,KAChBqC,SAAUjB,EACV9B,MAAOoB,EAAOV,KACd6B,UAAQ,KAGVrE,EAAAA,EAAAA,KAAC4E,EAAAA,EAAS,CACRzD,MAAM,QACNqB,KAAK,QACLE,KAAK,QACLtB,MAAOiB,EAASI,MAChBoC,SAAUjB,EACV9B,MAAOoB,EAAOT,MACd4B,UAAQ,KAGVrE,EAAAA,EAAAA,KAAC4E,EAAAA,EAAS,CACRzD,MAAM,YACNqB,KAAK,OACLE,KAAK,SACLtB,MAAOiB,EAASK,KAChBmC,SAAUjB,EACV9B,MAAOoB,EAAOR,KACd2B,UAAQ,EACRS,QAAS,CACP,CAAE1D,MAAO,WAAYD,MAAO,YAC5B,CAAEC,MAAO,WAAYD,MAAO,gBAIhCnB,EAAAA,EAAAA,KAAC4E,EAAAA,EAAS,CACRzD,MAAM,eACNqB,KAAK,QACLpB,MAAOiB,EAASM,MAChBkC,SAAUjB,EACV9B,MAAOoB,EAAOP,SAGhB3C,EAAAA,EAAAA,KAAC4E,EAAAA,EAAS,CACRzD,MAAM,UACNqB,KAAK,UACLE,KAAK,WACLtB,MAAOiB,EAASO,QAChBiC,SAAUjB,EACV9B,MAAOoB,EAAON,QACdyB,UAAQ,EACRU,YAAY,wBAGd/E,EAAAA,EAAAA,KAAC4E,EAAAA,EAAS,CACRzD,MAAM,gBACNqB,KAAK,eACLE,KAAK,SACLtB,MAAOiB,EAASQ,aAChBgC,SAAUjB,EACV9B,MAAOoB,EAAOL,aACdwB,UAAQ,EACRW,QAAS1B,EACTwB,QAAS,CACP,CAAE1D,MAAO,GAAID,MAAO,2BACjBiC,EAAc6B,KAAIvC,IAAI,CACvBtB,MAAOsB,EAAK3B,GACZI,MAAOuB,EAAKF,aAKlBxC,EAAAA,EAAAA,KAAC4E,EAAAA,EAAS,CACRzD,MAAM,WACNqB,KAAK,WACLE,KAAK,WACLtB,MAAOiB,EAASS,SAChB+B,SAAUjB,EACV9B,MAAOoB,EAAOJ,SACduB,UAAQ,KAGVrE,EAAAA,EAAAA,KAAC4E,EAAAA,EAAS,CACRzD,MAAM,mBACNqB,KAAK,kBACLE,KAAK,WACLtB,MAAOiB,EAASU,gBAChB8B,SAAUjB,EACV9B,MAAOoB,EAAOH,gBACdsB,UAAQ,QAKZrE,EAAAA,EAAAA,KAACkF,EAAAA,EAAW,CACV/D,MAAM,kBACNqB,KAAK,QACLpB,MAAOiB,EAASY,MAChB4B,SAzIqBM,IACzB7C,GAAY0B,IAAI,IAAUA,EAAMf,MAAOkC,MAGnCjC,EAAOD,OACTE,GAAUa,IAAI,IAAUA,EAAMf,MAAO,MACvC,EAoIInB,MAAOoB,EAAOD,YAASmC,EACvBC,QAAS,QACTC,aAAc,CAAC,aAAc,YAAa,YAAa,iBAGzDtF,EAAAA,EAAAA,KAAA,OAAKD,UAAU,oBAAmBD,UAChCE,EAAAA,EAAAA,KAAC4E,EAAAA,EAAS,CACRzD,MAAM,wBACNqB,KAAK,aACLE,KAAK,WACLtB,MAAOiB,EAASW,WAChB6B,SAAUjB,EACV7D,UAAU,mCAIdsB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,6BAA4BD,SAAA,EACzCE,EAAAA,EAAAA,KAACuF,EAAAA,EAAM,CACLC,QAAQ,UACRC,QAAStD,EACTuD,SAAUtD,EAAUtC,SACrB,YAGDE,EAAAA,EAAAA,KAACuF,EAAAA,EAAM,CACL7C,KAAK,SACLsC,QAAS5C,EAAUtC,SACpB,kBAIE,E,cC/NX,MAmKA,EAnKkDD,IAI3C,IAJ4C,KACjD8F,EAAI,SACJzD,EAAQ,UACRE,GAAY,GACbvC,EACC,MAAOwC,EAAUC,IAAeC,EAAAA,EAAAA,UAA+B,CAC7DC,KAAM,GACNC,MAAO,GACPC,KAAM,cAGDQ,EAAQC,IAAaZ,EAAAA,EAAAA,UAAiC,CAAC,IAG9DiB,EAAAA,EAAAA,YAAU,KACJmC,GACFrD,EAAY,CACVE,KAAMmD,EAAKnD,KACXC,MAAOkD,EAAKlD,MACZC,KAAMiD,EAAKjD,MAEf,GACC,CAACiD,IAEJ,MAAM/B,EAAgBC,IACpB,MAAM,KAAErB,EAAI,MAAEpB,EAAK,KAAEsB,GAASmB,EAAEC,OAEhC,GAAa,aAATpB,EAAqB,CACvB,MAAMqB,EAAWF,EAAEC,OAA4BC,QAC/CzB,GAAY0B,IAAI,IAAUA,EAAM,CAACxB,GAAOuB,KAC1C,MACEzB,GAAY0B,IAAI,IAAUA,EAAM,CAACxB,GAAOpB,MAItC8B,EAAOV,IACTW,GAAUa,IAAI,IAAUA,EAAM,CAACxB,GAAO,MACxC,EA6BF,OACExC,EAAAA,EAAAA,KAAC4F,EAAAA,EAAI,CAAA9F,UACHuB,EAAAA,EAAAA,MAAA,QAAMa,SAhBWV,UAGnB,GAFAqC,EAAEI,iBAbqBC,MACvB,MAAMC,EAAsB,CAC1B3B,KAAM,CAAC4B,EAAAA,GAAgBC,SAAS,qBAChC5B,MAAO,CAAC2B,EAAAA,GAAgBC,SAAS,qBAAsBD,EAAAA,GAAgB3B,SACvEC,KAAM,CAAC0B,EAAAA,GAAgBC,SAAS,2BAG5BE,GAAYC,EAAAA,EAAAA,GAAanC,EAAU8B,GAEzC,OADAhB,EAAUoB,GAC+B,IAAlChE,OAAOkE,KAAKF,GAAWG,MAAY,EAMtCR,GACF,UACQhC,EAASG,EAEjB,CAAE,MAAOP,GAEP4B,QAAQ5B,MAAM,yBAA0BA,EAC1C,CACF,EAKgC/B,UAAU,gBAAeD,SAAA,EACrDuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,wCAAuCD,SAAA,EACpDuB,EAAAA,EAAAA,MAAA,OAAAvB,SAAA,EACEuB,EAAAA,EAAAA,MAAA,SAAOwE,QAAQ,OAAO9F,UAAU,0CAAyCD,SAAA,CAAC,cAC9DE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,eAAcD,SAAC,UAE3CE,EAAAA,EAAAA,KAAA,SACE0C,KAAK,OACL3B,GAAG,OACHyB,KAAK,OACLpB,MAAOiB,EAASG,KAChBqC,SAAUjB,EACV7D,UAAW,8GACTmD,EAAa,KAAI,iBAAmB,MAGvCA,EAAa,OAAKlD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,4BAA2BD,SAAEoD,EAAa,WAG5E7B,EAAAA,EAAAA,MAAA,OAAAvB,SAAA,EACEuB,EAAAA,EAAAA,MAAA,SAAOwE,QAAQ,QAAQ9F,UAAU,0CAAyCD,SAAA,CAAC,UACnEE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,eAAcD,SAAC,UAEvCE,EAAAA,EAAAA,KAAA,SACE0C,KAAK,QACL3B,GAAG,QACHyB,KAAK,QACLpB,MAAOiB,EAASI,MAChBoC,SAAUjB,EACV7D,UAAW,8GACTmD,EAAc,MAAI,iBAAmB,MAGxCA,EAAc,QAAKlD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,4BAA2BD,SAAEoD,EAAc,YAG9E7B,EAAAA,EAAAA,MAAA,OAAAvB,SAAA,EACEuB,EAAAA,EAAAA,MAAA,SAAOwE,QAAQ,OAAO9F,UAAU,0CAAyCD,SAAA,CAAC,cAC9DE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,eAAcD,SAAC,UAE3CuB,EAAAA,EAAAA,MAAA,UACEN,GAAG,OACHyB,KAAK,OACLpB,MAAOiB,EAASK,KAChBmC,SAAUjB,EACV7D,UAAW,8GACTmD,EAAa,KAAI,iBAAmB,IACnCpD,SAAA,EAEHE,EAAAA,EAAAA,KAAA,UAAQoB,MAAM,WAAUtB,SAAC,cACzBE,EAAAA,EAAAA,KAAA,UAAQoB,MAAM,WAAUtB,SAAC,gBAE1BoD,EAAa,OAAKlD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,4BAA2BD,SAAEoD,EAAa,WAG5E7B,EAAAA,EAAAA,MAAA,OAAAvB,SAAA,EACEE,EAAAA,EAAAA,KAAA,SAAO6F,QAAQ,WAAW9F,UAAU,0CAAyCD,SAAC,kBAG9EE,EAAAA,EAAAA,KAAA,SACE0C,KAAK,WACL3B,GAAG,WACHyB,KAAK,WACLpB,MAAOiB,EAASS,UAAY,GAC5B+B,SAAUjB,EACVmB,YAAY,uCACZhF,UAAW,8GACTmD,EAAiB,SAAI,iBAAmB,MAG3CA,EAAiB,WAAKlD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,4BAA2BD,SAAEoD,EAAiB,kBAItF7B,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,6BAA4BD,SAAA,EACzCE,EAAAA,EAAAA,KAACuF,EAAAA,EAAM,CACLC,QAAQ,UACR9C,KAAK,SACL+C,QAASA,IAAMK,OAAOC,QAAQC,OAC9BN,SAAUtD,EAAUtC,SACrB,YAGDE,EAAAA,EAAAA,KAACuF,EAAAA,EAAM,CACL7C,KAAK,SACLsC,QAAS5C,EAAUtC,SACpB,wBAKA,E,yEChKX,MA8EA,EA9EoDD,IAO7C,IAP8C,OACnDoG,EAAM,MACN9F,EAAQ,SAAQ,YAChB+F,EAAc,gBAAe,YAC7BC,EAAW,aACXC,EAAe,kBAAiB,UAChCrG,EAAY,IACbF,EACC,OACEG,EAAAA,EAAAA,KAACqG,EAAAA,EAAa,CACZlG,MAAOA,EACP+F,YAAaA,EACbnG,UAAWA,EAAUD,SAEF,IAAlBmG,EAAOvB,QACN1E,EAAAA,EAAAA,KAAA,OAAKD,UAAU,8CAA6CD,SACzDsG,KAGHpG,EAAAA,EAAAA,KAAA,OAAKD,UAAU,kBAAiBD,UAC9BuB,EAAAA,EAAAA,MAAA,SAAOtB,UAAU,sCAAqCD,SAAA,EACpDE,EAAAA,EAAAA,KAAA,SAAOD,UAAU,aAAYD,UAC3BuB,EAAAA,EAAAA,MAAA,MAAAvB,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAIsG,MAAM,MAAMvG,UAAU,iFAAgFD,SAAC,cAG3GE,EAAAA,EAAAA,KAAA,MAAIsG,MAAM,MAAMvG,UAAU,iFAAgFD,SAAC,UAG3GE,EAAAA,EAAAA,KAAA,MAAIsG,MAAM,MAAMvG,UAAU,iFAAgFD,SAAC,YAG3GE,EAAAA,EAAAA,KAAA,MAAIsG,MAAM,MAAMvG,UAAU,iFAAgFD,SAAC,YAG3GE,EAAAA,EAAAA,KAAA,MAAIsG,MAAM,MAAMvG,UAAU,kFAAiFD,SAAC,kBAKhHE,EAAAA,EAAAA,KAAA,SAAOD,UAAU,oCAAmCD,SACjDmG,EAAOhB,KAAKsB,IACXlF,EAAAA,EAAAA,MAAA,MAAmBtB,UAAU,mBAAkBD,SAAA,EAC7CE,EAAAA,EAAAA,KAAA,MAAID,UAAU,+DAA8DD,SACzEyG,EAAMxF,MAETf,EAAAA,EAAAA,KAAA,MAAID,UAAU,oDAAmDD,UAC9D0G,EAAAA,EAAAA,IAAWD,EAAME,cAEpBzG,EAAAA,EAAAA,KAAA,MAAID,UAAU,oDAAmDD,UAC9D4G,EAAAA,EAAAA,IAAeH,EAAMI,gBAExB3G,EAAAA,EAAAA,KAAA,MAAID,UAAU,8BAA6BD,UACzCE,EAAAA,EAAAA,KAAC4G,EAAAA,EAAW,CAACC,OAAQN,EAAMM,OAAQnE,KAAK,aAE1C1C,EAAAA,EAAAA,KAAA,MAAID,UAAU,6DAA4DD,SACvEqG,IACCnG,EAAAA,EAAAA,KAACuF,EAAAA,EAAM,CACLC,QAAQ,OACRsB,KAAK,KACLrB,QAASA,IAAMU,EAAYI,GAC3BQ,MAAM/G,EAAAA,EAAAA,KAACgH,EAAAA,EAAO,CAACjH,UAAU,uBACzBA,UAAU,mDAAkDD,SAC7D,aArBEyG,EAAMxF,cAgCX,E,aCrEpB,MAiCA,EAjCgDlB,IAAgC,IAA/B,KAAE8F,EAAI,WAAEsB,EAAa,IAAIpH,EACxE,MAAMqH,GAAWC,EAAAA,EAAAA,MAMjB,OACE9F,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,YAAWD,SAAA,EACxBE,EAAAA,EAAAA,KAACqG,EAAAA,EAAa,CACZlG,MAAM,mBACN+F,YAAY,mCAAkCpG,UAE9CuB,EAAAA,EAAAA,MAAC+F,EAAAA,EAAU,CAAAtH,SAAA,EACTE,EAAAA,EAAAA,KAACqH,EAAAA,EAAU,CAAClG,MAAM,YAAYC,MAAOuE,EAAKnD,QAC1CxC,EAAAA,EAAAA,KAACqH,EAAAA,EAAU,CAAClG,MAAM,gBAAgBC,MAAOuE,EAAKlD,SAC9CzC,EAAAA,EAAAA,KAACqH,EAAAA,EAAU,CAAClG,MAAM,YAAYC,MAAOuE,EAAKjD,QAC1C1C,EAAAA,EAAAA,KAACqH,EAAAA,EAAU,CAAClG,MAAM,SAASC,OAAOpB,EAAAA,EAAAA,KAAC4G,EAAAA,EAAW,CAACC,OAAQlB,EAAKkB,OAAQnE,KAAK,YACzE1C,EAAAA,EAAAA,KAACqH,EAAAA,EAAU,CAAClG,MAAM,aAAaC,MAAOuE,EAAK2B,kBAI/CtH,EAAAA,EAAAA,KAACuH,EAAa,CACZtB,OAAQgB,EACR9G,MAAM,cACN+F,YAAY,6BACZC,YAvBmBI,IACvBW,EAASM,EAAAA,EAAOC,qBAAqBlB,EAAMxF,IAAI,EAuB3CqF,aAAa,8CAEX,E,kCChCV,MAsFA,EAtF0DvG,IAAe,IAAd,KAAE8F,GAAM9F,EACjE,OACEwB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,YAAWD,SAAA,EACxBE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,oCAAmCD,UAChDuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,8BAA6BD,SAAA,EAC1CE,EAAAA,EAAAA,KAAC0H,EAAAA,EAAM,IACA/B,EAAKgC,QAAU,CAAEC,IAAKjC,EAAKgC,QAChCE,IAAKlC,EAAKnD,KACVA,KAAMmD,EAAKnD,KACXsE,KAAK,QAEPzF,EAAAA,EAAAA,MAAA,OAAAvB,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAID,UAAU,oCAAmCD,SAAE6F,EAAKnD,QACxDnB,EAAAA,EAAAA,MAAA,KAAGtB,UAAU,wBAAuBD,SAAA,CAAC,OAAK6F,EAAK5E,OAC/Cf,EAAAA,EAAAA,KAAA,OAAKD,UAAU,OAAMD,UACnBE,EAAAA,EAAAA,KAAA,QAAMD,UAAW,4EACC,WAAhB4F,EAAKkB,OACD,8BACA,2BACH/G,SACA6F,EAAKkB,OAAOiB,OAAO,GAAGC,cAAgBpC,EAAKkB,OAAOmB,MAAM,gBAOnE3G,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,sEAAqED,SAAA,EAClFuB,EAAAA,EAAAA,MAAA,OAAAvB,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAID,UAAU,yCAAwCD,SAAC,yBACvDuB,EAAAA,EAAAA,MAAA,MAAItB,UAAU,YAAWD,SAAA,EACvBuB,EAAAA,EAAAA,MAAA,OAAAvB,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAID,UAAU,wBAAuBD,SAAC,WACtCE,EAAAA,EAAAA,KAAA,MAAID,UAAU,wBAAuBD,SAAE6F,EAAKlD,WAE7CkD,EAAKhD,QACJtB,EAAAA,EAAAA,MAAA,OAAAvB,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAID,UAAU,wBAAuBD,SAAC,WACtCE,EAAAA,EAAAA,KAAA,MAAID,UAAU,wBAAuBD,SAAE6F,EAAKhD,YAGhDtB,EAAAA,EAAAA,MAAA,OAAAvB,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAID,UAAU,wBAAuBD,SAAC,gBACtCE,EAAAA,EAAAA,KAAA,MAAID,UAAU,wBAAuBD,SAAE6F,EAAK2B,qBAKjD3B,EAAK/C,UACJvB,EAAAA,EAAAA,MAAA,OAAAvB,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAID,UAAU,yCAAwCD,SAAC,aACvDE,EAAAA,EAAAA,KAAA,WAASD,UAAU,mCAAkCD,SAClD6F,EAAK/C,gBAMb+C,EAAK9C,eACJxB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,gCAA+BD,SAAA,EAC5CE,EAAAA,EAAAA,KAAA,MAAID,UAAU,yCAAwCD,SAAC,mBACvDE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,oGAAmGD,SAChH6F,EAAK9C,mBAKZxB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,gCAA+BD,SAAA,EAC5CE,EAAAA,EAAAA,KAAA,MAAID,UAAU,yCAAwCD,SAAC,oBACvDuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,8BAA6BD,SAAA,CACzB,WAAhB6F,EAAKkB,QACJ7G,EAAAA,EAAAA,KAACiI,EAAAA,EAAe,CAAClI,UAAU,4BAE3BC,EAAAA,EAAAA,KAACkI,EAAAA,EAAW,CAACnI,UAAU,0BAEzBC,EAAAA,EAAAA,KAAA,QAAMD,UAAU,wBAAuBD,SACpB,WAAhB6F,EAAKkB,OACF,gBAAgBlB,EAAK2B,YACrB,8BAIN,E,4CCxEV,MA0GA,EA1G0CzH,IAQnC,IARoC,MACzCsI,EAAK,WACLC,EACAC,WAAYC,EAAW,aACvBC,EACAC,YAAaC,EAAY,MACzBtI,EAAQ,QAAO,QACf6E,GAAU,GACXnF,EACC,MAAMqH,GAAWC,EAAAA,EAAAA,MAQXuB,EAA0B,CAC9B,CACEC,IAAK,OACLxH,MAAO,OACPyH,UAAU,EACVC,OAAQA,CAACC,EAAQnD,KACftE,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,oBAAmBD,SAAA,CAC/B6F,EAAKgC,QACJ3H,EAAAA,EAAAA,KAAA,OACE4H,IAAKjC,EAAKgC,OACVE,IAAKlC,EAAKnD,KACVzC,UAAU,4CAGZC,EAAAA,EAAAA,KAAA,OAAKD,UAAU,mFAAkFD,SAC9F6F,EAAKnD,KAAKsF,OAAO,MAGtBzG,EAAAA,EAAAA,MAAA,OAAAvB,SAAA,EACEE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,4BAA2BD,SAAE6F,EAAKnD,QACjDnB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,wBAAuBD,SAAA,CAAC,OAAK6F,EAAK5E,aAKzD,CACE4H,IAAK,QACLxH,MAAO,QACPyH,UAAU,EACVC,OAASzH,IACPC,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAAC+I,EAAAA,EAAY,CAAChJ,UAAU,gCACxBC,EAAAA,EAAAA,KAAA,QAAAF,SAAOsB,QAIb,CAAEuH,IAAK,OAAQxH,MAAO,OAAQyH,UAAU,GACxC,CAAED,IAAK,SAAUxH,MAAO,SAAUyH,UAAU,GAC5C,CAAED,IAAK,YAAaxH,MAAO,aAAcyH,UAAU,GACnD,CACED,IAAK,UACLxH,MAAO,UACP0H,OAAQA,CAACG,EAAGrD,KACVtE,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,8BAA6BD,SAAA,EAC1CE,EAAAA,EAAAA,KAAA,UACED,UAAU,sEACV0F,QAAU5B,IACRA,EAAEoF,kBACFb,EAAWzC,EAAK,EAChB7F,UAEFE,EAAAA,EAAAA,KAACgH,EAAAA,EAAO,CAACjH,UAAU,eAErBC,EAAAA,EAAAA,KAAA,UACED,UAAU,uEACV0F,QAAU5B,IACRA,EAAEoF,kBACF/B,EAASM,EAAAA,EAAO0B,iBAAiBvD,EAAK5E,IAAI,EAC1CjB,UAEFE,EAAAA,EAAAA,KAACmJ,EAAAA,EAAU,CAACpJ,UAAU,eAExBC,EAAAA,EAAAA,KAAA,UACED,UAAU,sEACV0F,QAAU5B,IACRA,EAAEoF,kBACFV,EAAa5C,EAAK,EAClB7F,UAEFE,EAAAA,EAAAA,KAACoJ,EAAAA,EAAS,CAACrJ,UAAU,mBAO/B,OACEC,EAAAA,EAAAA,KAACqJ,EAAAA,EAAc,CACbC,KAAMnB,EACNO,QAASA,EACTa,WArFoB5D,IACtBuB,EAASM,EAAAA,EAAO0B,iBAAiBvD,EAAK5E,IAAI,EAqFxCZ,MAAOA,EACPqJ,YAAY,EACZxE,QAASA,EACToB,aAAa,kBACb,E,4CC5GN,MAAMqD,EAA0B1C,IAC9B,IAAKzG,EAAAA,eAAqByG,GAAO,MAAO,2BAGxC,MAGM2C,GAHY3C,EAAK1G,MAAMN,WAAa,IAGb4J,MAAM,kBACnC,GAAID,EAAY,CAEd,MAAO,MADOA,EAAW,OAE3B,CAEA,MAAO,0BAA0B,EAkDnC,EA/C8C7J,IAKvC,IALwC,MAC7CM,EAAK,KACLmJ,EAAI,KACJvC,EAAI,YACJ6C,EAAexI,GAAUA,EAAMyI,YAChChK,EACC,OACEG,EAAAA,EAAAA,KAAC4F,EAAAA,EAAI,CAAA9F,UACHuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAAA,OAAKD,UAAW,oBAAoB0J,EAAuB1C,KAAQjH,SAEhEQ,EAAAA,eAAqByG,GACpB,MACE,MAAM+C,EAAc/C,EAEd2C,GADoBI,EAAYzJ,MAAMN,WAAa,IACpB4J,MAAM,mBACrCI,EAAaL,EAAaA,EAAW,GAAK,gBAGhD,OAAOpJ,EAAAA,aAAmBwJ,EAAa,CACrC/J,UAAW,WAAgBgK,KAE9B,EAVD,GAYAhD,KAGJ1F,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,cAAaD,SAAA,EAC1BE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,oCAAmCD,SAAEK,KAClDkB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,sBAAqBD,SAAA,EAClCE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,uCAAsCD,SAChD8J,EAAYN,EAAKU,cAEH5E,IAAhBkE,EAAKW,SACJ5I,EAAAA,EAAAA,MAAA,KAAGtB,UAAW,mDACZuJ,EAAKW,QAAU,EAAI,iBAAmB,gBACrCnK,SAAA,CACAwJ,EAAKW,QAAU,EAAI,IAAM,GAAIX,EAAKW,OAAOC,QAAQ,GAAG,iBAM1D,ECvCX,EArBsDrK,IAG/C,IAHgD,QACrDsK,EAAO,UACPpK,EAAY,IACbF,EACC,OACEG,EAAAA,EAAAA,KAAA,OAAKD,UAAW,yCAAyCA,IAAYD,SAClEqK,EAAQlF,KAAI,CAACmF,EAAQC,KACpBrK,EAAAA,EAAAA,KAACsK,EAAU,CAETnK,MAAOiK,EAAOjK,MACdmJ,KAAM,CACJU,MAA+B,kBAAjBI,EAAOhJ,MAAqBmJ,WAAWH,EAAOhJ,QAAU,EAAIgJ,EAAOhJ,MACjF6I,OAAQG,EAAOI,QAAU,GAE3BzD,KAAMqD,EAAOrD,MANRsD,MASL,E,cCjBV,MAwNA,EAxNoDxK,IAG7C,IAAD4K,EAAAC,EAAAC,EAAA,IAFJC,OAAQC,EAAO,SACfC,GACDjL,EAEC,MAAMkL,EAAe,CACnBC,aAAqB,OAARF,QAAQ,IAARA,OAAQ,EAARA,EAAUE,cAAe,EACtCC,YAAoB,OAARH,QAAQ,IAARA,OAAQ,EAARA,EAAUG,aAAc,EACpCC,mBAA2B,OAARJ,QAAQ,IAARA,OAAQ,EAARA,EAAUI,oBAAqB,EAClDC,gBAAwB,OAARL,QAAQ,IAARA,OAAQ,EAARA,EAAUK,iBAAkB,EAC5CC,cAAsB,OAARN,QAAQ,IAARA,OAAQ,EAARA,EAAUM,eAAgB,IAMpCjB,EAAoB,CACxB,CACEhK,MAAO,eACPiB,MAAO2J,EAAaC,YACpBjE,MAAM/G,EAAAA,EAAAA,KAACqL,EAAAA,EAAgB,CAACtL,UAAU,2BAEpC,CACEI,MAAO,cACPiB,OAAOsF,EAAAA,EAAAA,IAAeqE,EAAaE,YACnClE,MAAM/G,EAAAA,EAAAA,KAACsL,EAAAA,EAAkB,CAACvL,UAAU,4BAEtC,CACEI,MAAO,gBACPiB,OAAOsF,EAAAA,EAAAA,IAAeqE,EAAaG,mBACnCnE,MAAM/G,EAAAA,EAAAA,KAACuL,EAAAA,EAAS,CAACxL,UAAU,8BAKzByL,EAAsBC,IAC1B,IAGE,OAFa,IAAIC,KAAKD,GAEVE,mBAAmB,QAAS,CACtCC,MAAO,QACPC,IAAK,UACLC,KAAM,WAEV,CAAE,MAAOhK,GAEP,OAAO2J,CACT,GAkDIM,EA9C2BC,MAC/B,GAAyC,IAArCjB,EAAaK,aAAa1G,OAC5B,MAAO,CACLuH,OAAQ,CAAC,WACTC,SAAU,CAAC,CACT/K,MAAO,iBACPmI,KAAM,CAAC,GACP6C,YAAa,UACbC,gBAAiB,0BACjB1L,MAAM,EACN2L,QAAS,MAMf,MAAMC,EAAevB,EAAaK,aAC/BmB,QAAOC,GAAQA,GAAQA,EAAKC,MAA+B,kBAAhBD,EAAKE,SAChDC,MAAK,CAACC,EAAGC,IAAM,IAAInB,KAAKkB,EAAEH,MAAMK,UAAY,IAAIpB,KAAKmB,EAAEJ,MAAMK,YAEhE,IAAIC,EAAqB,EACzB,MAAMC,EAAYV,EAAarH,KAAIsB,IACjCwG,GAAsBxG,EAAMmG,OACrB,CACLvL,MAAOqK,EAAmBjF,EAAMkG,MAChCrL,MAAO2L,MAIX,MAAO,CACLd,OAAQe,EAAU/H,KAAIuH,GAAQA,EAAKrL,QACnC+K,SAAU,CAAC,CACT/K,MAAO,sBACPmI,KAAM0D,EAAU/H,KAAIuH,GAAQA,EAAKpL,QACjC+K,YAAa,UACbC,gBAAiB,0BACjB1L,MAAM,EACN2L,QAAS,GACTY,qBAAsB,UACtBC,iBAAkB,UAClBC,iBAAkB,EAClBC,YAAa,IAEhB,EAGuBpB,GAE1B,OACE3K,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,YAAWD,SAAA,EACxBE,EAAAA,EAAAA,KAACqN,EAAc,CAAClD,QAASA,KAEzB9I,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,wCAAuCD,SAAA,EAEpDuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,iDAAgDD,SAAA,EAC7DuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,OAAMD,SAAA,EACnBE,EAAAA,EAAAA,KAAA,MAAID,UAAU,sCAAqCD,SAAC,oBACpDE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,wBAAuBD,SACjCiL,EAAaK,aAAa1G,OAAS,EAChC,gCACA,kCAKPqG,EAAaK,aAAa1G,OAAS,GAClC1E,EAAAA,EAAAA,KAAA,OAAKD,UAAU,OAAMD,UACnBE,EAAAA,EAAAA,KAACsN,EAAAA,GAAI,CACHhE,KAAMyC,EACNjH,QAAS,IACJyI,EAAAA,GACHC,QAAS,IACJD,EAAAA,GAAwBC,QAC3BrN,MAAO,CACLsN,SAAS,GAEXC,OAAQ,CACND,SAAS,IAGbE,OAAQ,IACHJ,EAAAA,GAAwBI,OAC3BC,EAAG,IACgC,QAAjCnD,EAAG8C,EAAAA,GAAwBI,cAAM,IAAAlD,OAAA,EAA9BA,EAAgCmD,EACnCC,MAAO,IAC4B,QAAjCnD,EAAG6C,EAAAA,GAAwBI,cAAM,IAAAjD,GAAG,QAAHC,EAA9BD,EAAgCkD,SAAC,IAAAjD,OAAH,EAA9BA,EAAmCkD,MACtCC,SAAU,SAAS1M,GACjB,OAAOsF,EAAAA,EAAAA,IAAetF,EACxB,WAQZpB,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wCAAuCD,UACpDuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,cAAaD,SAAA,EAC1BE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,8BAA6BD,SAAC,YAC7CE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,oCAAmCD,SAAC,sBACjDE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BD,SAAC,0EASlDuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,iDAAgDD,SAAA,EAC7DuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,OAAMD,SAAA,EACnBE,EAAAA,EAAAA,KAAA,MAAID,UAAU,sCAAqCD,SAAC,qBACpDE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,wBAAuBD,SAAC,8CAGvCE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,YAAWD,SACvBiL,EAAaK,aAAa1G,OAAS,GAClCrD,EAAAA,EAAAA,MAAA0M,EAAAA,SAAA,CAAAjO,SAAA,EACEuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,8DAA6DD,SAAA,EAC1EE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,oCAAmCD,SAAC,uBACpDE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,wBAAuBD,SACpC,MACC,MAAMkO,EAAYjD,EAAaK,aAAaL,EAAaK,aAAa1G,OAAS,GAC/E,OAAgB,OAATsJ,QAAS,IAATA,GAAAA,EAAWvB,KAAOjB,EAAmBwC,EAAUvB,MAAQ,KAC/D,EAHA,SAMLpL,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,8DAA6DD,SAAA,EAC1EE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,oCAAmCD,SAAC,iBACpDE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,wBAAuBD,SACpC,MACC,MAAMmO,EAAalD,EAAaK,aAAa,GAC7C,OAAiB,OAAV6C,QAAU,IAAVA,GAAAA,EAAYxB,KAAOjB,EAAmByC,EAAWxB,MAAQ,KACjE,EAHA,SAMLpL,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,8DAA6DD,SAAA,EAC1EE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,oCAAmCD,SAAC,mBACpDE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,wBAAuBD,UACpC4G,EAAAA,EAAAA,IAAewH,KAAKC,OAAOpD,EAAaK,aAAanG,KAAIsB,GAASA,EAAMmG,iBAG7ErL,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,8DAA6DD,SAAA,EAC1EE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,oCAAmCD,SAAC,oBACpDE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,wBAAuBD,UACpC4G,EAAAA,EAAAA,IAAewH,KAAKE,OAAOrD,EAAaK,aAAanG,KAAIsB,GAASA,EAAMmG,oBAK/ErL,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,mBAAkBD,SAAA,EAC/BE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,8BAA6BD,SAAC,kBAC7CE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,4BAA2BD,SAAC,mBACzCE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BD,SAAC,2DAQhD,E,aCpOH,MAAMuO,EAWJ,CACLC,KAAM,SACNC,QAAUxN,GAAe,UAAUA,IACnCyN,OAASzN,GAAe,UAAUA,WAClC0N,IAAM1N,GAAe,UAAUA,QAC/B2N,MAAQ3N,GAAe,UAAUA,UACjC4N,aAAc,uBCJLC,EAA8BC,IACzC,MAAMC,EAAuB,CAC3BC,KAAMF,EAAarM,KACnBwM,MAAOH,EAAapM,MACpBwM,mBAAoBJ,EAAaI,oBAAsB,WAgBzD,YAb8B7J,IAA1ByJ,EAAa/L,WACfgM,EAAOhM,SAAW+L,EAAa/L,eAENsC,IAAvByJ,EAAalM,QACfmM,EAAOI,YAAcL,EAAalM,YAEPyC,IAAzByJ,EAAajM,UACfkM,EAAOK,QAAUN,EAAajM,cAEEwC,IAA9ByJ,EAAahM,eACfiM,EAAOM,aAAeP,EAAahM,cAG9BiM,CAAM,EAMFO,EAAkCC,IAC7C,MAAMR,EAAe,CACnB/N,GAAIuO,EAAYvO,GAChByB,KAAM8M,EAAY9M,KAClBC,MAAO6M,EAAY7M,MACnBC,KAAM4M,EAAY5M,KAClBmE,OAAQyI,EAAYzI,OACpBoI,mBAAoBK,EAAYL,oBAAsB,WAmBxD,YAhB2B7J,IAAvBkK,EAAY3H,SACdmH,EAAOnH,OAAS2H,EAAY3H,aAEJvC,IAAtBkK,EAAY3M,QACdmM,EAAOnM,MAAQ2M,EAAY3M,YAEDyC,IAAxBkK,EAAY1M,UACdkM,EAAOlM,QAAU0M,EAAY1M,cAEEwC,IAA7BkK,EAAYzM,eACdiM,EAAOjM,aAAeyM,EAAYzM,mBAENuC,IAA1BkK,EAAYhI,YACdwH,EAAOxH,UAAYgI,EAAYhI,WAG1BwH,CAAM,EAsBFS,EAA8B9N,IACzC,IAAKA,EACH,MAAM,IAAI+N,MAAM,2BAGlB,GAAwB,kBAAb/N,EACT,MAAM,IAAI+N,MAAM,2BAGlB,KAAM,YAAa/N,GACjB,MAAM,IAAI+N,MAAM,kCAGlB,IAAyB,IAArB/N,EAASgO,QACX,MAAM,IAAID,MAAM/N,EAASiO,SAAW,kBAGtC,KAAM,SAAUjO,GACd,MAAM,IAAI+N,MAAM,+BAGlB,OAAO/N,CAAQ,EA2BJkO,EAAyBC,IACpC,MAAMC,EAAoBN,EAAqCK,GAEzDE,EAAkBT,EAA+BQ,EAAkBvG,MAEzE,MAAO,CACLmG,QAASI,EAAkBJ,QAC3BC,QAASG,EAAkBH,QAC3BpG,KAAMwG,EACP,EAMUC,EAAuBC,GACL,kBAAlBA,EACFA,EAGQ,OAAbA,QAAa,IAAbA,GAAAA,EAAeN,QACVM,EAAcN,QAGN,OAAbM,QAAa,IAAbA,GAAAA,EAAelO,MACVkO,EAAclO,MAGhB,+BC5IImO,EAAW,CAMtBC,SAAU1O,UACR,IACE,MAAM2O,EAAgBC,EDwCkBA,KAC5C,MAAMD,EAAqC,CAAC,EAS5C,YAPoB/K,IAAhBgL,EAAOC,OAAoBF,EAAcE,KAAOD,EAAOC,WACtCjL,IAAjBgL,EAAOE,QAAqBH,EAAcG,MAAQF,EAAOE,YACvClL,IAAlBgL,EAAOG,SAAsBJ,EAAcI,OAASH,EAAOG,aACzCnL,IAAlBgL,EAAOvJ,SAAsBsJ,EAActJ,OAASuJ,EAAOvJ,aAC3CzB,IAAhBgL,EAAOzD,OAAoBwD,EAAcxD,KAAOyD,EAAOzD,WACtCvH,IAAjBgL,EAAO7J,QAAqB4J,EAAc5J,MAAQ6J,EAAO7J,OAEtD4J,CAAa,EClDeK,CAA8BJ,GAAU,CAAC,EAClE3O,QAAiBC,EAAAA,EAAUC,IAAI0M,EAAgBC,KAAM,CAAE8B,OAAQD,IAGrE,GAAI1O,EAASK,MACX,MAAM,IAAI0N,MAAM/N,EAASK,OAG3B,IAAKL,EAAS6H,KACZ,MAAM,IAAIkG,MAAM,6BAIlB,MDsEoCI,KACxC,MAAMC,EAAoBN,EAAuCK,GAE3Da,EAAmBZ,EAAkBvG,KAAKrE,IAAIoK,GAE9CP,EAAqC,CACzCW,QAASI,EAAkBJ,QAC3BC,QAASG,EAAkBH,QAC3BpG,KAAMmH,GAOR,YAJqCrL,IAAjCyK,EAAkBrG,aACpBsF,EAAOtF,WAAaqG,EAAkBrG,YAGjCsF,CAAM,ECrFF4B,CAA0BjP,EAAS6H,KAC5C,CAAE,MAAOxH,GAAa,IAAD6O,EAEnB,GAAkB,QAAlBA,EAAI7O,EAAML,gBAAQ,IAAAkP,GAAdA,EAAgBrH,KAAM,CACxB,MAAMsH,EAAeb,EAAoBjO,EAAML,SAAS6H,MACxD,MAAM,IAAIkG,MAAMoB,EAClB,CACA,MAAM7O,EAAAA,EAAAA,IAAeD,EACvB,GAQF+O,YAAarP,UACX,IACE,MAAMC,QAAiBC,EAAAA,EAAUC,IAAI0M,EAAgBE,QAAQxN,IAG7D,GAAIU,EAASK,MACX,MAAM,IAAI0N,MAAM/N,EAASK,OAG3B,IAAKL,EAAS6H,KACZ,MAAM,IAAIkG,MAAM,6BAKlB,OAD4BG,EAAsBlO,EAAS6H,MAChCA,IAC7B,CAAE,MAAOxH,GAAa,IAADgP,EAEnB,GAAkB,QAAlBA,EAAIhP,EAAML,gBAAQ,IAAAqP,GAAdA,EAAgBxH,KAAM,CACxB,MAAMsH,EAAeb,EAAoBjO,EAAML,SAAS6H,MACxD,MAAM,IAAIkG,MAAMoB,EAClB,CACA,MAAM7O,EAAAA,EAAAA,IAAeD,EACvB,GAQFiP,WAAYvP,UACV,IAEE,MAAMwP,EAAcpC,EAA2B9D,GACzCrJ,QAAiBC,EAAAA,EAAUuP,KAAK5C,EAAgBC,KAAM0C,GAG5D,GAAIvP,EAASK,MACX,MAAM,IAAI0N,MAAM/N,EAASK,OAG3B,IAAKL,EAAS6H,KACZ,MAAM,IAAIkG,MAAM,6BAKlB,OAD4BG,EAAsBlO,EAAS6H,MAChCA,IAC7B,CAAE,MAAOxH,GAAa,IAADoP,EAEnB,GAAkB,QAAlBA,EAAIpP,EAAML,gBAAQ,IAAAyP,GAAdA,EAAgB5H,KAAM,CACxB,MAAMsH,EAAeb,EAAoBjO,EAAML,SAAS6H,MACxD,MAAM,IAAIkG,MAAMoB,EAClB,CACA,MAAM7O,EAAAA,EAAAA,IAAeD,EACvB,GASFqP,WAAY3P,MAAOT,EAAY+J,KAC7B,IAEE,MAAMkG,EAAcpC,EAA2B9D,GACzCrJ,QAAiBC,EAAAA,EAAU0P,IAAI/C,EAAgBE,QAAQxN,GAAKiQ,GAGlE,GAAIvP,EAASK,MACX,MAAM,IAAI0N,MAAM/N,EAASK,OAG3B,IAAKL,EAAS6H,KACZ,MAAM,IAAIkG,MAAM,6BAKlB,OAD4BG,EAAsBlO,EAAS6H,MAChCA,IAC7B,CAAE,MAAOxH,GAAa,IAADuP,EAEnB,GAAkB,QAAlBA,EAAIvP,EAAML,gBAAQ,IAAA4P,GAAdA,EAAgB/H,KAAM,CACxB,MAAMsH,EAAeb,EAAoBjO,EAAML,SAAS6H,MACxD,MAAM,IAAIkG,MAAMoB,EAClB,CACA,MAAM7O,EAAAA,EAAAA,IAAeD,EACvB,GAQFwP,WAAY9P,UACV,IACE,MAAMC,QAAiBC,EAAAA,EAAU6P,OAAOlD,EAAgBE,QAAQxN,IAGhE,GAAIU,EAASK,MACX,MAAM,IAAI0N,MAAM/N,EAASK,OAI3B,GAAIL,EAAS6H,MAAiC,kBAAlB7H,EAAS6H,MAAqB,YAAa7H,EAAS6H,OAAS7H,EAAS6H,KAAKmG,QAAS,CAC9G,MAAMmB,EAAe,YAAanP,EAAS6H,KAAO7H,EAAS6H,KAAKoG,QAAU,0BAC1E,MAAM,IAAIF,MAAMoB,EAClB,CACF,CAAE,MAAO9O,GAAa,IAAD0P,EAEnB,GAAkB,QAAlBA,EAAI1P,EAAML,gBAAQ,IAAA+P,GAAdA,EAAgBlI,KAAM,CACxB,MAAMsH,EAAeb,EAAoBjO,EAAML,SAAS6H,MACxD,MAAM,IAAIkG,MAAMoB,EAClB,CACA,MAAM7O,EAAAA,EAAAA,IAAeD,EACvB,GASF2P,iBAAkBjQ,MAAOT,EAAY8F,KACnC,IACE,MAAM6K,EAA+B,CAAE7K,UACjCpF,QAAiBC,EAAAA,EAAU0P,IAAI/C,EAAgBG,OAAOzN,GAAK2Q,GAGjE,GAAIjQ,EAASK,MACX,MAAM,IAAI0N,MAAM/N,EAASK,OAI3B,GAAIL,EAAS6H,MAAiC,kBAAlB7H,EAAS6H,MAAqB,YAAa7H,EAAS6H,OAAS7H,EAAS6H,KAAKmG,QAAS,CAC9G,MAAMmB,EAAe,YAAanP,EAAS6H,KAAO7H,EAAS6H,KAAKoG,QAAU,uBAC1E,MAAM,IAAIF,MAAMoB,EAClB,CACF,CAAE,MAAO9O,GAAa,IAAD6P,EAEnB,GAAkB,QAAlBA,EAAI7P,EAAML,gBAAQ,IAAAkQ,GAAdA,EAAgBrI,KAAM,CACxB,MAAMsH,EAAeb,EAAoBjO,EAAML,SAAS6H,MACxD,MAAM,IAAIkG,MAAMoB,EAClB,CACA,MAAM7O,EAAAA,EAAAA,IAAeD,EACvB,GASF8P,YAAapQ,MAAOqQ,EAAezB,KACjC,IACE,MAAM0B,EAAgC,IAAK1B,EAAQG,OAAQsB,GAC3D,aAAa5B,EAASC,SAAS4B,EACjC,CAAE,MAAOhQ,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GASFiQ,eAAgBvQ,MAAOwQ,EAAgC5B,KACrD,IAEE,MAAM6B,EAA8B,IAAK7B,GAGzC,aAAaH,EAASC,SAAS+B,EACjC,CAAE,MAAOnQ,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAQFoQ,gBAAiB1Q,UACf,IACE,MAAMa,EAAW,IAAI8P,SACrB9P,EAAS+P,OAAO,QAASjN,GAEzB,MAAM1D,QAAiBC,EAAAA,EAAUuP,KAAK5C,EAAgBM,aAActM,EAAU,CAC5EgQ,QAAS,CACP,eAAgB,yBAKpB,GAAI5Q,EAASK,MACX,MAAM,IAAI0N,MAAM/N,EAASK,OAG3B,IAAKL,EAAS6H,KACZ,MAAM,IAAIkG,MAAM,6BAKlB,OAD0BD,EAA6C9N,EAAS6H,MACvDA,IAC3B,CAAE,MAAOxH,GAAa,IAADwQ,EAEnB,GAAkB,QAAlBA,EAAIxQ,EAAML,gBAAQ,IAAA6Q,GAAdA,EAAgBhJ,KAAM,CACxB,MAAMsH,EAAeb,EAAoBjO,EAAML,SAAS6H,MACxD,MAAM,IAAIkG,MAAMoB,EAClB,CACA,MAAM7O,EAAAA,EAAAA,IAAeD,EACvB,KAKS,SACXoO,GAAQ,YACRW,GAAW,WACXE,GAAU,WACVI,GAAU,WACVG,GAAU,iBACVG,GAAgB,YAChBG,GAAW,eACXG,GAAc,gBACdG,IACEjC,EAEJ,K,eClSO,MAyIP,GAzIwB,WAAuC,IAAtCnL,EAAOyN,UAAA7N,OAAA,QAAAU,IAAAmN,UAAA,GAAAA,UAAA,GAAG,CAAEC,cAAc,IAEjDhP,EAAAA,EAAAA,YAAU,KAER9B,EAAAA,EAAU+Q,YAAY,GACrB,IAIH,MAAMC,EAAa,CACjBC,OAAQnR,gBACiByO,GAAAA,YACP3G,KAElBrH,QAASgO,GAAAA,YACT2C,OAAQ3C,GAAAA,WACR4C,OAAQ5C,GAAAA,WACRsB,OAAQtB,GAAAA,YAGJ6C,GAAWC,EAAAA,EAAAA,GAAoBL,EAAY,CAC/CM,WAAY,QACZR,aAAc1N,EAAQ0N,gBAGlB,iBAAES,IAAqBC,EAAAA,GAAAA,KAGvBC,GAAsBC,EAAAA,EAAAA,QAAOH,IAGnCzP,EAAAA,EAAAA,YAAU,KACR2P,EAAoBE,QAAUJ,CAAgB,IAIhD,MAAMxB,GAAmB6B,EAAAA,EAAAA,cAAY9R,MAAOT,EAAY8F,KACtD,UACQoJ,GAAAA,iBAA0BlP,EAAI8F,GAGpC,MACM0M,EADkBT,EAASU,SACOvO,KAAIU,GAC1CA,EAAK5E,KAAOA,EAAK,IAAK4E,EAAMkB,UAAWlB,IAIzCmN,EAASW,YAAYF,GAErBJ,EAAoBE,QAAQ,CAC1B3Q,KAAM,UACNvC,MAAO,UACPuP,QAAS,QAAmB,WAAX7I,EAAsB,YAAc,yBAEzD,CAAE,MAAO/E,GAMP,MALAqR,EAAoBE,QAAQ,CAC1B3Q,KAAM,QACNvC,MAAO,QACPuP,QAAS,aAAwB,WAAX7I,EAAsB,WAAa,eAErD/E,CACR,IACC,CAACgR,IAGE3B,GAAamC,EAAAA,EAAAA,cAAY9R,MAAOT,EAAY+J,KAChD,IACE,MAAM4I,QAAoBzD,GAAAA,WAAoBlP,EAAI+J,GAI5CyI,EADkBT,EAASU,SACOvO,KAAIU,GAC1CA,EAAK5E,KAAOA,EAAK2S,EAAc/N,IAYjC,OARAmN,EAASW,YAAYF,GAErBJ,EAAoBE,QAAQ,CAC1B3Q,KAAM,UACNvC,MAAO,UACPuP,QAAS,8BAGJgE,CACT,CAAE,MAAO5R,GAMP,MALAqR,EAAoBE,QAAQ,CAC1B3Q,KAAM,QACNvC,MAAO,QACPuP,QAAS,0BAEL5N,CACR,IACC,CAACgR,IAGElB,GAAc0B,EAAAA,EAAAA,cAAY9R,MAAOqQ,EAAezB,KACpD,IACE,aAAaH,GAAAA,YAAqB4B,EAAOzB,EAC3C,CAAE,MAAOtO,GAMP,MALAqR,EAAoBE,QAAQ,CAC1B3Q,KAAM,QACNvC,MAAO,QACPuP,QAAS,2BAEL5N,CACR,IACC,IAGG6R,GAAyBL,EAAAA,EAAAA,cAAY9R,UACzC,IACE,aAAayO,GAAAA,SAAkBG,EACjC,CAAE,MAAOtO,GAMP,MALAqR,EAAoBE,QAAQ,CAC1B3Q,KAAM,QACNvC,MAAO,QACPuP,QAAS,0BAEL5N,CACR,IACC,IAEH,MAAO,IACFgR,EACH3K,MAAO2K,EAASU,SAChBI,WAAYd,EAASe,cACrBhD,YAAaiC,EAASgB,cACtBC,aAAcjB,EAASiB,aACvBC,aAAclB,EAASkB,aACvBvC,mBACAN,aACAS,cACA+B,yBAEJ,E,8DCpIA,MA+FA,EA/F4C9T,IAarC,IAbsC,MAC3CsB,EAAK,KACLqB,EAAI,KACJE,EAAO,OAAM,MACbtB,EAAK,SACLyD,EAAQ,MACR/C,EAAK,SACLuC,GAAW,EAAK,YAChBU,EAAc,GAAE,QAChBD,EAAU,GAAE,UACZ/E,EAAY,GAAE,SACd2F,GAAW,EAAK,QAChBV,GAAU,GACXnF,EACC,MAAMoU,EAAe,sDACnBnS,EAAQ,yDAA2D,2DAqErE,OACET,EAAAA,EAAAA,MAAA,OAAKtB,UAAW,GAAGA,IAAYD,SAAA,EAC7BuB,EAAAA,EAAAA,MAAA,SAAOwE,QAASrD,EAAMzC,UAAU,0CAAyCD,SAAA,CACtEqB,EAAM,IAAEkD,IAAYrE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,eAAcD,SAAC,SArEtCoU,MAClB,OAAQxR,GACN,IAAK,WACH,OACE1C,EAAAA,EAAAA,KAAA,YACEe,GAAIyB,EACJA,KAAMA,EACNpB,MAAOA,EACPyD,SAAUA,EACV9E,UAAWkU,EACXlP,YAAaA,EACbW,SAAUA,IAIhB,IAAK,SACH,OACE1F,EAAAA,EAAAA,KAAA,UACEe,GAAIyB,EACJA,KAAMA,EACNpB,MAAOA,EACPyD,SAAUA,EACV9E,UAAWkU,EACXvO,SAAUA,GAAYV,EAAQlF,SAE7BkF,GACChF,EAAAA,EAAAA,KAAA,UAAQoB,MAAM,GAAEtB,SAAC,eAEjBgF,EAAQG,KAAIkP,IACVnU,EAAAA,EAAAA,KAAA,UAA2BoB,MAAO+S,EAAO/S,MAAMtB,SAC5CqU,EAAOhT,OADGgT,EAAO/S,WAQ9B,IAAK,WACH,OACEpB,EAAAA,EAAAA,KAAA,SACE0C,KAAK,WACL3B,GAAIyB,EACJA,KAAMA,EACNuB,QAAS3C,EACTyD,SAAUA,EACV9E,UAAU,kEACV2F,SAAUA,IAIhB,QACE,OACE1F,EAAAA,EAAAA,KAAA,SACE0C,KAAMA,EACN3B,GAAIyB,EACJA,KAAMA,EACNpB,MAAOA,EACPyD,SAAUA,EACV9E,UAAWkU,EACXlP,YAAaA,EACbW,SAAUA,IAGlB,EAQGwO,GACApS,IAAS9B,EAAAA,EAAAA,KAAA,KAAGD,UAAU,4BAA2BD,SAAEgC,MAChD,C,uDClGV,MAqBA,EArBoDjC,IAK7C,IAL8C,MACnDM,EAAK,YACL+F,EAAW,SACXpG,EAAQ,UACRC,EAAY,IACbF,EACC,OACEwB,EAAAA,EAAAA,MAAA,OAAKtB,UAAW,iDAAiDA,IAAYD,SAAA,EAC3EuB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAAA,MAAID,UAAU,8CAA6CD,SAAEK,IAC5D+F,IACClG,EAAAA,EAAAA,KAAA,KAAGD,UAAU,uCAAsCD,SAAEoG,QAGzDlG,EAAAA,EAAAA,KAAA,OAAKD,UAAU,2BAA0BD,SACtCA,MAEC,C,uFCJV,MA6JA,EA7JgDD,IAWzC,IAX0C,MAC/CsB,EAAK,KACLqB,EAAI,MACJpB,EAAK,SACLyD,EAAQ,MACR/C,EAAK,SACLuC,GAAW,EAAK,SAChBqB,GAAW,EAAK,QAChBL,EAAU,QAAe,aACzBC,EAAe,CAAC,aAAc,YAAa,YAAa,cAAa,UACrEvF,EAAY,IACbF,EACC,MAAOuU,EAAYC,IAAiB9R,EAAAA,EAAAA,WAAS,IACtC+R,EAASC,IAAchS,EAAAA,EAAAA,UAAwB,MAChDiS,GAAepB,EAAAA,EAAAA,QAAyB,MAG9C9S,EAAAA,WAAgB,KACd,GAAIc,aAAiBqT,KAAM,CACzB,MAAMC,EAAMC,IAAIC,gBAAgBxT,GAEhC,OADAmT,EAAWG,GACJ,IAAMC,IAAIE,gBAAgBH,EACnC,CAAO,MAAqB,kBAAVtT,GAAsBA,OACtCmT,EAAWnT,QAGXmT,EAAW,KAEb,GACC,CAACnT,IAEJ,MAAM0T,GAAmBxB,EAAAA,EAAAA,cAAanO,IACpC,MAAM4P,GAAaC,EAAAA,EAAAA,IAAa7P,EAAM,CACpCE,UACAC,iBAGEyP,EAAWE,MACbpQ,EAASM,GAGTzB,QAAQ5B,MAAM,0BAA2BiT,EAAWjT,MACtD,GACC,CAACuD,EAASC,EAAcT,IA8C3B,OACExD,EAAAA,EAAAA,MAAA,OAAKtB,UAAWA,EAAUD,SAAA,EACxBuB,EAAAA,EAAAA,MAAA,SAAOtB,UAAU,+CAA8CD,SAAA,CAC5DqB,EAAM,IAAEkD,IAAYrE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,eAAcD,SAAC,UAGtDuB,EAAAA,EAAAA,MAAA,OACEtB,UAAW,sHAEPqU,EAAa,yCAA2C,gCACxDtS,EAAQ,iBAAmB,iBAC3B4D,EAAW,gCAAkC,oDAEjDwP,WAlDkBrR,IACtBA,EAAEI,iBACGyB,GACH2O,GAAc,EAChB,EA+CIc,YA5CmBtR,IACvBA,EAAEI,iBACFoQ,GAAc,EAAM,EA2ChBe,OAxCcvR,IAAwB,IAADwR,EAIzC,GAHAxR,EAAEI,iBACFoQ,GAAc,GAEV3O,EAAU,OAEd,MAAMP,EAA2B,QAAvBkQ,EAAGxR,EAAEyR,aAAaC,aAAK,IAAAF,OAAA,EAApBA,EAAuB,GAChClQ,GACF2P,EAAiB3P,EACnB,EAgCIM,QAtBc+P,MACb9P,GAAY8O,EAAanB,SAC5BmB,EAAanB,QAAQoC,OACvB,EAmByB3V,SAAA,EAErBE,EAAAA,EAAAA,KAAA,SACEc,IAAK0T,EACL9R,KAAK,OACLF,KAAMA,EACNkT,OAAQpQ,EAAaqQ,KAAK,KAC1B9Q,SAnEuBhB,IAA4C,IAAD+R,EACxE,MAAMzQ,EAAqB,QAAjByQ,EAAG/R,EAAEC,OAAOyR,aAAK,IAAAK,OAAA,EAAdA,EAAiB,GAC1BzQ,GACF2P,EAAiB3P,EACnB,EAgEMpF,UAAU,SACV2F,SAAUA,IAGX4O,GACCjT,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,WAAUD,SAAA,EACvBE,EAAAA,EAAAA,KAAA,OACE4H,IAAK0M,EACLzM,IAAI,UACJ9H,UAAU,+CAEV2F,IACA1F,EAAAA,EAAAA,KAAA,UACE0C,KAAK,SACL+C,QAAU5B,IACRA,EAAEoF,kBAnDhBpE,EAAS,MACL2P,EAAanB,UACfmB,EAAanB,QAAQjS,MAAQ,GAkDH,EAEhBrB,UAAU,qGAAoGD,UAE9GE,EAAAA,EAAAA,KAAC6V,EAAAA,EAAS,CAAC9V,UAAU,kBAK3BsB,EAAAA,EAAAA,MAAA,OAAAvB,SAAA,EACEE,EAAAA,EAAAA,KAACC,EAAAA,EAAS,CAACF,UAAU,qCACrBsB,EAAAA,EAAAA,MAAA,OAAKtB,UAAU,OAAMD,SAAA,EACnBE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,wBAAuBD,SACjCsU,EAAa,kBAAoB,sCAEpC/S,EAAAA,EAAAA,MAAA,KAAGtB,UAAU,6BAA4BD,SAAA,CAAC,uBACnBoO,KAAK4H,MAAMzQ,EAAU,KAAO,MAAM,iBAOhEvD,IAAS9B,EAAAA,EAAAA,KAAA,KAAGD,UAAU,4BAA2BD,SAAEgC,MAChD,C,mECzJViU,EAAAA,GAAQC,SACNC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,IAIK,MAAMpJ,EAAgD,CAC3DqJ,YAAY,EACZC,qBAAqB,EACrBrJ,QAAS,CACPrN,MAAO,CACLsN,SAAS,EACTqJ,MAAO,UACPC,KAAM,CACJjQ,KAAM,GACNkQ,OAAQ,SAGZtJ,OAAQ,CACND,SAAS,EACTxB,OAAQ,CACN6K,MAAO,YAGXG,QAAS,CACP7K,gBAAiB,UACjB8K,WAAY,UACZC,UAAW,UACXC,QAAS,GACTC,eAAe,IAGnB1J,OAAQ,CACN2J,EAAG,CACDC,KAAM,CACJ9J,SAAS,GAEXI,MAAO,CACLiJ,MAAO,YAGXlJ,EAAG,CACD4J,aAAa,EACbD,KAAM,CACJ9J,SAAS,EACTqJ,MAAO,WAETjJ,MAAO,CACLiJ,MAAO,cAMFW,EAA8C,CACzDb,YAAY,EACZC,qBAAqB,EACrBrJ,QAAS,CACPrN,MAAO,CACLsN,SAAS,EACTqJ,MAAO,UACPC,KAAM,CACJjQ,KAAM,GACNkQ,OAAQ,SAGZtJ,OAAQ,CACNgK,SAAU,SACVzL,OAAQ,CACN6K,MAAO,UACPC,KAAM,CACJjQ,KAAM,MAIZmQ,QAAS,CACP7K,gBAAiB,UACjB8K,WAAY,UACZC,UAAW,UACXC,QAAS,MAsDFO,EAAgBC,IACvBA,GACFA,EAAcC,SAChB,C,gDCvKF,SAAS9O,EAAYlJ,EAIlBK,GAAQ,IAJW,MACpBC,EAAK,QACLC,KACGC,GACJR,EACC,OAAoBS,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKZ,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBG,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHD,GAAS,KAAmBG,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,mQAEP,CACA,MACA,EADiCZ,EAAAA,WAAiByI,E", "sources": ["components/common/DetailList.tsx", "../node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js", "components/common/DetailItem.tsx", "features/users/api/businessTypesApi.ts", "features/users/components/AddUserForm.tsx", "features/users/components/EditUserForm.tsx", "components/common/OrdersSection.tsx", "features/users/components/UserDetails.tsx", "features/users/components/UserDetailsModal.tsx", "features/users/components/UserList.tsx", "components/common/MetricCard.tsx", "components/common/MetricsSection.tsx", "features/users/components/UserAnalytics.tsx", "constants/endpoints.ts", "features/users/utils/apiTransformers.ts", "features/users/api/usersApi.ts", "features/users/hooks/useUsers.ts", "components/common/FormField.tsx", "components/common/DetailSection.tsx", "components/common/ImageUpload.tsx", "utils/chartConfig.ts", "../node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js"], "sourcesContent": ["import React from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\ninterface DetailListProps {\r\n  children: ReactNode;\r\n  className?: string;\r\n}\r\n\r\nconst DetailList: React.FC<DetailListProps> = ({\r\n  children,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <dl className={`sm:divide-y sm:divide-gray-200 ${className}`}>\r\n      {children}\r\n    </dl>\r\n  );\r\n};\r\n\r\nexport default DetailList;", "import * as React from \"react\";\nfunction PhotoIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PhotoIcon);\nexport default ForwardRef;", "import React from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\ninterface DetailItemProps {\r\n  label: string;\r\n  value: ReactNode;\r\n  className?: string;\r\n}\r\n\r\nconst DetailItem: React.FC<DetailItemProps> = ({\r\n  label,\r\n  value,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <div className={`py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 ${className}`}>\r\n      <dt className=\"text-sm font-medium text-gray-500\">{label}</dt>\r\n      <dd className=\"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2\">{value}</dd>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DetailItem;", "/**\r\n * Business Types API Service\r\n * \r\n * This file provides methods for interacting with business types API endpoints.\r\n */\r\n\r\nimport apiClient from '../../../api';\r\nimport { handleApiError } from '../../../utils/errorHandling';\r\nimport { responseValidators } from '../../../utils/apiHelpers';\r\nimport type { BusinessType } from '../types';\r\n\r\nexport const businessTypesApi = {\r\n  /**\r\n   * Get all business types\r\n   * @returns Promise resolving to an array of business types\r\n   */\r\n  getBusinessTypes: async (): Promise<BusinessType[]> => {\r\n    try {\r\n      const response = await apiClient.get<BusinessType[]>('/business-types');\r\n      return responseValidators.getList(response, 'business types');\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get a business type by ID\r\n   * @param id - The business type ID\r\n   * @returns Promise resolving to a business type\r\n   */\r\n  getBusinessTypeById: async (id: string): Promise<BusinessType> => {\r\n    try {\r\n      const response = await apiClient.get<BusinessType>(`/business-types/${id}`);\r\n      return responseValidators.getById(response, 'business type', id);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  }\r\n};\r\n\r\n// Export individual methods for more flexible importing\r\nexport const { \r\n  getBusinessTypes, \r\n  getBusinessTypeById \r\n} = businessTypesApi;\r\n\r\nexport default businessTypesApi;\r\n", "/**\r\n * Add User Form Component\r\n *\r\n * This component provides a form for adding new users.\r\n */\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Button from '../../../components/common/Button';\r\nimport FormField from '../../../components/common/FormField';\r\nimport ImageUpload from '../../../components/common/ImageUpload';\r\nimport { validateForm, validationRules } from '../../../utils/validation';\r\nimport { getBusinessTypes } from '../api/businessTypesApi';\r\nimport type { UserFormDataFrontend, BusinessType } from '../types';\r\n\r\ninterface AddUserFormProps {\r\n  onSubmit: (userData: UserFormDataFrontend & { confirmPassword: string; sendInvite: boolean }) => void;\r\n  onCancel: () => void;\r\n  isLoading?: boolean;\r\n}\r\n\r\nconst AddUserForm: React.FC<AddUserFormProps> = ({ onSubmit, onCancel, isLoading = false }) => {\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    email: '',\r\n    type: 'customer' as const,\r\n    phone: '',\r\n    address: '',\r\n    businessType: '',\r\n    password: '',\r\n    confirmPassword: '',\r\n    sendInvite: true,\r\n    image: null as File | null\r\n  });\r\n\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n  const [businessTypes, setBusinessTypes] = useState<BusinessType[]>([]);\r\n  const [loadingBusinessTypes, setLoadingBusinessTypes] = useState(false);\r\n\r\n  // Load business types on component mount\r\n  useEffect(() => {\r\n    const loadBusinessTypes = async () => {\r\n      setLoadingBusinessTypes(true);\r\n      try {\r\n        const types = await getBusinessTypes();\r\n        setBusinessTypes(types);\r\n      } catch (error) {\r\n        console.error('Failed to load business types:', error);\r\n        // You might want to show a notification here\r\n      } finally {\r\n        setLoadingBusinessTypes(false);\r\n      }\r\n    };\r\n\r\n    loadBusinessTypes();\r\n  }, []);\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n    const { name, value, type } = e.target;\r\n\r\n    if (type === 'checkbox') {\r\n      const checked = (e.target as HTMLInputElement).checked;\r\n      setFormData(prev => ({ ...prev, [name]: checked }));\r\n    } else {\r\n      setFormData(prev => ({ ...prev, [name]: value }));\r\n    }\r\n\r\n    // Clear error when field is edited\r\n    if (errors[name]) {\r\n      setErrors(prev => ({ ...prev, [name]: '' }));\r\n    }\r\n  };\r\n\r\n  const handleImageChange = (file: File | null) => {\r\n    setFormData(prev => ({ ...prev, image: file }));\r\n\r\n    // Clear error when image is changed\r\n    if (errors.image) {\r\n      setErrors(prev => ({ ...prev, image: '' }));\r\n    }\r\n  };\r\n\r\n  const validateUserForm = () => {\r\n    const formValidationRules = {\r\n      name: [validationRules.required('Name is required')],\r\n      email: [validationRules.required('Email is required'), validationRules.email()],\r\n      type: [validationRules.required('User type is required')],\r\n      password: [validationRules.required('Password is required'), validationRules.password()],\r\n      confirmPassword: [validationRules.required('Confirm password is required'), validationRules.passwordMatch()],\r\n      address: [validationRules.required('Address is required')],\r\n      businessType: [validationRules.required('Business type is required')],\r\n    };\r\n\r\n    const newErrors = validateForm(formData, formValidationRules);\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (validateUserForm()) {\r\n      const submitData = {\r\n        ...formData,\r\n        // Include image field, even if null\r\n        image: formData.image\r\n      };\r\n      onSubmit(submitData);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n      <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\r\n        <FormField\r\n          label=\"Full Name\"\r\n          name=\"name\"\r\n          value={formData.name}\r\n          onChange={handleChange}\r\n          error={errors.name}\r\n          required\r\n        />\r\n        \r\n        <FormField\r\n          label=\"Email\"\r\n          name=\"email\"\r\n          type=\"email\"\r\n          value={formData.email}\r\n          onChange={handleChange}\r\n          error={errors.email}\r\n          required\r\n        />\r\n        \r\n        <FormField\r\n          label=\"User Type\"\r\n          name=\"type\"\r\n          type=\"select\"\r\n          value={formData.type}\r\n          onChange={handleChange}\r\n          error={errors.type}\r\n          required\r\n          options={[\r\n            { value: 'customer', label: 'Customer' },\r\n            { value: 'supplier', label: 'Supplier' }\r\n          ]}\r\n        />\r\n        \r\n        <FormField\r\n          label=\"Phone Number\"\r\n          name=\"phone\"\r\n          value={formData.phone}\r\n          onChange={handleChange}\r\n          error={errors.phone}\r\n        />\r\n\r\n        <FormField\r\n          label=\"Address\"\r\n          name=\"address\"\r\n          type=\"textarea\"\r\n          value={formData.address}\r\n          onChange={handleChange}\r\n          error={errors.address}\r\n          required\r\n          placeholder=\"Enter full address\"\r\n        />\r\n\r\n        <FormField\r\n          label=\"Business Type\"\r\n          name=\"businessType\"\r\n          type=\"select\"\r\n          value={formData.businessType}\r\n          onChange={handleChange}\r\n          error={errors.businessType}\r\n          required\r\n          loading={loadingBusinessTypes}\r\n          options={[\r\n            { value: '', label: 'Select Business Type' },\r\n            ...businessTypes.map(type => ({\r\n              value: type.id,\r\n              label: type.name\r\n            }))\r\n          ]}\r\n        />\r\n        \r\n        <FormField\r\n          label=\"Password\"\r\n          name=\"password\"\r\n          type=\"password\"\r\n          value={formData.password}\r\n          onChange={handleChange}\r\n          error={errors.password}\r\n          required\r\n        />\r\n        \r\n        <FormField\r\n          label=\"Confirm Password\"\r\n          name=\"confirmPassword\"\r\n          type=\"password\"\r\n          value={formData.confirmPassword}\r\n          onChange={handleChange}\r\n          error={errors.confirmPassword}\r\n          required\r\n        />\r\n      </div>\r\n\r\n      {/* Image Upload Field */}\r\n      <ImageUpload\r\n        label=\"Profile Picture\"\r\n        name=\"image\"\r\n        value={formData.image}\r\n        onChange={handleImageChange}\r\n        error={errors.image || undefined}\r\n        maxSize={5 * 1024 * 1024} // 5MB\r\n        allowedTypes={['image/jpeg', 'image/png', 'image/gif', 'image/webp']}\r\n      />\r\n\r\n      <div className=\"flex items-center\">\r\n        <FormField\r\n          label=\"Send invitation email\"\r\n          name=\"sendInvite\"\r\n          type=\"checkbox\"\r\n          value={formData.sendInvite}\r\n          onChange={handleChange}\r\n          className=\"flex items-center space-x-2\"\r\n        />\r\n      </div>\r\n      \r\n      <div className=\"flex justify-end space-x-3\">\r\n        <Button\r\n          variant=\"outline\"\r\n          onClick={onCancel}\r\n          disabled={isLoading}\r\n        >\r\n          Cancel\r\n        </Button>\r\n        <Button\r\n          type=\"submit\"\r\n          loading={isLoading}\r\n        >\r\n          Add User\r\n        </Button>\r\n      </div>\r\n    </form>\r\n  );\r\n};\r\n\r\nexport default AddUserForm;\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n", "/**\r\n * Edit User Form Component\r\n *\r\n * This component provides a form for editing existing users.\r\n */\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Button from '../../../components/common/Button';\r\nimport Card from '../../../components/common/Card';\r\nimport { validateForm, validationRules } from '../../../utils/validation';\r\nimport type { User, UserFormDataFrontend } from '../types/index';\r\n\r\ninterface EditUserFormProps {\r\n  user: User;\r\n  onSubmit: (userData: UserFormDataFrontend) => Promise<void>;\r\n  isLoading?: boolean;\r\n}\r\n\r\nconst EditUserForm: React.FC<EditUserFormProps> = ({ \r\n  user, \r\n  onSubmit, \r\n  isLoading = false \r\n}) => {\r\n  const [formData, setFormData] = useState<UserFormDataFrontend>({\r\n    name: '',\r\n    email: '',\r\n    type: 'customer',\r\n  });\r\n\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n\r\n  // Initialize form with user data\r\n  useEffect(() => {\r\n    if (user) {\r\n      setFormData({\r\n        name: user.name,\r\n        email: user.email,\r\n        type: user.type,\r\n      });\r\n    }\r\n  }, [user]);\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\r\n    const { name, value, type } = e.target;\r\n\r\n    if (type === 'checkbox') {\r\n      const checked = (e.target as HTMLInputElement).checked;\r\n      setFormData(prev => ({ ...prev, [name]: checked }));\r\n    } else {\r\n      setFormData(prev => ({ ...prev, [name]: value }));\r\n    }\r\n\r\n    // Clear error when field is edited\r\n    if (errors[name]) {\r\n      setErrors(prev => ({ ...prev, [name]: '' }));\r\n    }\r\n  };\r\n\r\n  const validateUserForm = () => {\r\n    const formValidationRules = {\r\n      name: [validationRules.required('Name is required')],\r\n      email: [validationRules.required('Email is required'), validationRules.email()],\r\n      type: [validationRules.required('User type is required')]\r\n    };\r\n    \r\n    const newErrors = validateForm(formData, formValidationRules);\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (validateUserForm()) {\r\n      try {\r\n        await onSubmit(formData);\r\n        // Success notification is handled in the parent component\r\n      } catch (error) {\r\n        // Error handling is done in the parent component\r\n        console.error('Form submission error:', error);\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card>\r\n      <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\r\n        <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\r\n          <div>\r\n            <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">\r\n              Full Name <span className=\"text-red-500\">*</span>\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"name\"\r\n              name=\"name\"\r\n              value={formData.name}\r\n              onChange={handleChange}\r\n              className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm ${\r\n                errors['name'] ? 'border-red-300' : ''\r\n              }`}\r\n            />\r\n            {errors['name'] && <p className=\"mt-1 text-sm text-red-600\">{errors['name']}</p>}\r\n          </div>\r\n          \r\n          <div>\r\n            <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\r\n              Email <span className=\"text-red-500\">*</span>\r\n            </label>\r\n            <input\r\n              type=\"email\"\r\n              id=\"email\"\r\n              name=\"email\"\r\n              value={formData.email}\r\n              onChange={handleChange}\r\n              className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm ${\r\n                errors['email'] ? 'border-red-300' : ''\r\n              }`}\r\n            />\r\n            {errors['email'] && <p className=\"mt-1 text-sm text-red-600\">{errors['email']}</p>}\r\n          </div>\r\n          \r\n          <div>\r\n            <label htmlFor=\"type\" className=\"block text-sm font-medium text-gray-700\">\r\n              User Type <span className=\"text-red-500\">*</span>\r\n            </label>\r\n            <select\r\n              id=\"type\"\r\n              name=\"type\"\r\n              value={formData.type}\r\n              onChange={handleChange}\r\n              className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm ${\r\n                errors['type'] ? 'border-red-300' : ''\r\n              }`}\r\n            >\r\n              <option value=\"customer\">Customer</option>\r\n              <option value=\"supplier\">Supplier</option>\r\n            </select>\r\n            {errors['type'] && <p className=\"mt-1 text-sm text-red-600\">{errors['type']}</p>}\r\n          </div>\r\n          \r\n          <div>\r\n            <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\r\n              New Password\r\n            </label>\r\n            <input\r\n              type=\"password\"\r\n              id=\"password\"\r\n              name=\"password\"\r\n              value={formData.password || ''}\r\n              onChange={handleChange}\r\n              placeholder=\"Leave blank to keep current password\"\r\n              className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm ${\r\n                errors['password'] ? 'border-red-300' : ''\r\n              }`}\r\n            />\r\n            {errors['password'] && <p className=\"mt-1 text-sm text-red-600\">{errors['password']}</p>}\r\n          </div>\r\n        </div>\r\n        \r\n        <div className=\"flex justify-end space-x-3\">\r\n          <Button\r\n            variant=\"outline\"\r\n            type=\"button\"\r\n            onClick={() => window.history.back()}\r\n            disabled={isLoading}\r\n          >\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            type=\"submit\"\r\n            loading={isLoading}\r\n          >\r\n            Save Changes\r\n          </Button>\r\n        </div>\r\n      </form>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default EditUserForm;\r\n", "import React from 'react';\r\nimport DetailSection from './DetailSection';\r\nimport type { Order } from '../../features/orders/types';\r\nimport { formatCurrency, formatDate } from '../../utils/formatters';\r\nimport StatusBadge from './StatusBadge';\r\nimport Button from './Button';\r\nimport { EyeIcon } from '@heroicons/react/24/outline';\r\n\r\ninterface OrdersSectionProps {\r\n  orders: Order[];\r\n  title?: string;\r\n  description?: string;\r\n  onViewOrder?: (order: Order) => void;\r\n  emptyMessage?: string;\r\n  className?: string;\r\n}\r\n\r\nconst OrdersSection: React.FC<OrdersSectionProps> = ({\r\n  orders,\r\n  title = 'Orders',\r\n  description = 'Recent orders',\r\n  onViewOrder,\r\n  emptyMessage = 'No orders found',\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <DetailSection\r\n      title={title}\r\n      description={description}\r\n      className={className}\r\n    >\r\n      {orders.length === 0 ? (\r\n        <div className=\"px-4 py-5 text-center text-sm text-gray-500\">\r\n          {emptyMessage}\r\n        </div>\r\n      ) : (\r\n        <div className=\"overflow-x-auto\">\r\n          <table className=\"min-w-full divide-y divide-gray-200\">\r\n            <thead className=\"bg-gray-50\">\r\n              <tr>\r\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Order ID\r\n                </th>\r\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Date\r\n                </th>\r\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Amount\r\n                </th>\r\n                <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Status\r\n                </th>\r\n                <th scope=\"col\" className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                  Actions\r\n                </th>\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"bg-white divide-y divide-gray-200\">\r\n              {orders.map((order) => (\r\n                <tr key={order.id} className=\"hover:bg-gray-50\">\r\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-primary\">\r\n                    {order.id}\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                    {formatDate(order.orderDate)}\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                    {formatCurrency(order.totalAmount)}\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <StatusBadge status={order.status} type=\"order\" />\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\r\n                    {onViewOrder && (\r\n                      <Button\r\n                        variant=\"text\"\r\n                        size=\"sm\"\r\n                        onClick={() => onViewOrder(order)}\r\n                        icon={<EyeIcon className=\"w-4 h-4 text-black\" />}\r\n                        className=\"text-black hover:text-gray-700 hover:bg-gray-100\"\r\n                      >\r\n                        View\r\n                      </Button>\r\n                    )}\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      )}\r\n    </DetailSection>\r\n  );\r\n};\r\n\r\nexport default OrdersSection;", "/**\r\n * User Details Component\r\n *\r\n * This component displays detailed information about a user.\r\n */\r\n\r\nimport React from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport DetailSection from '../../../components/common/DetailSection';\r\nimport DetailList from '../../../components/common/DetailList';\r\nimport DetailItem from '../../../components/common/DetailItem';\r\nimport OrdersSection from '../../../components/common/OrdersSection';\r\nimport StatusBadge from '../../../components/common/StatusBadge';\r\nimport type{ User } from '../types';\r\nimport type { Order } from '../../orders/types';\r\nimport { ROUTES } from '../../../constants/routes';\r\n\r\ninterface UserDetailsProps {\r\n  user: User;\r\n  userOrders: Order[];\r\n}\r\n\r\nconst UserDetails: React.FC<UserDetailsProps> = ({ user, userOrders = [] }) => {\r\n  const navigate = useNavigate();\r\n\r\n  const handleViewOrder = (order: Order) => {\r\n    navigate(ROUTES.getOrderDetailsRoute(order.id));\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <DetailSection\r\n        title=\"User Information\"\r\n        description=\"Personal details and application\"\r\n      >\r\n        <DetailList>\r\n          <DetailItem label=\"Full name\" value={user.name} />\r\n          <DetailItem label=\"Email address\" value={user.email} />\r\n          <DetailItem label=\"User type\" value={user.type} />\r\n          <DetailItem label=\"Status\" value={<StatusBadge status={user.status} type=\"user\" />} />\r\n          <DetailItem label=\"Last login\" value={user.lastLogin} />\r\n        </DetailList>\r\n      </DetailSection>\r\n\r\n      <OrdersSection\r\n        orders={userOrders}\r\n        title=\"User Orders\"\r\n        description=\"Orders placed by this user\"\r\n        onViewOrder={handleViewOrder}\r\n        emptyMessage=\"This user has not placed any orders yet\"\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UserDetails;", "/**\r\n * User Details Modal Component\r\n *\r\n * This component displays detailed information about a user in a modal format.\r\n * It's specifically designed for the modal popup and matches the Supplier Details modal design.\r\n */\r\n\r\nimport React from 'react';\r\nimport Avatar from '../../../components/common/Avatar';\r\nimport type{ User } from '../types';\r\nimport {\r\n  CheckCircleIcon,\r\n  XCircleIcon\r\n} from '@heroicons/react/24/outline';\r\n\r\ninterface UserDetailsModalProps {\r\n  user: User;\r\n}\r\n\r\nconst UserDetailsModal: React.FC<UserDetailsModalProps> = ({ user }) => {\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-4\">\r\n          <Avatar\r\n            {...(user.avatar && { src: user.avatar })}\r\n            alt={user.name}\r\n            name={user.name}\r\n            size=\"xl\"\r\n          />\r\n          <div>\r\n            <h3 className=\"text-lg font-medium text-gray-900\">{user.name}</h3>\r\n            <p className=\"text-sm text-gray-500\">ID: {user.id}</p>\r\n            <div className=\"mt-1\">\r\n              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\r\n                user.status === 'active'\r\n                  ? 'bg-green-100 text-green-800'\r\n                  : 'bg-red-100 text-red-800'\r\n              }`}>\r\n                {user.status.charAt(0).toUpperCase() + user.status.slice(1)}\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 border-t border-gray-200 pt-4\">\r\n        <div>\r\n          <h4 className=\"text-sm font-medium text-gray-500 mb-3\">Contact Information</h4>\r\n          <dl className=\"space-y-3\">\r\n            <div>\r\n              <dt className=\"text-xs text-gray-500\">Email</dt>\r\n              <dd className=\"text-sm text-gray-900\">{user.email}</dd>\r\n            </div>\r\n            {user.phone && (\r\n              <div>\r\n                <dt className=\"text-xs text-gray-500\">Phone</dt>\r\n                <dd className=\"text-sm text-gray-900\">{user.phone}</dd>\r\n              </div>\r\n            )}\r\n            <div>\r\n              <dt className=\"text-xs text-gray-500\">Last Login</dt>\r\n              <dd className=\"text-sm text-gray-900\">{user.lastLogin}</dd>\r\n            </div>\r\n          </dl>\r\n        </div>\r\n\r\n        {user.address && (\r\n          <div>\r\n            <h4 className=\"text-sm font-medium text-gray-500 mb-3\">Address</h4>\r\n            <address className=\"not-italic text-sm text-gray-900\">\r\n              {user.address}\r\n            </address>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {user.businessType && (\r\n        <div className=\"border-t border-gray-200 pt-4\">\r\n          <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Business Type</h4>\r\n          <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\r\n            {user.businessType}\r\n          </span>\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"border-t border-gray-200 pt-4\">\r\n        <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Account Status</h4>\r\n        <div className=\"flex items-center space-x-2\">\r\n          {user.status === 'active' ? (\r\n            <CheckCircleIcon className=\"w-5 h-5 text-green-500\" />\r\n          ) : (\r\n            <XCircleIcon className=\"w-5 h-5 text-red-500\" />\r\n          )}\r\n          <span className=\"text-sm text-gray-700\">\r\n            {user.status === 'active'\r\n              ? `Active since ${user.lastLogin}`\r\n              : 'Account is banned'}\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UserDetailsModal;\r\n", "/**\r\n * User List Component\r\n *\r\n * This component displays a list of users in a data table.\r\n */\r\n\r\nimport React from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport BaseEntityList from '../../../components/common/EntityList/BaseEntityList';\r\nimport type { Column } from '../../../components/common/DataTable';\r\nimport type { User } from '../types';\r\nimport {\r\n  EnvelopeIcon,\r\n  PencilIcon,\r\n  TrashIcon,\r\n  EyeIcon\r\n} from '@heroicons/react/24/outline';\r\nimport { ROUTES } from '../../../constants/routes';\r\n\r\ninterface UserListProps {\r\n  users: User[];\r\n  onViewUser: (user: User) => void;\r\n  onEditUser: (user: User) => void;\r\n  onDeleteUser: (user: User) => void;\r\n  onUserClick: (user: User) => void;\r\n  title?: string;\r\n  loading?: boolean;\r\n}\r\n\r\nconst UserList: React.FC<UserListProps> = ({\r\n  users,\r\n  onViewUser,\r\n  onEditUser: _onEditUser,\r\n  onDeleteUser,\r\n  onUserClick: _onUserClick, // Keep for interface compatibility but use navigation instead\r\n  title = 'Users',\r\n  loading = false\r\n}) => {\r\n  const navigate = useNavigate();\r\n\r\n  // Handle row click to navigate to user edit page (which serves as details page)\r\n  const handleRowClick = (user: User) => {\r\n    navigate(ROUTES.getUserEditRoute(user.id));\r\n  };\r\n  \r\n  // Define user-specific columns\r\n  const columns: Column<User>[] = [\r\n    {\r\n      key: 'name',\r\n      label: 'Name',\r\n      sortable: true,\r\n      render: (_value, user) => (\r\n        <div className=\"flex items-center\">\r\n          {user.avatar ? (\r\n            <img\r\n              src={user.avatar}\r\n              alt={user.name}\r\n              className=\"w-8 h-8 rounded-full mr-3 object-cover\"\r\n            />\r\n          ) : (\r\n            <div className=\"w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center mr-3\">\r\n              {user.name.charAt(0)}\r\n            </div>\r\n          )}\r\n          <div>\r\n            <div className=\"font-medium text-gray-900\">{user.name}</div>\r\n            <div className=\"text-xs text-gray-500\">ID: {user.id}</div>\r\n          </div>\r\n        </div>\r\n      )\r\n    },\r\n    {\r\n      key: 'email',\r\n      label: 'Email',\r\n      sortable: true,\r\n      render: (value) => (\r\n        <div className=\"flex items-center\">\r\n          <EnvelopeIcon className=\"w-4 h-4 text-gray-400 mr-2\" />\r\n          <span>{value}</span>\r\n        </div>\r\n      )\r\n    },\r\n    { key: 'type', label: 'Type', sortable: true },\r\n    { key: 'status', label: 'Status', sortable: true },\r\n    { key: 'lastLogin', label: 'Last Login', sortable: true },\r\n    {\r\n      key: 'actions',\r\n      label: 'Actions',\r\n      render: (_, user) => (\r\n        <div className=\"flex items-center space-x-2\">\r\n          <button\r\n            className=\"p-1 text-gray-500 hover:text-primary rounded-full hover:bg-gray-100\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              onViewUser(user);\r\n            }}\r\n          >\r\n            <EyeIcon className=\"w-5 h-5\" />\r\n          </button>\r\n          <button\r\n            className=\"p-1 text-gray-500 hover:text-blue-600 rounded-full hover:bg-gray-100\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              navigate(ROUTES.getUserEditRoute(user.id));\r\n            }}\r\n          >\r\n            <PencilIcon className=\"w-5 h-5\" />\r\n          </button>\r\n          <button\r\n            className=\"p-1 text-gray-500 hover:text-red-600 rounded-full hover:bg-gray-100\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              onDeleteUser(user);\r\n            }}\r\n          >\r\n            <TrashIcon className=\"w-5 h-5\" />\r\n          </button>\r\n        </div>\r\n      )\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <BaseEntityList<User>\r\n      data={users}\r\n      columns={columns}\r\n      onRowClick={handleRowClick}\r\n      title={title}\r\n      pagination={true}\r\n      loading={loading}\r\n      emptyMessage=\"No users found\"\r\n    />\r\n  );\r\n};\r\n\r\nexport default UserList;\r\n\r\n\r\n\r\n", "/**\n * Metric Card Component\n *\n * A simple metric card component for displaying key metrics with icons.\n * This is a replacement for the analytics MetricCard to avoid dependencies.\n */\n\nimport React from 'react';\nimport Card from './Card';\n\ninterface MetricData {\n  total: number;\n  growth?: number;\n}\n\ninterface MetricCardProps {\n  title: string;\n  data: MetricData;\n  icon?: React.ReactNode;\n  formatValue?: (value: number) => string;\n}\n\n// Helper function to get appropriate background class for icon\nconst getIconBackgroundClass = (icon: React.ReactNode): string => {\n  if (!React.isValidElement(icon)) return 'bg-primary bg-opacity-10';\n\n  // Get the className from the icon props\n  const className = icon.props.className || '';\n  \n  // Extract color from className (e.g., \"text-blue-500\" -> \"blue\")\n  const colorMatch = className.match(/text-([a-z]+)-/);\n  if (colorMatch) {\n    const color = colorMatch[1];\n    return `bg-${color}-50`;\n  }\n  \n  return 'bg-primary bg-opacity-10';\n};\n\nconst MetricCard: React.FC<MetricCardProps> = ({\n  title,\n  data,\n  icon,\n  formatValue = (value) => value.toString()\n}) => {\n  return (\n    <Card>\n      <div className=\"flex items-center\">\n        <div className={`p-3 rounded-full ${getIconBackgroundClass(icon)}`}>\n          {/* Ensure consistent icon styling while preserving color */}\n          {React.isValidElement(icon) ? (\n            (() => {\n              const iconElement = icon as React.ReactElement;\n              const existingClassName = iconElement.props.className || '';\n              const colorMatch = existingClassName.match(/text-[a-z0-9-]+/);\n              const colorClass = colorMatch ? colorMatch[0] : 'text-gray-600';\n              const sizeClass = 'h-6 w-6';\n              \n              return React.cloneElement(iconElement, {\n                className: `${sizeClass} ${colorClass}`\n              });\n            })()\n          ) : (\n            icon\n          )}\n        </div>\n        <div className=\"ml-4 flex-1\">\n          <p className=\"text-sm font-medium text-gray-500\">{title}</p>\n          <div className=\"flex items-baseline\">\n            <p className=\"text-2xl font-semibold text-gray-900\">\n              {formatValue(data.total)}\n            </p>\n            {data.growth !== undefined && (\n              <p className={`ml-2 flex items-baseline text-sm font-semibold ${\n                data.growth >= 0 ? 'text-green-600' : 'text-red-600'\n              }`}>\n                {data.growth >= 0 ? '+' : ''}{data.growth.toFixed(1)}%\n              </p>\n            )}\n          </div>\n        </div>\n      </div>\n    </Card>\n  );\n};\n\nexport default MetricCard;\n", "/**\n * Metrics Section Component\n *\n * A simple metrics section component for displaying multiple metrics.\n * This is a replacement for the analytics MetricsSection to avoid dependencies.\n */\n\nimport React from 'react';\nimport MetricCard from './MetricCard';\n\nexport interface Metric {\n  title: string;\n  value: string | number;\n  change?: number;\n  icon?: React.ReactNode;\n}\n\ninterface MetricsSectionProps {\n  metrics: Metric[];\n  className?: string;\n}\n\nconst MetricsSection: React.FC<MetricsSectionProps> = ({\n  metrics,\n  className = ''\n}) => {\n  return (\n    <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 ${className}`}>\n      {metrics.map((metric, index) => (\n        <MetricCard\n          key={index}\n          title={metric.title}\n          data={{ \n            total: typeof metric.value === 'string' ? parseFloat(metric.value) || 0 : metric.value, \n            growth: metric.change || 0 \n          }}\n          icon={metric.icon}\n        />\n      ))}\n    </div>\n  );\n};\n\nexport default MetricsSection;\n", "import React from 'react';\r\nimport { CurrencyDollarIcon, ShoppingCartIcon, ClockIcon } from '@heroicons/react/24/outline';\r\nimport { Line } from 'react-chartjs-2';\r\nimport MetricsSection from '../../../components/common/MetricsSection';\r\nimport type { Metric } from '../../../components/common/MetricsSection';\r\nimport { formatCurrency } from '../../../utils/formatters';\r\nimport { defaultLineChartOptions } from '../../../utils/chartConfig';\r\n\r\ninterface UserAnalyticsProps {\r\n  userId: string;\r\n  userData: {\r\n    totalOrders: number;\r\n    totalSpent: number;\r\n    averageOrderValue: number;\r\n    orderFrequency: number;\r\n    orderHistory: {\r\n      date: string;\r\n      amount: number;\r\n    }[];\r\n  };\r\n}\r\n\r\nconst UserAnalytics: React.FC<UserAnalyticsProps> = ({\r\n  userId: _userId,\r\n  userData\r\n}) => {\r\n  // Validate userData to prevent runtime errors\r\n  const safeUserData = {\r\n    totalOrders: userData?.totalOrders || 0,\r\n    totalSpent: userData?.totalSpent || 0,\r\n    averageOrderValue: userData?.averageOrderValue || 0,\r\n    orderFrequency: userData?.orderFrequency || 0,\r\n    orderHistory: userData?.orderHistory || []\r\n  };\r\n\r\n\r\n\r\n  // Prepare metrics with safe data and new analytics\r\n  const metrics: Metric[] = [\r\n    {\r\n      title: 'Total Orders',\r\n      value: safeUserData.totalOrders,\r\n      icon: <ShoppingCartIcon className=\"w-6 h-6 text-blue-500\" />\r\n    },\r\n    {\r\n      title: 'Total Spent',\r\n      value: formatCurrency(safeUserData.totalSpent),\r\n      icon: <CurrencyDollarIcon className=\"w-6 h-6 text-green-500\" />\r\n    },\r\n    {\r\n      title: 'Average Order',\r\n      value: formatCurrency(safeUserData.averageOrderValue),\r\n      icon: <ClockIcon className=\"w-6 h-6 text-purple-500\" />\r\n    },\r\n  ];\r\n  \r\n  // Helper function to format date for chart labels\r\n  const formatDateForChart = (dateString: string): string => {\r\n    try {\r\n      const date = new Date(dateString);\r\n      // Format as MM/DD or MM/DD/YY for better readability\r\n      return date.toLocaleDateString('en-US', {\r\n        month: 'short',\r\n        day: 'numeric',\r\n        year: '2-digit'\r\n      });\r\n    } catch (error) {\r\n      // Fallback to original string if date parsing fails\r\n      return dateString;\r\n    }\r\n  };\r\n\r\n  // Prepare spending trend data for Line chart\r\n  const prepareSpendingTrendData = () => {\r\n    if (safeUserData.orderHistory.length === 0) {\r\n      return {\r\n        labels: ['No Data'],\r\n        datasets: [{\r\n          label: 'Spending Trend',\r\n          data: [0],\r\n          borderColor: '#F28B22',\r\n          backgroundColor: 'rgba(242, 139, 34, 0.1)',\r\n          fill: true,\r\n          tension: 0.4\r\n        }]\r\n      };\r\n    }\r\n\r\n    // Sort orders by date and calculate cumulative spending\r\n    const sortedOrders = safeUserData.orderHistory\r\n      .filter(item => item && item.date && typeof item.amount === 'number')\r\n      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());\r\n\r\n    let cumulativeSpending = 0;\r\n    const trendData = sortedOrders.map(order => {\r\n      cumulativeSpending += order.amount;\r\n      return {\r\n        label: formatDateForChart(order.date),\r\n        value: cumulativeSpending\r\n      };\r\n    });\r\n\r\n    return {\r\n      labels: trendData.map(item => item.label),\r\n      datasets: [{\r\n        label: 'Cumulative Spending',\r\n        data: trendData.map(item => item.value),\r\n        borderColor: '#F28B22',\r\n        backgroundColor: 'rgba(242, 139, 34, 0.1)',\r\n        fill: true,\r\n        tension: 0.4,\r\n        pointBackgroundColor: '#F28B22',\r\n        pointBorderColor: '#ffffff',\r\n        pointBorderWidth: 2,\r\n        pointRadius: 4\r\n      }]\r\n    };\r\n  };\r\n\r\n  const spendingTrendData = prepareSpendingTrendData();\r\n  \r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <MetricsSection metrics={metrics} />\r\n      \r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n        {/* Spending Trend Chart */}\r\n        <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\r\n          <div className=\"mb-4\">\r\n            <h3 className=\"text-lg font-semibold text-gray-900\">Spending Trend</h3>\r\n            <p className=\"text-sm text-gray-500\">\r\n              {safeUserData.orderHistory.length > 0\r\n                ? \"Cumulative spending over time\"\r\n                : \"No spending data available\"\r\n              }\r\n            </p>\r\n          </div>\r\n\r\n          {safeUserData.orderHistory.length > 0 ? (\r\n            <div className=\"h-80\">\r\n              <Line\r\n                data={spendingTrendData}\r\n                options={{\r\n                  ...defaultLineChartOptions,\r\n                  plugins: {\r\n                    ...defaultLineChartOptions.plugins,\r\n                    title: {\r\n                      display: false\r\n                    },\r\n                    legend: {\r\n                      display: false\r\n                    }\r\n                  },\r\n                  scales: {\r\n                    ...defaultLineChartOptions.scales,\r\n                    y: {\r\n                      ...defaultLineChartOptions.scales?.y,\r\n                      ticks: {\r\n                        ...defaultLineChartOptions.scales?.y?.ticks,\r\n                        callback: function(value) {\r\n                          return formatCurrency(value as number);\r\n                        }\r\n                      }\r\n                    }\r\n                  }\r\n                }}\r\n              />\r\n            </div>\r\n          ) : (\r\n            <div className=\"h-80 flex items-center justify-center\">\r\n              <div className=\"text-center\">\r\n                <div className=\"text-gray-400 text-4xl mb-4\">�</div>\r\n                <p className=\"text-gray-500 text-lg font-medium\">No Spending Data</p>\r\n                <p className=\"text-gray-400 text-sm mt-2\">\r\n                  Spending trends will appear here once the user places orders\r\n                </p>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Order Frequency Analysis */}\r\n        <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\r\n          <div className=\"mb-4\">\r\n            <h3 className=\"text-lg font-semibold text-gray-900\">Order Frequency</h3>\r\n            <p className=\"text-sm text-gray-500\">Order patterns and frequency analysis</p>\r\n          </div>\r\n\r\n          <div className=\"space-y-4\">\r\n            {safeUserData.orderHistory.length > 0 ? (\r\n              <>\r\n                <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\r\n                  <span className=\"text-sm font-medium text-gray-700\">Most Recent Order</span>\r\n                  <span className=\"text-sm text-gray-900\">\r\n                    {(() => {\r\n                      const lastOrder = safeUserData.orderHistory[safeUserData.orderHistory.length - 1];\r\n                      return lastOrder?.date ? formatDateForChart(lastOrder.date) : 'N/A';\r\n                    })()}\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\r\n                  <span className=\"text-sm font-medium text-gray-700\">First Order</span>\r\n                  <span className=\"text-sm text-gray-900\">\r\n                    {(() => {\r\n                      const firstOrder = safeUserData.orderHistory[0];\r\n                      return firstOrder?.date ? formatDateForChart(firstOrder.date) : 'N/A';\r\n                    })()}\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\r\n                  <span className=\"text-sm font-medium text-gray-700\">Largest Order</span>\r\n                  <span className=\"text-sm text-gray-900\">\r\n                    {formatCurrency(Math.max(...safeUserData.orderHistory.map(order => order.amount)))}\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\r\n                  <span className=\"text-sm font-medium text-gray-700\">Smallest Order</span>\r\n                  <span className=\"text-sm text-gray-900\">\r\n                    {formatCurrency(Math.min(...safeUserData.orderHistory.map(order => order.amount)))}\r\n                  </span>\r\n                </div>\r\n              </>\r\n            ) : (\r\n              <div className=\"text-center py-8\">\r\n                <div className=\"text-gray-400 text-3xl mb-3\">📊</div>\r\n                <p className=\"text-gray-500 font-medium\">No Order Data</p>\r\n                <p className=\"text-gray-400 text-sm mt-1\">\r\n                  Order frequency analysis will appear here\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UserAnalytics;", "/**\r\n * API Endpoints\r\n * \r\n * This file contains constants for all API endpoints.\r\n */\r\n\r\nexport const ENDPOINTS = {\r\n  // Auth endpoints\r\n  AUTH: {\r\n    LOGIN: '/auth/login',\r\n    LOGOUT: '/auth/logout',\r\n    CURRENT_USER: '/auth/me',\r\n    FORGOT_PASSWORD: '/auth/forgot-password',\r\n    RESET_PASSWORD: '/auth/reset-password',\r\n  },\r\n  \r\n  // User endpoints\r\n  USERS: {\r\n    BASE: '/users',\r\n    DETAILS: (id: string) => `/users/${id}`,\r\n    STATUS: (id: string) => `/users/${id}/status`,\r\n    BAN: (id: string) => `/users/${id}/ban`,\r\n    UNBAN: (id: string) => `/users/${id}/unban`,\r\n    UPLOAD_IMAGE: '/users/upload-image',\r\n  },\r\n  \r\n  // Supplier endpoints\r\n  SUPPLIERS: {\r\n    BASE: '/suppliers',\r\n    DETAILS: (id: string) => `/suppliers/${id}`,\r\n    VERIFY: (id: string) => `/suppliers/${id}/verify`,\r\n    REJECT: (id: string) => `/suppliers/${id}/reject`,\r\n    PRODUCTS: (id: string) => `/suppliers/${id}/products`,\r\n  },\r\n  \r\n  // Category endpoints\r\n  CATEGORIES: {\r\n    BASE: '/categories',\r\n    DETAILS: (id: string) => `/categories/${id}`,\r\n    PRODUCTS: (id: string) => `/categories/${id}/products`,\r\n  },\r\n\r\n  // Business Types endpoints\r\n  BUSINESS_TYPES: {\r\n    BASE: '/business-types',\r\n  },\r\n  \r\n  // Order endpoints\r\n  ORDERS: {\r\n    BASE: '/orders',\r\n    DETAILS: (id: string) => `/orders/${id}`,\r\n    APPROVE: (id: string) => `/orders/${id}/approve`,\r\n    REJECT: (id: string) => `/orders/${id}/reject`,\r\n    COMPLETE: (id: string) => `/orders/${id}/complete`,\r\n  },\r\n  \r\n  // Dashboard endpoints\r\n  DASHBOARD: {\r\n    STATS: '/dashboard/stats',\r\n    SALES: '/dashboard/sales',\r\n    USER_GROWTH: '/dashboard/user-growth',\r\n    CATEGORY_DISTRIBUTION: '/dashboard/category-distribution',\r\n    RECENT_ORDERS: '/dashboard/recent-orders',\r\n    SALES_CHART: '/dashboard/sales-chart',\r\n  },\r\n};\r\n", "/**\n * User API Data Transformers\n * \n * This file provides utilities to transform data between frontend and backend formats\n * for the users API, handling field name differences and data structure variations.\n */\n\nimport type { \n  User, \n  BackendUser, \n  UserFormData, \n  UserFormDataFrontend,\n  ApiResponseWrapper,\n  UserQueryParams\n} from '../types';\n\n/**\n * Transform frontend user form data to backend format\n */\nexport const transformUserFormToBackend = (frontendData: UserFormDataFrontend): UserFormData => {\n  const result: UserFormData = {\n    Name: frontendData.name,\n    Email: frontendData.email,\n    verificationStatus: frontendData.verificationStatus || 'pending'\n  };\n\n  if (frontendData.password !== undefined) {\n    result.password = frontendData.password;\n  }\n  if (frontendData.phone !== undefined) {\n    result.PhoneNumber = frontendData.phone;\n  }\n  if (frontendData.address !== undefined) {\n    result.Address = frontendData.address;\n  }\n  if (frontendData.businessType !== undefined) {\n    result.BusinessType = frontendData.businessType;\n  }\n\n  return result;\n};\n\n/**\n * Transform backend user data to frontend format\n */\nexport const transformBackendUserToFrontend = (backendUser: BackendUser): User => {\n  const result: User = {\n    id: backendUser.id,\n    name: backendUser.name,\n    email: backendUser.email,\n    type: backendUser.type,\n    status: backendUser.status,\n    verificationStatus: backendUser.verificationStatus || 'pending'\n  };\n\n  if (backendUser.avatar !== undefined) {\n    result.avatar = backendUser.avatar;\n  }\n  if (backendUser.phone !== undefined) {\n    result.phone = backendUser.phone;\n  }\n  if (backendUser.address !== undefined) {\n    result.address = backendUser.address;\n  }\n  if (backendUser.businessType !== undefined) {\n    result.businessType = backendUser.businessType;\n  }\n  if (backendUser.lastLogin !== undefined) {\n    result.lastLogin = backendUser.lastLogin;\n  }\n\n  return result;\n};\n\n/**\n * Transform frontend query parameters to backend format\n */\nexport const transformQueryParamsToBackend = (params: UserQueryParams): Record<string, any> => {\n  const backendParams: Record<string, any> = {};\n  \n  if (params.page !== undefined) backendParams.page = params.page;\n  if (params.limit !== undefined) backendParams.limit = params.limit;\n  if (params.search !== undefined) backendParams.search = params.search;\n  if (params.status !== undefined) backendParams.status = params.status;\n  if (params.sort !== undefined) backendParams.sort = params.sort;\n  if (params.order !== undefined) backendParams.order = params.order;\n  \n  return backendParams;\n};\n\n/**\n * Validate backend API response structure\n */\nexport const validateBackendResponse = <T>(response: any): ApiResponseWrapper<T> => {\n  if (!response) {\n    throw new Error('Empty response received');\n  }\n\n  if (typeof response !== 'object') {\n    throw new Error('Invalid response format');\n  }\n\n  if (!('success' in response)) {\n    throw new Error('Response missing success field');\n  }\n\n  if (response.success === false) {\n    throw new Error(response.message || 'Request failed');\n  }\n\n  if (!('data' in response)) {\n    throw new Error('Response missing data field');\n  }\n\n  return response as ApiResponseWrapper<T>;\n};\n\n/**\n * Transform backend user list response to frontend format\n */\nexport const transformUserListResponse = (backendResponse: any): ApiResponseWrapper<User[]> => {\n  const validatedResponse = validateBackendResponse<BackendUser[]>(backendResponse);\n\n  const transformedUsers = validatedResponse.data.map(transformBackendUserToFrontend);\n\n  const result: ApiResponseWrapper<User[]> = {\n    success: validatedResponse.success,\n    message: validatedResponse.message,\n    data: transformedUsers\n  };\n\n  if (validatedResponse.pagination !== undefined) {\n    result.pagination = validatedResponse.pagination;\n  }\n\n  return result;\n};\n\n/**\n * Transform backend single user response to frontend format\n */\nexport const transformUserResponse = (backendResponse: any): ApiResponseWrapper<User> => {\n  const validatedResponse = validateBackendResponse<BackendUser>(backendResponse);\n  \n  const transformedUser = transformBackendUserToFrontend(validatedResponse.data);\n  \n  return {\n    success: validatedResponse.success,\n    message: validatedResponse.message,\n    data: transformedUser\n  };\n};\n\n/**\n * Extract error message from backend response\n */\nexport const extractErrorMessage = (errorResponse: any): string => {\n  if (typeof errorResponse === 'string') {\n    return errorResponse;\n  }\n  \n  if (errorResponse?.message) {\n    return errorResponse.message;\n  }\n  \n  if (errorResponse?.error) {\n    return errorResponse.error;\n  }\n  \n  return 'An unexpected error occurred';\n};\n", "/**\r\n * Users API Service\r\n *\r\n * This file provides methods for interacting with the users API endpoints.\r\n */\r\n\r\nimport apiClient from '../../../api';\r\nimport { handleApiError } from '../../../utils/errorHandling';\r\nimport { ENDPOINTS } from '../../../constants/endpoints';\r\nimport type {\r\n  User,\r\n  UserFormDataFrontend,\r\n  UserQueryParams,\r\n  UserStatusUpdate,\r\n  ImageUploadResponse,\r\n  ApiResponseWrapper\r\n} from '../types';\r\nimport {\r\n  transformUserFormToBackend,\r\n  transformUserListResponse,\r\n  transformUserResponse,\r\n  transformQueryParamsToBackend,\r\n  validateBackendResponse,\r\n  extractErrorMessage\r\n} from '../utils/apiTransformers';\r\n\r\n/**\r\n * Users API service with methods for managing user data\r\n */\r\nexport const usersApi = {\r\n  /**\r\n   * Get all users with optional filtering and pagination\r\n   * @param params - Optional query parameters for filtering users\r\n   * @returns Promise resolving to paginated user data\r\n   */\r\n  getUsers: async (params?: UserQueryParams): Promise<ApiResponseWrapper<User[]>> => {\r\n    try {\r\n      const backendParams = params ? transformQueryParamsToBackend(params) : {};\r\n      const response = await apiClient.get(ENDPOINTS.USERS.BASE, { params: backendParams });\r\n\r\n      // Handle API client error wrapper\r\n      if (response.error) {\r\n        throw new Error(response.error);\r\n      }\r\n\r\n      if (!response.data) {\r\n        throw new Error('No response data received');\r\n      }\r\n\r\n      // Transform backend response to frontend format\r\n      return transformUserListResponse(response.data);\r\n    } catch (error: any) {\r\n      // Enhanced error handling for backend-specific errors\r\n      if (error.response?.data) {\r\n        const errorMessage = extractErrorMessage(error.response.data);\r\n        throw new Error(errorMessage);\r\n      }\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get a user by their ID\r\n   * @param id - The user's unique identifier\r\n   * @returns Promise resolving to a single user\r\n   */\r\n  getUserById: async (id: string): Promise<User> => {\r\n    try {\r\n      const response = await apiClient.get(ENDPOINTS.USERS.DETAILS(id));\r\n\r\n      // Handle API client error wrapper\r\n      if (response.error) {\r\n        throw new Error(response.error);\r\n      }\r\n\r\n      if (!response.data) {\r\n        throw new Error('No response data received');\r\n      }\r\n\r\n      // Transform backend response to frontend format\r\n      const transformedResponse = transformUserResponse(response.data);\r\n      return transformedResponse.data;\r\n    } catch (error: any) {\r\n      // Enhanced error handling for backend-specific errors\r\n      if (error.response?.data) {\r\n        const errorMessage = extractErrorMessage(error.response.data);\r\n        throw new Error(errorMessage);\r\n      }\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Create a new user\r\n   * @param userData - The user data to create\r\n   * @returns Promise resolving to the created user\r\n   */\r\n  createUser: async (userData: UserFormDataFrontend): Promise<User> => {\r\n    try {\r\n      // Transform frontend data to backend format\r\n      const backendData = transformUserFormToBackend(userData);\r\n      const response = await apiClient.post(ENDPOINTS.USERS.BASE, backendData);\r\n\r\n      // Handle API client error wrapper\r\n      if (response.error) {\r\n        throw new Error(response.error);\r\n      }\r\n\r\n      if (!response.data) {\r\n        throw new Error('No response data received');\r\n      }\r\n\r\n      // Transform backend response to frontend format\r\n      const transformedResponse = transformUserResponse(response.data);\r\n      return transformedResponse.data;\r\n    } catch (error: any) {\r\n      // Enhanced error handling for backend-specific errors\r\n      if (error.response?.data) {\r\n        const errorMessage = extractErrorMessage(error.response.data);\r\n        throw new Error(errorMessage);\r\n      }\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update an existing user\r\n   * @param id - The user's unique identifier\r\n   * @param userData - The user data to update\r\n   * @returns Promise resolving to the updated user\r\n   */\r\n  updateUser: async (id: string, userData: UserFormDataFrontend): Promise<User> => {\r\n    try {\r\n      // Transform frontend data to backend format\r\n      const backendData = transformUserFormToBackend(userData);\r\n      const response = await apiClient.put(ENDPOINTS.USERS.DETAILS(id), backendData);\r\n\r\n      // Handle API client error wrapper\r\n      if (response.error) {\r\n        throw new Error(response.error);\r\n      }\r\n\r\n      if (!response.data) {\r\n        throw new Error('No response data received');\r\n      }\r\n\r\n      // Transform backend response to frontend format\r\n      const transformedResponse = transformUserResponse(response.data);\r\n      return transformedResponse.data;\r\n    } catch (error: any) {\r\n      // Enhanced error handling for backend-specific errors\r\n      if (error.response?.data) {\r\n        const errorMessage = extractErrorMessage(error.response.data);\r\n        throw new Error(errorMessage);\r\n      }\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Delete a user\r\n   * @param id - The user's unique identifier\r\n   * @returns Promise resolving to a success indicator\r\n   */\r\n  deleteUser: async (id: string): Promise<void> => {\r\n    try {\r\n      const response = await apiClient.delete(ENDPOINTS.USERS.DETAILS(id));\r\n\r\n      // Handle API client error wrapper\r\n      if (response.error) {\r\n        throw new Error(response.error);\r\n      }\r\n\r\n      // For delete operations, we just need to check success\r\n      if (response.data && typeof response.data === 'object' && 'success' in response.data && !response.data.success) {\r\n        const errorMessage = 'message' in response.data ? response.data.message : 'Delete operation failed';\r\n        throw new Error(errorMessage as string);\r\n      }\r\n    } catch (error: any) {\r\n      // Enhanced error handling for backend-specific errors\r\n      if (error.response?.data) {\r\n        const errorMessage = extractErrorMessage(error.response.data);\r\n        throw new Error(errorMessage);\r\n      }\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n  \r\n  /**\r\n   * Update a user's status\r\n   * @param id - The user's unique identifier\r\n   * @param status - The new status to set\r\n   * @returns Promise resolving to void\r\n   */\r\n  updateUserStatus: async (id: string, status: 'active' | 'banned'): Promise<void> => {\r\n    try {\r\n      const statusData: UserStatusUpdate = { status };\r\n      const response = await apiClient.put(ENDPOINTS.USERS.STATUS(id), statusData);\r\n\r\n      // Handle API client error wrapper\r\n      if (response.error) {\r\n        throw new Error(response.error);\r\n      }\r\n\r\n      // For status update operations, we just need to check success\r\n      if (response.data && typeof response.data === 'object' && 'success' in response.data && !response.data.success) {\r\n        const errorMessage = 'message' in response.data ? response.data.message : 'Status update failed';\r\n        throw new Error(errorMessage as string);\r\n      }\r\n    } catch (error: any) {\r\n      // Enhanced error handling for backend-specific errors\r\n      if (error.response?.data) {\r\n        const errorMessage = extractErrorMessage(error.response.data);\r\n        throw new Error(errorMessage);\r\n      }\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Search for users by name or email\r\n   * @param query - The search query string\r\n   * @param params - Additional query parameters\r\n   * @returns Promise resolving to paginated user data\r\n   */\r\n  searchUsers: async (query: string, params?: Omit<UserQueryParams, 'search'>): Promise<ApiResponseWrapper<User[]>> => {\r\n    try {\r\n      const searchParams: UserQueryParams = { ...params, search: query };\r\n      return await usersApi.getUsers(searchParams);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get users filtered by type\r\n   * @param type - The user type to filter by\r\n   * @param params - Additional query parameters\r\n   * @returns Promise resolving to paginated user data\r\n   */\r\n  getUsersByType: async (_type: 'customer' | 'supplier', params?: Omit<UserQueryParams, 'type'>): Promise<ApiResponseWrapper<User[]>> => {\r\n    try {\r\n      // Note: Removed 'admin' type as it's not part of the regular user interface\r\n      const typeParams: UserQueryParams = { ...params };\r\n      // Add type filtering logic here if the backend supports it\r\n      // For now, we'll get all users and filter on the frontend if needed\r\n      return await usersApi.getUsers(typeParams);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Upload user image\r\n   * @param file - The image file to upload\r\n   * @returns Promise resolving to the uploaded image URL\r\n   */\r\n  uploadUserImage: async (file: File): Promise<ImageUploadResponse> => {\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('image', file);\r\n\r\n      const response = await apiClient.post(ENDPOINTS.USERS.UPLOAD_IMAGE, formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data'\r\n        }\r\n      });\r\n\r\n      // Handle API client error wrapper\r\n      if (response.error) {\r\n        throw new Error(response.error);\r\n      }\r\n\r\n      if (!response.data) {\r\n        throw new Error('No response data received');\r\n      }\r\n\r\n      // Validate backend response\r\n      const validatedResponse = validateBackendResponse<ImageUploadResponse>(response.data);\r\n      return validatedResponse.data;\r\n    } catch (error: any) {\r\n      // Enhanced error handling for backend-specific errors\r\n      if (error.response?.data) {\r\n        const errorMessage = extractErrorMessage(error.response.data);\r\n        throw new Error(errorMessage);\r\n      }\r\n      throw handleApiError(error);\r\n    }\r\n  }\r\n};\r\n\r\n// Export individual methods for more flexible importing\r\nexport const {\r\n  getUsers,\r\n  getUserById,\r\n  createUser,\r\n  updateUser,\r\n  deleteUser,\r\n  updateUserStatus,\r\n  searchUsers,\r\n  getUsersByType,\r\n  uploadUserImage\r\n} = usersApi;\r\n\r\nexport default usersApi;\r\n\r\n\r\n", "// src/features/users/hooks/useUsers.ts\r\n/**\r\n * Users Hook\r\n * \r\n * This hook provides methods and state for working with users.\r\n */\r\n\r\nimport { useCallback, useRef, useEffect } from 'react';\r\nimport { useEntityData } from '../../../hooks/useEntityData';\r\nimport usersApi from '../api/usersApi';\r\nimport type { User, UserFormDataFrontend, UserQueryParams } from '../types';\r\nimport useNotification from '../../../hooks/useNotification';\r\nimport apiClient from '../../../api';\r\n\r\nexport const useUsers = (options = { initialFetch: true }) => {\r\n  // Clear any cached users data on hook initialization\r\n  useEffect(() => {\r\n    // Clear cache for users endpoint\r\n    apiClient.clearCache();\r\n  }, []);\r\n\r\n  // Create an adapter that maps usersApi methods to what useEntityData expects\r\n  // Note: We need to adapt the new paginated response format\r\n  const apiAdapter = {\r\n    getAll: async () => {\r\n      const response = await usersApi.getUsers();\r\n      return response.data; // Extract just the data array for useEntityData\r\n    },\r\n    getById: usersApi.getUserById,\r\n    create: usersApi.createUser,\r\n    update: usersApi.updateUser,\r\n    delete: usersApi.deleteUser\r\n  };\r\n\r\n  const baseHook = useEntityData<User>(apiAdapter, {\r\n    entityName: 'users',\r\n    initialFetch: options.initialFetch\r\n  });\r\n\r\n  const { showNotification } = useNotification();\r\n\r\n  // Use ref to avoid dependency issues\r\n  const showNotificationRef = useRef(showNotification);\r\n\r\n  // Update ref when showNotification changes\r\n  useEffect(() => {\r\n    showNotificationRef.current = showNotification;\r\n  });\r\n  \r\n  // User-specific methods\r\n  const updateUserStatus = useCallback(async (id: string, status: 'active' | 'banned') => {\r\n    try {\r\n      await usersApi.updateUserStatus(id, status);\r\n\r\n      // Update the local state immediately using setEntities\r\n      const currentEntities = baseHook.entities as User[];\r\n      const updatedEntities = currentEntities.map(user =>\r\n        user.id === id ? { ...user, status } : user\r\n      );\r\n\r\n      // Use the exposed setEntities function to update the state\r\n      baseHook.setEntities(updatedEntities);\r\n\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: `User ${status === 'active' ? 'activated' : 'banned'} successfully`\r\n      });\r\n    } catch (error) {\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to ${status === 'active' ? 'activate' : 'ban'} user`\r\n      });\r\n      throw error;\r\n    }\r\n  }, [baseHook]);\r\n  \r\n  // Add updateUser method\r\n  const updateUser = useCallback(async (id: string, userData: UserFormDataFrontend) => {\r\n    try {\r\n      const updatedUser = await usersApi.updateUser(id, userData);\r\n\r\n      // Update the local state using setEntities\r\n      const currentEntities = baseHook.entities as User[];\r\n      const updatedEntities = currentEntities.map(user =>\r\n        user.id === id ? updatedUser : user\r\n      );\r\n\r\n      // Use the exposed setEntities function to update the state\r\n      baseHook.setEntities(updatedEntities);\r\n\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: 'User updated successfully'\r\n      });\r\n\r\n      return updatedUser;\r\n    } catch (error) {\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to update user'\r\n      });\r\n      throw error;\r\n    }\r\n  }, [baseHook]);\r\n\r\n  // Add search method with pagination support\r\n  const searchUsers = useCallback(async (query: string, params?: Omit<UserQueryParams, 'search'>) => {\r\n    try {\r\n      return await usersApi.searchUsers(query, params);\r\n    } catch (error) {\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to search users'\r\n      });\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  // Add method to get users with pagination\r\n  const getUsersWithPagination = useCallback(async (params?: UserQueryParams) => {\r\n    try {\r\n      return await usersApi.getUsers(params);\r\n    } catch (error) {\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to fetch users'\r\n      });\r\n      throw error;\r\n    }\r\n  }, []);\r\n  \r\n  return {\r\n    ...baseHook,\r\n    users: baseHook.entities as User[], // Rename for clarity\r\n    fetchUsers: baseHook.fetchEntities, // Rename for clarity\r\n    getUserById: baseHook.getEntityById, // Rename for clarity\r\n    createEntity: baseHook.createEntity, // Expose create method\r\n    deleteEntity: baseHook.deleteEntity, // Expose delete method\r\n    updateUserStatus, // Updated method name\r\n    updateUser, // Add the new method to the return object\r\n    searchUsers, // Add search method\r\n    getUsersWithPagination // Add pagination method\r\n  };\r\n};\r\n\r\nexport default useUsers;", "import React from 'react';\r\n\r\ninterface FormFieldProps {\r\n  label: string;\r\n  name: string;\r\n  type?: string;\r\n  value: any;\r\n  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;\r\n  error?: string | undefined;\r\n  required?: boolean;\r\n  placeholder?: string;\r\n  options?: { value: string; label: string }[];\r\n  className?: string;\r\n  disabled?: boolean;\r\n  loading?: boolean;\r\n}\r\n\r\nconst FormField: React.FC<FormFieldProps> = ({\r\n  label,\r\n  name,\r\n  type = 'text',\r\n  value,\r\n  onChange,\r\n  error,\r\n  required = false,\r\n  placeholder = '',\r\n  options = [],\r\n  className = '',\r\n  disabled = false,\r\n  loading = false\r\n}) => {\r\n  const inputClasses = `mt-1 block w-full rounded-md shadow-sm sm:text-sm ${\r\n    error ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-primary focus:ring-primary'\r\n  }`;\r\n  \r\n  const renderField = () => {\r\n    switch (type) {\r\n      case 'textarea':\r\n        return (\r\n          <textarea\r\n            id={name}\r\n            name={name}\r\n            value={value}\r\n            onChange={onChange}\r\n            className={inputClasses}\r\n            placeholder={placeholder}\r\n            disabled={disabled}\r\n          />\r\n        );\r\n      \r\n      case 'select':\r\n        return (\r\n          <select\r\n            id={name}\r\n            name={name}\r\n            value={value}\r\n            onChange={onChange}\r\n            className={inputClasses}\r\n            disabled={disabled || loading}\r\n          >\r\n            {loading ? (\r\n              <option value=\"\">Loading...</option>\r\n            ) : (\r\n              options.map(option => (\r\n                <option key={option.value} value={option.value}>\r\n                  {option.label}\r\n                </option>\r\n              ))\r\n            )}\r\n          </select>\r\n        );\r\n      \r\n      case 'checkbox':\r\n        return (\r\n          <input\r\n            type=\"checkbox\"\r\n            id={name}\r\n            name={name}\r\n            checked={value}\r\n            onChange={onChange}\r\n            className=\"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary\"\r\n            disabled={disabled}\r\n          />\r\n        );\r\n      \r\n      default:\r\n        return (\r\n          <input\r\n            type={type}\r\n            id={name}\r\n            name={name}\r\n            value={value}\r\n            onChange={onChange}\r\n            className={inputClasses}\r\n            placeholder={placeholder}\r\n            disabled={disabled}\r\n          />\r\n        );\r\n    }\r\n  };\r\n  \r\n  return (\r\n    <div className={`${className}`}>\r\n      <label htmlFor={name} className=\"block text-sm font-medium text-gray-700\">\r\n        {label} {required && <span className=\"text-red-500\">*</span>}\r\n      </label>\r\n      {renderField()}\r\n      {error && <p className=\"mt-1 text-sm text-red-600\">{error}</p>}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FormField;\r\n", "import React from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\ninterface DetailSectionProps {\r\n  title: string;\r\n  description?: string;\r\n  children: ReactNode;\r\n  className?: string;\r\n}\r\n\r\nconst DetailSection: React.FC<DetailSectionProps> = ({\r\n  title,\r\n  description,\r\n  children,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <div className={`bg-white shadow overflow-hidden sm:rounded-lg ${className}`}>\r\n      <div className=\"px-4 py-5 sm:px-6\">\r\n        <h3 className=\"text-lg leading-6 font-medium text-gray-900\">{title}</h3>\r\n        {description && (\r\n          <p className=\"mt-1 max-w-2xl text-sm text-gray-500\">{description}</p>\r\n        )}\r\n      </div>\r\n      <div className=\"border-t border-gray-200\">\r\n        {children}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DetailSection;", "/**\r\n * Image Upload Component\r\n * \r\n * A reusable component for uploading and previewing images with drag and drop support.\r\n */\r\n\r\nimport React, { useState, useRef, useCallback } from 'react';\r\nimport { PhotoIcon, XMarkIcon } from '@heroicons/react/24/outline';\r\nimport { validateFile } from '../../utils/errorHandling';\r\n\r\ninterface ImageUploadProps {\r\n  label: string;\r\n  name: string;\r\n  value?: File | string | null;\r\n  onChange: (file: File | null) => void;\r\n  error?: string | undefined;\r\n  required?: boolean;\r\n  disabled?: boolean;\r\n  maxSize?: number; // in bytes\r\n  allowedTypes?: string[];\r\n  className?: string;\r\n}\r\n\r\nconst ImageUpload: React.FC<ImageUploadProps> = ({\r\n  label,\r\n  name,\r\n  value,\r\n  onChange,\r\n  error,\r\n  required = false,\r\n  disabled = false,\r\n  maxSize = 5 * 1024 * 1024, // 5MB default\r\n  allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],\r\n  className = ''\r\n}) => {\r\n  const [isDragOver, setIsDragOver] = useState(false);\r\n  const [preview, setPreview] = useState<string | null>(null);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  // Generate preview URL when value changes\r\n  React.useEffect(() => {\r\n    if (value instanceof File) {\r\n      const url = URL.createObjectURL(value);\r\n      setPreview(url);\r\n      return () => URL.revokeObjectURL(url);\r\n    } else if (typeof value === 'string' && value) {\r\n      setPreview(value);\r\n      return;\r\n    } else {\r\n      setPreview(null);\r\n      return;\r\n    }\r\n  }, [value]);\r\n\r\n  const handleFileSelect = useCallback((file: File) => {\r\n    const validation = validateFile(file, {\r\n      maxSize,\r\n      allowedTypes\r\n    });\r\n\r\n    if (validation.valid) {\r\n      onChange(file);\r\n    } else {\r\n      // Handle validation error - you might want to show this error\r\n      console.error('File validation failed:', validation.error);\r\n    }\r\n  }, [maxSize, allowedTypes, onChange]);\r\n\r\n  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = e.target.files?.[0];\r\n    if (file) {\r\n      handleFileSelect(file);\r\n    }\r\n  };\r\n\r\n  const handleDragOver = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    if (!disabled) {\r\n      setIsDragOver(true);\r\n    }\r\n  };\r\n\r\n  const handleDragLeave = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragOver(false);\r\n  };\r\n\r\n  const handleDrop = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragOver(false);\r\n    \r\n    if (disabled) return;\r\n\r\n    const file = e.dataTransfer.files?.[0];\r\n    if (file) {\r\n      handleFileSelect(file);\r\n    }\r\n  };\r\n\r\n  const handleRemove = () => {\r\n    onChange(null);\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  const handleClick = () => {\r\n    if (!disabled && fileInputRef.current) {\r\n      fileInputRef.current.click();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={className}>\r\n      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n        {label} {required && <span className=\"text-red-500\">*</span>}\r\n      </label>\r\n      \r\n      <div\r\n        className={`\r\n          relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors\r\n          ${isDragOver ? 'border-primary bg-primary bg-opacity-5' : 'border-gray-300'}\r\n          ${error ? 'border-red-300' : ''}\r\n          ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-primary hover:bg-gray-50'}\r\n        `}\r\n        onDragOver={handleDragOver}\r\n        onDragLeave={handleDragLeave}\r\n        onDrop={handleDrop}\r\n        onClick={handleClick}\r\n      >\r\n        <input\r\n          ref={fileInputRef}\r\n          type=\"file\"\r\n          name={name}\r\n          accept={allowedTypes.join(',')}\r\n          onChange={handleFileInputChange}\r\n          className=\"hidden\"\r\n          disabled={disabled}\r\n        />\r\n\r\n        {preview ? (\r\n          <div className=\"relative\">\r\n            <img\r\n              src={preview}\r\n              alt=\"Preview\"\r\n              className=\"mx-auto h-32 w-32 object-cover rounded-lg\"\r\n            />\r\n            {!disabled && (\r\n              <button\r\n                type=\"button\"\r\n                onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  handleRemove();\r\n                }}\r\n                className=\"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors\"\r\n              >\r\n                <XMarkIcon className=\"h-4 w-4\" />\r\n              </button>\r\n            )}\r\n          </div>\r\n        ) : (\r\n          <div>\r\n            <PhotoIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\r\n            <div className=\"mt-4\">\r\n              <p className=\"text-sm text-gray-600\">\r\n                {isDragOver ? 'Drop image here' : 'Click to upload or drag and drop'}\r\n              </p>\r\n              <p className=\"text-xs text-gray-500 mt-1\">\r\n                PNG, JPG, GIF up to {Math.round(maxSize / 1024 / 1024)}MB\r\n              </p>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {error && <p className=\"mt-1 text-sm text-red-600\">{error}</p>}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ImageUpload;\r\n", "/**\r\n * Chart.js Configuration\r\n *\r\n * This file configures Chart.js and registers all necessary components.\r\n */\r\n\r\nimport {\r\n  Chart as ChartJS,\r\n  CategoryScale,\r\n  LinearScale,\r\n  PointElement,\r\n  LineElement,\r\n  BarElement,\r\n  ArcElement,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n  Filler,\r\n  RadialLinearScale,\r\n  type ChartOptions\r\n} from 'chart.js';\r\n\r\n// Register ChartJS components\r\nChartJS.register(\r\n  CategoryScale,\r\n  LinearScale,\r\n  PointElement,\r\n  LineElement,\r\n  BarElement,\r\n  ArcElement, // Required for Pie and Doughnut charts\r\n  RadialLinearScale,\r\n  Title,\r\n  Tooltip,\r\n  Legend,\r\n  Filler\r\n);\r\n\r\n// Default chart options\r\nexport const defaultLineChartOptions: ChartOptions<'line'> = {\r\n  responsive: true,\r\n  maintainAspectRatio: false,\r\n  plugins: {\r\n    title: {\r\n      display: true,\r\n      color: '#000000',\r\n      font: {\r\n        size: 16,\r\n        weight: 'bold'\r\n      }\r\n    },\r\n    legend: {\r\n      display: false,\r\n      labels: {\r\n        color: '#000000'\r\n      }\r\n    },\r\n    tooltip: {\r\n      backgroundColor: '#F28B22',\r\n      titleColor: '#FFFFFF',\r\n      bodyColor: '#FFFFFF',\r\n      padding: 12,\r\n      displayColors: false\r\n    }\r\n  },\r\n  scales: {\r\n    x: {\r\n      grid: {\r\n        display: false\r\n      },\r\n      ticks: {\r\n        color: '#000000'\r\n      }\r\n    },\r\n    y: {\r\n      beginAtZero: true,\r\n      grid: {\r\n        display: true,\r\n        color: '#E5E7EB'\r\n      },\r\n      ticks: {\r\n        color: '#000000'\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\nexport const defaultPieChartOptions: ChartOptions<'pie'> = {\r\n  responsive: true,\r\n  maintainAspectRatio: false,\r\n  plugins: {\r\n    title: {\r\n      display: true,\r\n      color: '#000000',\r\n      font: {\r\n        size: 16,\r\n        weight: 'bold'\r\n      }\r\n    },\r\n    legend: {\r\n      position: 'bottom',\r\n      labels: {\r\n        color: '#000000',\r\n        font: {\r\n          size: 12\r\n        }\r\n      }\r\n    },\r\n    tooltip: {\r\n      backgroundColor: '#F28B22',\r\n      titleColor: '#FFFFFF',\r\n      bodyColor: '#FFFFFF',\r\n      padding: 12\r\n    }\r\n  }\r\n};\r\n\r\nexport const defaultBarChartOptions: ChartOptions<'bar'> = {\r\n  responsive: true,\r\n  maintainAspectRatio: false,\r\n  plugins: {\r\n    title: {\r\n      display: true,\r\n      color: '#000000',\r\n      font: {\r\n        size: 16,\r\n        weight: 'bold'\r\n      }\r\n    },\r\n    legend: {\r\n      display: false,\r\n      labels: {\r\n        color: '#000000'\r\n      }\r\n    },\r\n    tooltip: {\r\n      backgroundColor: '#F28B22',\r\n      titleColor: '#FFFFFF',\r\n      bodyColor: '#FFFFFF',\r\n      padding: 12,\r\n      displayColors: false\r\n    }\r\n  },\r\n  scales: {\r\n    x: {\r\n      grid: {\r\n        display: false\r\n      },\r\n      ticks: {\r\n        color: '#000000'\r\n      }\r\n    },\r\n    y: {\r\n      beginAtZero: true,\r\n      grid: {\r\n        display: true,\r\n        color: '#E5E7EB'\r\n      },\r\n      ticks: {\r\n        color: '#000000'\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\n// Helper function to destroy chart instances\r\nexport const destroyChart = (chartInstance: ChartJS | null) => {\r\n  if (chartInstance) {\r\n    chartInstance.destroy();\r\n  }\r\n};\r\n\r\nexport default ChartJS;\r\n", "import * as React from \"react\";\nfunction EnvelopeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EnvelopeIcon);\nexport default ForwardRef;"], "names": ["_ref", "children", "className", "_jsx", "PhotoIcon", "svgRef", "title", "titleId", "props", "React", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "id", "strokeLinecap", "strokeLinejoin", "d", "label", "value", "_jsxs", "businessTypesApi", "getBusinessTypes", "async", "response", "apiClient", "get", "responseValidators", "getList", "error", "handleApiError", "getBusinessTypeById", "getById", "onSubmit", "onCancel", "isLoading", "formData", "setFormData", "useState", "name", "email", "type", "phone", "address", "businessType", "password", "confirmPassword", "sendInvite", "image", "errors", "setErrors", "businessTypes", "setBusinessTypes", "loadingBusinessTypes", "setLoadingBusinessTypes", "useEffect", "types", "console", "loadBusinessTypes", "handleChange", "e", "target", "checked", "prev", "preventDefault", "validateUserForm", "formValidationRules", "validationRules", "required", "passwordMatch", "newErrors", "validateForm", "keys", "length", "submitData", "FormField", "onChange", "options", "placeholder", "loading", "map", "ImageUpload", "file", "undefined", "maxSize", "allowedTypes", "<PERSON><PERSON>", "variant", "onClick", "disabled", "user", "Card", "htmlFor", "window", "history", "back", "orders", "description", "onViewOrder", "emptyMessage", "DetailSection", "scope", "order", "formatDate", "orderDate", "formatCurrency", "totalAmount", "StatusBadge", "status", "size", "icon", "EyeIcon", "userOrders", "navigate", "useNavigate", "DetailList", "DetailItem", "lastLogin", "OrdersSection", "ROUTES", "getOrderDetailsRoute", "Avatar", "avatar", "src", "alt", "char<PERSON>t", "toUpperCase", "slice", "CheckCircleIcon", "XCircleIcon", "users", "onViewUser", "onEditUser", "_onEditUser", "onDeleteUser", "onUserClick", "_onUserClick", "columns", "key", "sortable", "render", "_value", "EnvelopeIcon", "_", "stopPropagation", "getUserEditRoute", "PencilIcon", "TrashIcon", "BaseEntityList", "data", "onRowClick", "pagination", "getIconBackgroundClass", "colorMatch", "match", "formatValue", "toString", "iconElement", "colorClass", "total", "growth", "toFixed", "metrics", "metric", "index", "MetricCard", "parseFloat", "change", "_defaultLineChartOpti", "_defaultLineChartOpti2", "_defaultLineChartOpti3", "userId", "_userId", "userData", "safeUserData", "totalOrders", "totalSpent", "averageOrderValue", "orderFrequency", "orderHistory", "ShoppingCartIcon", "CurrencyDollarIcon", "ClockIcon", "formatDateForChart", "dateString", "Date", "toLocaleDateString", "month", "day", "year", "spendingTrendData", "prepareSpendingTrendData", "labels", "datasets", "borderColor", "backgroundColor", "tension", "sortedOrders", "filter", "item", "date", "amount", "sort", "a", "b", "getTime", "cumulativeSpending", "trendData", "pointBackgroundColor", "pointBorderColor", "pointBorderWidth", "pointRadius", "MetricsSection", "Line", "defaultLineChartOptions", "plugins", "display", "legend", "scales", "y", "ticks", "callback", "_Fragment", "lastOrder", "firstOrder", "Math", "max", "min", "ENDPOINTS", "BASE", "DETAILS", "STATUS", "BAN", "UNBAN", "UPLOAD_IMAGE", "transformUserFormToBackend", "frontendData", "result", "Name", "Email", "verificationStatus", "PhoneNumber", "Address", "BusinessType", "transformBackendUserToFrontend", "backendUser", "validateBackendResponse", "Error", "success", "message", "transformUserResponse", "backendResponse", "validatedResponse", "transformedUser", "extractErrorMessage", "errorResponse", "usersApi", "getUsers", "backendParams", "params", "page", "limit", "search", "transformQueryParamsToBackend", "transformedUsers", "transformUserListResponse", "_error$response", "errorMessage", "getUserById", "_error$response2", "createUser", "backendData", "post", "_error$response3", "updateUser", "put", "_error$response4", "deleteUser", "delete", "_error$response5", "updateUserStatus", "statusData", "_error$response6", "searchUsers", "query", "searchParams", "getUsersByType", "_type", "typeParams", "uploadUserImage", "FormData", "append", "headers", "_error$response7", "arguments", "initialFetch", "clearCache", "apiAdapter", "getAll", "create", "update", "baseHook", "useEntityData", "entityName", "showNotification", "useNotification", "showNotificationRef", "useRef", "current", "useCallback", "updatedEntities", "entities", "setEntities", "updatedUser", "getUsersWithPagination", "fetchUsers", "fetchEntities", "getEntityById", "createEntity", "deleteEntity", "inputClasses", "renderField", "option", "isDragOver", "setIsDragOver", "preview", "setPreview", "fileInputRef", "File", "url", "URL", "createObjectURL", "revokeObjectURL", "handleFileSelect", "validation", "validateFile", "valid", "onDragOver", "onDragLeave", "onDrop", "_e$dataTransfer$files", "dataTransfer", "files", "handleClick", "click", "accept", "join", "_e$target$files", "XMarkIcon", "round", "ChartJS", "register", "CategoryScale", "LinearScale", "PointElement", "LineElement", "BarElement", "ArcElement", "RadialLinearScale", "Title", "<PERSON><PERSON><PERSON>", "Legend", "Filler", "responsive", "maintainAspectRatio", "color", "font", "weight", "tooltip", "titleColor", "bodyColor", "padding", "displayColors", "x", "grid", "beginAtZero", "defaultPieChartOptions", "position", "destroy<PERSON>hart", "chartInstance", "destroy"], "sourceRoot": ""}