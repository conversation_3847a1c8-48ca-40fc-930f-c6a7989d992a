"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[88],{2806:(e,t,a)=>{a.d(t,{A:()=>c});var s=a(5043),r=a(5475),n=a(5501),l=a(6365),i=a(579);const o=e=>{let{title:t,description:a,actions:s,breadcrumbs:o,className:c="",testId:d}=e;return(0,i.jsxs)("div",{className:`mb-6 ${c}`,"data-testid":d,children:[o&&o.length>0&&(0,i.jsx)("nav",{className:"flex mb-4","aria-label":"Breadcrumb",children:(0,i.jsxs)("ol",{className:"flex items-center space-x-1 text-sm text-gray-500",children:[(0,i.jsx)("li",{children:(0,i.jsx)(r.N_,{to:"/",className:"flex items-center hover:text-primary","aria-label":"Home",children:(0,i.jsx)(n.A,{className:"h-4 w-4"})})}),o.map(((e,t)=>(0,i.jsxs)("li",{className:"flex items-center",children:[(0,i.jsx)(l.A,{className:"h-4 w-4 mx-1 text-gray-400"}),e.path&&t<o.length-1?(0,i.jsx)(r.N_,{to:e.path,className:"hover:text-primary",children:e.label}):(0,i.jsx)("span",{className:"font-medium text-gray-700",children:e.label})]},t)))]})}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-2xl font-bold text-gray-800",children:t}),a&&"string"===typeof a?(0,i.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:a}):a]}),s&&(0,i.jsx)("div",{className:"flex flex-wrap gap-3 mt-2 sm:mt-0",children:s})]})]})},c=(0,s.memo)(o)},6365:(e,t,a)=>{a.d(t,{A:()=>n});var s=a(5043);function r(e,t){let{title:a,titleId:r,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),a?s.createElement("title",{id:r},a):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))}const n=s.forwardRef(r)},7098:(e,t,a)=>{a.d(t,{A:()=>n});var s=a(5043);function r(e,t){let{title:a,titleId:r,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),a?s.createElement("title",{id:r},a):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const n=s.forwardRef(r)},8100:(e,t,a)=>{a.d(t,{A:()=>o});var s=a(5043),r=a(7591),n=a(7950),l=a(579);const i=e=>{let{isOpen:t,onClose:a,title:i,children:o,size:c="md",footer:d,closeOnEsc:m=!0,closeOnBackdropClick:u=!0,showCloseButton:x=!0,centered:h=!0,className:p="",bodyClassName:g="",headerClassName:y="",footerClassName:b="",backdropClassName:f="",testId:v}=e;const j=(0,s.useRef)(null);if((0,s.useEffect)((()=>{const e=e=>{m&&"Escape"===e.key&&a()};return t&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="auto"}}),[t,a,m]),(0,s.useEffect)((()=>{if(!t||!j.current)return;const e=j.current.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');if(0===e.length)return;const a=e[0],s=e[e.length-1],r=e=>{"Tab"===e.key&&(e.shiftKey?document.activeElement===a&&(s.focus(),e.preventDefault()):document.activeElement===s&&(a.focus(),e.preventDefault()))};return document.addEventListener("keydown",r),a.focus(),()=>{document.removeEventListener("keydown",r)}}),[t]),!t)return null;const w=(0,l.jsxs)(s.Fragment,{children:[(0,l.jsx)("div",{className:`fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity ${f}`,onClick:u?a:void 0,"data-testid":`${v}-backdrop`}),(0,l.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,l.jsx)("div",{className:`flex min-h-full items-${h?"center":"start"} justify-center p-4 text-center`,children:(0,l.jsxs)("div",{ref:j,className:`${{xs:"max-w-xs",sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[c]} w-full transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all ${p}`,onClick:e=>e.stopPropagation(),"data-testid":v,children:[(0,l.jsxs)("div",{className:`flex items-center justify-between px-6 py-4 border-b border-gray-100 ${y}`,children:["string"===typeof i?(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:i}):i,x&&(0,l.jsx)("button",{type:"button",className:"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary rounded-full p-1",onClick:a,"aria-label":"Close modal","data-testid":`${v}-close-button`,children:(0,l.jsx)(r.A,{className:"h-6 w-6"})})]}),(0,l.jsx)("div",{className:`px-6 py-4 ${g}`,children:o}),d&&(0,l.jsx)("div",{className:`px-6 py-4 bg-gray-50 border-t border-gray-100 flex justify-end space-x-3 ${b}`,children:d})]})})})]});return(0,n.createPortal)(w,document.body)},o=(0,s.memo)(i)},8662:(e,t,a)=>{a.d(t,{A:()=>n});var s=a(5043);function r(e,t){let{title:a,titleId:r,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),a?s.createElement("title",{id:r},a):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))}const n=s.forwardRef(r)},9531:(e,t,a)=>{a.d(t,{A:()=>n});var s=a(5043);function r(e,t){let{title:a,titleId:r,...n}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),a?s.createElement("title",{id:r},a):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const n=s.forwardRef(r)},9895:(e,t,a)=>{a.d(t,{W9:()=>h,h4:()=>m,Ck:()=>v});var s=a(5043),r=a(7422),n=a(4538),l=a(7098),i=a(9531),o=a(2811),c=a(9248),d=a(579);const m=e=>{let{categories:t,onCategoryClick:a,onViewCategory:s,onEditCategory:m,onDeleteCategory:u,title:x="Categories",loading:h=!1}=e;const p=[{key:"id",label:"ID",sortable:!0,render:e=>(0,d.jsx)("span",{className:"text-xs text-gray-500",children:e})},{key:"name",label:"Name",sortable:!0,render:e=>(0,d.jsx)("span",{className:"font-medium text-gray-900",children:e})},{key:"description",label:"Description",sortable:!0},{key:"productCount",label:"Products",sortable:!0,render:e=>(0,d.jsx)("span",{className:"font-medium",children:e})},{key:"status",label:"Status",sortable:!0,render:e=>(0,d.jsxs)("div",{className:"flex items-center",children:["active"===e?(0,d.jsx)(n.A,{className:"w-4 h-4 text-green-500 mr-1"}):(0,d.jsx)(l.A,{className:"w-4 h-4 text-red-500 mr-1"}),(0,d.jsx)("span",{children:e.charAt(0).toUpperCase()+e.slice(1)})]})},{key:"createdAt",label:"Created At",sortable:!0},{key:"actions",label:"Actions",render:(e,t)=>(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[s&&(0,d.jsx)("button",{className:"p-1 text-gray-500 hover:text-primary rounded-full hover:bg-gray-100",onClick:e=>{e.stopPropagation(),s(t)},children:(0,d.jsx)(i.A,{className:"w-5 h-5"})}),m&&(0,d.jsx)("button",{className:"p-1 text-gray-500 hover:text-blue-600 rounded-full hover:bg-gray-100",onClick:e=>{e.stopPropagation(),m(t)},children:(0,d.jsx)(o.A,{className:"w-5 h-5"})}),u&&(0,d.jsx)("button",{className:"p-1 text-gray-500 hover:text-red-600 rounded-full hover:bg-gray-100",onClick:e=>{e.stopPropagation(),u(t)},children:(0,d.jsx)(c.A,{className:"w-5 h-5"})})]})}];return(0,d.jsx)(r.A,{columns:p,data:t,onRowClick:a,title:x,pagination:!0,loading:h})};var u=a(7907);var x=a(6773);const h=e=>{let{onSubmit:t,onCancel:a,isLoading:r=!1}=e;const[n,l]=(0,s.useState)({name:"",description:"",status:"active",visibleInSupplierApp:!0,visibleInCustomerApp:!0}),[i,o]=(0,s.useState)({}),c=e=>{const{name:t,value:a}=e.target;l((e=>({...e,[t]:a}))),i[t]&&o((e=>({...e,[t]:""})))};return(0,d.jsxs)("form",{onSubmit:e=>{e.preventDefault(),(()=>{const e=(0,x.l)({name:n.name,description:n.description},{name:[x.tU.required("Category name is required")],description:[x.tU.required("Description is required")]});return o(e),0===Object.keys(e).length})()&&t(n)},className:"space-y-6",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:["Category Name ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"text",id:"name",name:"name",value:n.name,onChange:c,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm "+(i.name?"border-red-300":"")}),i.name&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:i.name})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700",children:["Description ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("textarea",{id:"description",name:"description",rows:3,value:n.description,onChange:c,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm "+(i.description?"border-red-300":"")}),i.description&&(0,d.jsx)("p",{className:"mt-1 text-sm text-red-600",children:i.description})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700",children:"Status"}),(0,d.jsxs)("select",{id:"status",name:"status",value:n.status,onChange:c,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm",children:[(0,d.jsx)("option",{value:"active",children:"Active"}),(0,d.jsx)("option",{value:"inactive",children:"Inactive"})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsx)("div",{children:(0,d.jsxs)("label",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",name:"visibleInSupplierApp",checked:n.visibleInSupplierApp,onChange:e=>l((t=>({...t,visibleInSupplierApp:e.target.checked}))),className:"rounded border-gray-300 text-primary focus:ring-primary"}),(0,d.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Visible in Supplier App"})]})}),(0,d.jsx)("div",{children:(0,d.jsxs)("label",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",name:"visibleInCustomerApp",checked:n.visibleInCustomerApp,onChange:e=>l((t=>({...t,visibleInCustomerApp:e.target.checked}))),className:"rounded border-gray-300 text-primary focus:ring-primary"}),(0,d.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Visible in Customer App"})]})})]})]}),(0,d.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,d.jsx)(u.A,{type:"button",variant:"outline",onClick:a,disabled:r,children:"Cancel"}),(0,d.jsx)(u.A,{type:"submit",loading:r,children:"Add Category"})]})]})};var p=a(233),g=a(1568),y=a(4703),b=a(8479);const f={getCategories:async e=>{try{const t=await g.A.get("/categories",{params:e});return b.lg.getList(t,"categories")}catch(t){throw(0,y.hS)(t)}},getCategoryById:async e=>{try{const t=await g.A.get(`/categories/${e}`);return b.lg.getById(t,"category",e)}catch(t){throw(0,y.hS)(t)}},createCategory:async e=>{try{const t=await g.A.post("/categories",e);return b.lg.create(t,"category")}catch(t){throw(0,y.hS)(t)}},updateCategory:async(e,t)=>{try{const a=await g.A.put(`/categories/${e}`,t);return b.lg.update(a,"category",e)}catch(a){throw(0,y.hS)(a)}},deleteCategory:async e=>{try{const t=await g.A.delete(`/categories/${e}`);return b.lg.delete(t,"category",e)}catch(t){throw(0,y.hS)(t)}},getSubcategories:async e=>{try{const t=await g.A.get("/categories",{params:{parentId:e}});return b.lg.getList(t,"subcategories",!0)}catch(t){throw(0,y.hS)(t)}}},v=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{initialFetch:!0};const t=(0,p.B)({getAll:f.getCategories,getById:f.getCategoryById,create:f.createCategory,update:f.updateCategory,delete:f.deleteCategory},{entityName:"categories",initialFetch:e.initialFetch}),a=(0,s.useCallback)((()=>t.entities.map((e=>({...e,subcategories:e.subcategories||[]})))),[t.entities]);return{...t,categories:t.entities,fetchCategories:t.fetchEntities,getCategoryById:t.getEntityById,getCategoryHierarchy:a}};a(3109)}}]);
//# sourceMappingURL=88.0d6c7c3f.chunk.js.map