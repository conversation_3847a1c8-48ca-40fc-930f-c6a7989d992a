{"version": 3, "file": "static/js/402.dcecae95.chunk.js", "mappings": "2TA8BA,MAkJA,EAlJ0DA,IAOnD,IAPoD,cACzDC,EAAa,mBACbC,EAAkB,eAClBC,EAAc,cACdC,EAAa,MACbC,EAAQ,gBAAe,YACvBC,GACDN,EACC,MAAMO,EAAkC,CACtC,CACEC,IAAK,cACLC,MAAO,eACPC,UAAU,EACVC,OAAQA,CAACC,EAAgBC,KACvBC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACLC,IAAKN,EAAaO,YAClBC,KAAMR,EAAaO,YACnBE,KAAK,UAGTR,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4BAA2BC,SAAEH,EAAaO,eACzDN,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,CAAC,OAAKH,EAAaU,aAKjE,CACEf,IAAK,QACLC,MAAO,QACPC,UAAU,EACVC,OAASa,IACPV,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAACQ,EAAAA,EAAY,CAACV,UAAU,gCACxBE,EAAAA,EAAAA,KAAA,QAAAD,SAAOQ,QAIb,CACEhB,IAAK,QACLC,MAAO,QACPC,UAAU,EACVC,OAASa,IACPV,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAACX,UAAU,gCACrBE,EAAAA,EAAAA,KAAA,QAAAD,SAAOQ,QAIb,CACEhB,IAAK,SACLC,MAAO,SACPC,UAAU,EACVC,OAASa,IAEP,IAAKA,EACH,OACEV,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAACU,EAAAA,EAAS,CAACZ,UAAU,gCACrBE,EAAAA,EAAAA,KAAA,QAAAD,SAAM,eAKZ,IAAIY,EACJ,OAAOJ,GACL,IAAK,WACHI,GAAOX,EAAAA,EAAAA,KAACY,EAAAA,EAAe,CAACd,UAAU,gCAClC,MACF,IAAK,UACHa,GAAOX,EAAAA,EAAAA,KAACU,EAAAA,EAAS,CAACZ,UAAU,iCAC5B,MACF,IAAK,WACHa,GAAOX,EAAAA,EAAAA,KAACa,EAAAA,EAAW,CAACf,UAAU,8BAC9B,MACF,QACEa,GAAOX,EAAAA,EAAAA,KAACU,EAAAA,EAAS,CAACZ,UAAU,+BAEhC,OACED,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,CAC/BY,GACDX,EAAAA,EAAAA,KAAA,QAAAD,SAAOQ,EAAQA,EAAMO,OAAO,GAAGC,cAAgBR,EAAMS,MAAM,GAAK,cAC5D,GAIZ,CACEzB,IAAK,gBACLC,MAAO,iBACPC,UAAU,EACVC,OAASa,GAAU,IAAIU,KAAKV,GAAOW,sBAErC,CACE3B,IAAK,UACLC,MAAO,UACPE,OAAQA,CAACyB,EAAQvB,KACfC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,UACEF,UAAU,sEACVsB,QAAUC,IACRA,EAAEC,kBACFrC,EAAmBW,EAAa,EAElCR,MAAM,4BAA2BW,UAEjCC,EAAAA,EAAAA,KAACuB,EAAAA,EAAO,CAACzB,UAAU,eAErBE,EAAAA,EAAAA,KAAA,UACEF,UAAU,wEACVsB,QAAUC,IACRA,EAAEC,kBACFpC,EAAeU,EAAa,EAE9BR,MAAM,uBAAsBW,UAE5BC,EAAAA,EAAAA,KAACwB,EAAAA,EAAS,CAAC1B,UAAU,eAEvBE,EAAAA,EAAAA,KAAA,UACEF,UAAU,sEACVsB,QAAUC,IACRA,EAAEC,kBACFnC,EAAcS,EAAa,EAE7BR,MAAM,sBAAqBW,UAE3BC,EAAAA,EAAAA,KAACyB,EAAAA,EAAS,CAAC3B,UAAU,mBAO/B,OACEE,EAAAA,EAAAA,KAAC0B,EAAAA,EAAS,CACRpC,QAASA,EACTqC,KAAM3C,EACNI,MAAOA,EACPC,YAAaA,EACbuC,YAAY,EACZC,YAAY,GACZ,E,cC1JN,MAqFA,EArFgE9C,IAKzD,IAL0D,aAC/Da,EAAY,QACZkC,EAAO,UACPC,EAAS,SACTC,GACDjD,EACC,OACEc,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oCAAmCC,SAAC,yBAClDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,KAAGC,UAAU,wBAAuBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,cAAaC,SAAC,kBAAoB,IAAEH,EAAaO,gBAEnEN,EAAAA,EAAAA,MAAA,KAAGC,UAAU,wBAAuBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,cAAaC,SAAC,oBAAsB,IAAEH,EAAaqC,kBAErEpC,EAAAA,EAAAA,MAAA,KAAGC,UAAU,wBAAuBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,cAAaC,SAAC,WAAa,IAAEH,EAAasC,UAE5DrC,EAAAA,EAAAA,MAAA,KAAGC,UAAU,wBAAuBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,cAAaC,SAAC,WAAa,IAAEH,EAAauC,UAE5DtC,EAAAA,EAAAA,MAAA,KAAGC,UAAU,wBAAuBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,cAAaC,SAAC,oBAAsB,IAAE,IAAIkB,KAAKrB,EAAawC,eAAeC,2BAKjGxC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oCAAmCC,SAAC,yBAClDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2BAA0BC,SACrCH,EAAa0C,UAAUC,KAAI,CAACC,EAAKC,KAChCzC,EAAAA,EAAAA,KAAA,MAAgBF,UAAU,OAAMC,UAC9BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oCAAmCC,SAAEyC,EAAIpC,QACtDP,EAAAA,EAAAA,MAAA,KAAGC,UAAU,wBAAuBC,SAAA,CAAEyC,EAAIE,KAAK,mBAEjD1C,EAAAA,EAAAA,KAAC2C,EAAAA,EAAM,CACLC,QAAQ,UACRvC,KAAK,KACLwC,KAAML,EAAIM,IACVC,OAAO,SAAQhD,SAChB,aAXI0C,gBAsBnBzC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gCAA+BC,UAC5CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6BAA4BC,SAAA,EACzCC,EAAAA,EAAAA,KAAC2C,EAAAA,EAAM,CACLC,QAAQ,UACRxB,QAASU,EAAQ/B,SAClB,WAGDC,EAAAA,EAAAA,KAAC2C,EAAAA,EAAM,CACLC,QAAQ,UACRjC,MAAMX,EAAAA,EAAAA,KAACwB,EAAAA,EAAS,CAAC1B,UAAU,YAC3BsB,QAASW,EAAUhC,SACpB,aAGDC,EAAAA,EAAAA,KAAC2C,EAAAA,EAAM,CACLC,QAAQ,SACRjC,MAAMX,EAAAA,EAAAA,KAACyB,EAAAA,EAAS,CAAC3B,UAAU,YAC3BsB,QAASY,EAASjC,SACnB,kBAKD,ECxCV,EAzC0EhB,IAKnE,IALoE,OACzEiE,EAAM,QACNlB,EAAO,UACPC,EAAS,aACTnC,GACDb,EACC,OAAKa,GAGHC,EAAAA,EAAAA,MAACoD,EAAAA,EAAK,CACJD,OAAQA,EACRlB,QAASA,EACT1C,MAAM,mBACNiB,KAAK,KACL6C,QACErD,EAAAA,EAAAA,MAAAsD,EAAAA,SAAA,CAAApD,SAAA,EACEC,EAAAA,EAAAA,KAAC2C,EAAAA,EAAM,CACLC,QAAQ,UACRxB,QAASU,EAAQ/B,SAClB,YAGDC,EAAAA,EAAAA,KAAC2C,EAAAA,EAAM,CACLC,QAAQ,UACRxB,QAASW,EAAUhC,SACpB,eAIJA,SAAA,EAEDF,EAAAA,EAAAA,MAAA,KAAGC,UAAU,wBAAuBC,SAAA,CAAC,qCACFC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,cAAaC,SAAEH,EAAaO,cAAmB,+BAElGH,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BAA4BC,SAAC,wFA5BpB,IA+BhB,ECyBZ,EA5DwEhB,IAOjE,IAPkE,OACvEiE,EAAM,QACNlB,EAAO,SACPE,EAAQ,aACRpC,EAAY,aACZwD,EAAY,gBACZC,GACDtE,EACC,OAAKa,GAGHI,EAAAA,EAAAA,KAACiD,EAAAA,EAAK,CACJD,OAAQA,EACRlB,QAASA,EACT1C,MAAM,kBACNiB,KAAK,KACL6C,QACErD,EAAAA,EAAAA,MAAAsD,EAAAA,SAAA,CAAApD,SAAA,EACEC,EAAAA,EAAAA,KAAC2C,EAAAA,EAAM,CACLC,QAAQ,UACRxB,QAASU,EAAQ/B,SAClB,YAGDC,EAAAA,EAAAA,KAAC2C,EAAAA,EAAM,CACLC,QAAQ,SACRxB,QAASY,EAASjC,SACnB,cAIJA,UAEDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,KAAGC,UAAU,wBAAuBC,SAAA,CAAC,oCACHC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,cAAaC,SAAEH,EAAaO,cAAmB,+BAGjGN,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOsD,QAAQ,gBAAgBxD,UAAU,0CAAyCC,SAAC,0BAGnFC,EAAAA,EAAAA,KAAA,YACEM,GAAG,gBACHiD,KAAM,EACNzD,UAAU,gJACV0D,YAAY,wCACZjD,MAAO6C,EACPK,SAAWpC,GAAMgC,EAAgBhC,EAAE0B,OAAOxC,aAI9CP,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wBAAuBC,SAAC,6DA5CjB,IAgDhB,E,kCCjEL,MA8GP,EA9GgC,CAI9B2D,iBAAkBC,UAChB,IACE,MAAMC,QAAiBC,EAAAA,EAAUC,IAAoB,iBAAkB,CAAEC,WACzE,OAAOC,EAAAA,GAAmBC,QAAQL,EAAU,gBAC9C,CAAE,MAAOM,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFE,oBAAqBT,UACnB,IACE,MAAMC,QAAiBC,EAAAA,EAAUC,IAAkB,kBAAkBxD,KACrE,OAAO0D,EAAAA,GAAmBK,QAAQT,EAAU,eAAgBtD,EAC9D,CAAE,MAAO4D,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFI,mBAAoBX,UAClB,IACE,MAAMC,QAAiBC,EAAAA,EAAUU,KAAmB,iBAAkBC,GACtE,OAAOR,EAAAA,GAAmBS,OAAOb,EAAU,eAC7C,CAAE,MAAOM,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFQ,yBAA0Bf,MAAOrD,EAAYqE,KAC3C,IACE,MAAMf,QAAiBC,EAAAA,EAAUe,IAAkB,kBAAkBtE,WAAa,CAAEqE,WACpF,OAAOX,EAAAA,GAAmBa,OAAOjB,EAAU,eAAgBtD,EAC7D,CAAE,MAAO4D,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFY,yBAA0BnB,UACxB,IACE,MAAMC,QAAiBC,EAAAA,EAAUC,IAAoB,iBAAkB,CAAEC,OAAQ,CAAEY,YACnF,OAAOX,EAAAA,GAAmBC,QAAQL,EAAU,iBAAiB,EAC/D,CAAE,MAAOM,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFa,uBAAwBpB,UACtB,IACE,MAAMC,QAAiBC,EAAAA,EAAUC,IAAoB,iBAAkB,CAAEC,OAAQ,CAAEiB,YACnF,OAAOhB,EAAAA,GAAmBC,QAAQL,EAAU,iBAAiB,EAC/D,CAAE,MAAOM,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFe,mBAAoBtB,MAAOrD,EAAYqB,KACrC,IACE,MAAMiC,QAAiBC,EAAAA,EAAUe,IAAkB,kBAAkBtE,IAAMqB,GAC3E,OAAOqC,EAAAA,GAAmBa,OAAOjB,EAAU,eAAgBtD,EAC7D,CAAE,MAAO4D,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFgB,oBAAqBvB,MAAOrD,EAAY6E,KACtC,IACE,MAAMvB,QAAiBC,EAAAA,EAAUe,IAAkB,kBAAkBtE,YAAc,CAAE6E,UACrF,OAAOnB,EAAAA,GAAmBa,OAAOjB,EAAU,eAAgBtD,EAC7D,CAAE,MAAO4D,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFkB,mBAAoBzB,MAAOrD,EAAY6E,KACrC,IACE,MAAMvB,QAAiBC,EAAAA,EAAUe,IAAkB,kBAAkBtE,WAAa,CAAE6E,UACpF,OAAOnB,EAAAA,GAAmBa,OAAOjB,EAAU,eAAgBtD,EAC7D,CAAE,MAAO4D,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,IC+DJ,EAzKgCmB,KAC9B,MAAOrG,EAAesG,IAAoBC,EAAAA,EAAAA,UAAyB,KAC5DC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCrB,EAAOwB,IAAYH,EAAAA,EAAAA,UAAuB,OAC3C,iBAAEI,IAAqBC,EAAAA,EAAAA,KAGvBC,GAAsBC,EAAAA,EAAAA,QAAOH,GAC7BI,GAAoBD,EAAAA,EAAAA,SAAO,IAGjCE,EAAAA,EAAAA,YAAU,KACRH,EAAoBI,QAAUN,CAAgB,IAIhD,MAAMO,GAAqBC,EAAAA,EAAAA,cAAYxC,UACrC8B,GAAa,GACbC,EAAS,MACT,IACE,MAAM/D,QAAayE,EAAiB1C,mBACpC4B,EAAiB3D,EACnB,CAAE,MAAO0E,GACPX,EAASW,GACTR,EAAoBI,QAAQ,CAC1BvD,KAAM,QACNtD,MAAO,QACPkH,QAAS,iCAEb,CAAC,QACCb,GAAa,EACf,IACC,IAGGrB,GAAsB+B,EAAAA,EAAAA,cAAYxC,UACtC8B,GAAa,GACbC,EAAS,MACT,IAEE,aAD2BU,EAAiBhC,oBAAoB9D,EAElE,CAAE,MAAO+F,GAOP,MANAX,EAASW,GACTR,EAAoBI,QAAQ,CAC1BvD,KAAM,QACNtD,MAAO,QACPkH,QAAS,gCAAgChG,MAErC+F,CACR,CAAC,QACCZ,GAAa,EACf,IACC,IAGGX,GAA2BqB,EAAAA,EAAAA,cAAYxC,UAC3C8B,GAAa,GACbC,EAAS,MACT,IAEE,aADoCU,EAAiBtB,yBAAyBH,EAEhF,CAAE,MAAO0B,GAOP,MANAX,EAASW,GACTR,EAAoBI,QAAQ,CAC1BvD,KAAM,QACNtD,MAAO,QACPkH,QAAS,6CAA6C3B,MAElD0B,CACR,CAAC,QACCZ,GAAa,EACf,IACC,IAGGR,GAAqBkB,EAAAA,EAAAA,cAAYxC,MAAOrD,EAAYqB,KACxD8D,GAAa,GACbC,EAAS,MACT,IACE,MAAMa,QAA4BH,EAAiBnB,mBAAmB3E,EAAIqB,GAS1E,OARA2D,GAAiBkB,GACfA,EAAkBjE,KAAI3C,GAAgBA,EAAaU,KAAOA,EAAKiG,EAAsB3G,MAEvFiG,EAAoBI,QAAQ,CAC1BvD,KAAM,UACNtD,MAAO,UACPkH,QAAS,sCAEJC,CACT,CAAE,MAAOF,GAOP,MANAX,EAASW,GACTR,EAAoBI,QAAQ,CAC1BvD,KAAM,QACNtD,MAAO,QACPkH,QAAS,kCAELD,CACR,CAAC,QACCZ,GAAa,EACf,IACC,IAGGP,GAAsBiB,EAAAA,EAAAA,cAAYxC,eAAOrD,GAAoC,IAAxB6E,EAAasB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACzEhB,GAAa,GACbC,EAAS,MACT,IACE,MAAMkB,QAA6BR,EAAiBlB,oBAAoB5E,EAAI6E,GAI5E,OAHAG,GAAiBkB,GACfA,EAAkBjE,KAAI3C,GAAgBA,EAAaU,KAAOA,EAAKsG,EAAuBhH,MAEjFgH,CACT,CAAE,MAAOP,GAOP,MANAX,EAASW,GACTR,EAAoBI,QAAQ,CAC1BvD,KAAM,QACNtD,MAAO,QACPkH,QAAS,mCAELD,CACR,CAAC,QACCZ,GAAa,EACf,CACF,GAAG,IAGGL,GAAqBe,EAAAA,EAAAA,cAAYxC,MAAOrD,EAAY6E,KACxDM,GAAa,GACbC,EAAS,MACT,IACE,MAAMmB,QAA6BT,EAAiBhB,mBAAmB9E,EAAI6E,GAI3E,OAHAG,GAAiBkB,GACfA,EAAkBjE,KAAI3C,GAAgBA,EAAaU,KAAOA,EAAKuG,EAAuBjH,MAEjFiH,CACT,CAAE,MAAOR,GAOP,MANAX,EAASW,GACTR,EAAoBI,QAAQ,CAC1BvD,KAAM,QACNtD,MAAO,QACPkH,QAAS,kCAELD,CACR,CAAC,QACCZ,GAAa,EACf,IACC,IAUH,OAPAO,EAAAA,EAAAA,YAAU,KACHD,EAAkBE,UACrBF,EAAkBE,SAAU,EAC5BC,IACF,GACC,IAEI,CACLlH,gBACAwG,YACAtB,QACAgC,qBACA9B,sBACAU,2BACAG,qBACAC,sBACAE,qBACD,ECEH,EA9JoC0B,KAElC,MAAM,cAAE9H,EAAa,UAAEwG,EAAS,oBAAEN,EAAmB,mBAAEE,GAAuBC,KAExE,YAAE0B,EAAW,UAAEC,IAAcpB,EAAAA,EAAAA,MAC5BqB,EAAsBC,IAA2B3B,EAAAA,EAAAA,UAA8B,OAC/E4B,EAAiBC,IAAsB7B,EAAAA,EAAAA,WAAS,IAChD8B,EAAoBC,IAAyB/B,EAAAA,EAAAA,WAAS,IACtDgC,EAAmBC,IAAwBjC,EAAAA,EAAAA,WAAS,IACpDnC,EAAcC,IAAmBkC,EAAAA,EAAAA,UAAS,KAG1CkC,EAA0BC,IAA+BnC,EAAAA,EAAAA,UAAsB,IAAIoC,KAGpFC,GAAyBC,EAAAA,EAAAA,UAAQ,IAC9B7I,EAAc8I,QAAOlI,IACzB6H,EAAyBM,IAAInI,EAAaU,KAA+B,YAAxBV,EAAa+E,UAEhE,CAAC3F,EAAeyI,IA+DnB,OACE5H,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAACgI,EAAAA,EAAU,CACT5I,MAAM,yBACNC,YAAY,mDACZ4I,YAAa,CAAC,CAAEzI,MAAO,qBAGzBQ,EAAAA,EAAAA,KAACkI,EAAAA,EAAI,CAAAnI,SACFyF,GACCxF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2BAA0BC,UACvCC,EAAAA,EAAAA,KAACmI,EAAAA,EAAc,MAEmB,IAAlCP,EAAuBlB,QACzB7G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6BAA4BC,SAAC,8BAC5CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wBAAuBC,SAAC,oFAKzCC,EAAAA,EAAAA,KAACoI,EAAgB,CACfpJ,cAAe4I,EACf3I,mBAlFsBW,IAC9BsH,EAAwBtH,GACxBwH,GAAmB,EAAK,EAiFhBlI,eA9EkBU,IAC1BsH,EAAwBtH,GACxB0H,GAAsB,EAAK,EA6EnBnI,cA1EiBS,IACzBsH,EAAwBtH,GACxB4H,GAAqB,EAAK,EAyElBpI,MAAM,wBACNC,YAAY,mDAMlBW,EAAAA,EAAAA,KAACiD,EAAAA,EAAK,CACJD,OAAQmE,EACRrF,QAASA,IAAMsF,GAAmB,GAClChI,MAAM,uBACNiB,KAAK,KAAIN,SAERkH,IACCjH,EAAAA,EAAAA,KAACqI,EAAmB,CAClBzI,aAAcqH,EACdnF,QAASA,IAAMsF,GAAmB,GAClCrF,UAAWA,KACTqF,GAAmB,GACnBE,GAAsB,EAAK,EAE7BtF,SAAUA,KACRoF,GAAmB,GACnBI,GAAqB,EAAK,OAOlCxH,EAAAA,EAAAA,KAACsI,EAAwB,CACvBtF,OAAQqE,EACRvF,QAASA,IAAMwF,GAAsB,GACrCvF,UAvGgB4B,UACpB,GAAKsD,EAEL,UACQ/B,EAAoB+B,EAAqB3G,IAG/CoH,GAA4Ba,GAAQ,IAAIZ,IAAIY,GAAMC,IAAIvB,EAAqB3G,MAE3EyG,EAAY,GAAGE,EAAqB9G,iCACpCmH,GAAsB,GACtBJ,EAAwB,KAC1B,CAAE,MAAOhD,GACPuE,QAAQvE,MAAM,gCAAiCA,EAGjD,GAwFItE,aAAcqH,KAIhBjH,EAAAA,EAAAA,KAAC0I,EAAuB,CACtB1F,OAAQuE,EACRzF,QAASA,IAAM0F,GAAqB,GACpCxF,SA5Fe2B,UACnB,GAAKP,EAAauF,QAKlB,GAAK1B,EAEL,UACQ7B,EAAmB6B,EAAqB3G,GAAI8C,GAGlDsE,GAA4Ba,GAAQ,IAAIZ,IAAIY,GAAMC,IAAIvB,EAAqB3G,MAE3EyG,EAAY,GAAGE,EAAqB9G,iCACpCqH,GAAqB,GACrBnE,EAAgB,IAChB6D,EAAwB,KAC1B,CAAE,MAAOhD,GACPuE,QAAQvE,MAAM,gCAAiCA,EAGjD,OApBE8C,EAAU,wCAoBZ,EAuEIpH,aAAcqH,EACd7D,aAAcA,EACdC,gBAAiBA,MAEf,C,uFCtJV,MAAM2E,EAAwCjJ,IAOvC,IAPwC,MAC7CK,EAAK,YACLC,EAAW,QACXuJ,EAAO,YACPX,EAAW,UACXnI,EAAY,GAAE,OACd+I,GACD9J,EACC,OACEc,EAAAA,EAAAA,MAAA,OACEC,UAAW,QAAQA,IACnB,cAAa+I,EAAO9I,SAAA,CAGnBkI,GAAeA,EAAYvB,OAAS,IACnC1G,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAY,aAAW,aAAYC,UAChDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oDAAmDC,SAAA,EAC/DC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAAC8I,EAAAA,GAAI,CACHC,GAAG,IACHjJ,UAAU,uCACV,aAAW,OAAMC,UAEjBC,EAAAA,EAAAA,KAACgJ,EAAAA,EAAQ,CAAClJ,UAAU,gBAIvBmI,EAAY1F,KAAI,CAAC0G,EAAMxG,KACtB5C,EAAAA,EAAAA,MAAA,MAAgBC,UAAU,oBAAmBC,SAAA,EAC3CC,EAAAA,EAAAA,KAACkJ,EAAAA,EAAgB,CAACpJ,UAAU,+BAC3BmJ,EAAKE,MAAQ1G,EAAQwF,EAAYvB,OAAS,GACzC1G,EAAAA,EAAAA,KAAC8I,EAAAA,GAAI,CACHC,GAAIE,EAAKE,KACTrJ,UAAU,qBAAoBC,SAE7BkJ,EAAKzJ,SAGRQ,EAAAA,EAAAA,KAAA,QAAMF,UAAU,4BAA2BC,SAAEkJ,EAAKzJ,UAV7CiD,WAmBjB5C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8EAA6EC,SAAA,EAC1FF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mCAAkCC,SAAEX,IACjDC,GAAsC,kBAAhBA,GACrBW,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BAA4BC,SAAEV,IAE3CA,KAIHuJ,IACC5I,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oCAAmCC,SAC/C6I,SAIH,EAIV,GAAeQ,EAAAA,EAAAA,MAAKpB,E,yDCjEpB,MAAME,EAA4BnJ,IAgB3B,IAhB4B,MACjCK,EAAK,SACLiK,EAAQ,SACRtJ,EAAQ,UACRD,EAAY,GAAE,cACdwJ,EAAgB,GAAE,gBAClBC,EAAkB,GAAE,gBACpBC,EAAkB,GAAE,KACpB7I,EAAI,OACJuC,EAAM,QACN9B,EAAO,UACPqI,GAAY,EAAK,UACjBC,GAAY,EAAK,SACjBC,GAAW,EAAI,QACfC,GAAU,EAAK,OACff,GACD9J,EAEC,MAAM8K,EAAc,6BACIF,EAAW,yBAA2B,uDAC1DF,EAAY,uEAAyE,oBACrFrI,EAAU,iBAAmB,WAC7BtB,QAIEgK,EAAgB,mFAElBP,QAIEQ,EAAc,SAChBL,EAAY,GAAK,cACjBJ,QAIEU,EAAgB,4DAElBR,QAIJ,OAAII,GAEA/J,EAAAA,EAAAA,MAAA,OAAKC,UAAW+J,EAAa,cAAahB,EAAO9I,SAAA,EAC7CX,GAASiK,GAAY1I,KACrBd,EAAAA,EAAAA,MAAA,OAAKC,UAAWgK,EAAc/J,SAAA,EAC5BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,SAAQC,SAAA,CACpBX,IAASY,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gDACxBuJ,IAAYrJ,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wDAE7Ba,IAAQX,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAI5BE,EAAAA,EAAAA,KAAA,OAAKF,UAAWiK,EAAYhK,UAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6CAGhBoD,IACClD,EAAAA,EAAAA,KAAA,OAAKF,UAAWkK,EAAcjK,UAC5BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sDAQvBD,EAAAA,EAAAA,MAAA,OACEC,UAAW+J,EACXzI,QAASA,EACT,cAAayH,EAAO9I,SAAA,EAElBX,GAASiK,GAAY1I,KACrBd,EAAAA,EAAAA,MAAA,OAAKC,UAAWgK,EAAc/J,SAAA,EAC5BF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,CACoB,kBAAVX,GACNY,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qCAAoCC,SAAEX,IAEpDA,EAEmB,kBAAbiK,GACNrJ,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BAA4BC,SAAEsJ,IAE3CA,KAGH1I,IAAQX,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcC,SAAEY,QAI5CX,EAAAA,EAAAA,KAAA,OAAKF,UAAWiK,EAAYhK,SAAEA,IAE7BmD,IACClD,EAAAA,EAAAA,KAAA,OAAKF,UAAWkK,EAAcjK,SAC3BmD,MAGD,EAIV,GAAekG,EAAAA,EAAAA,MAAKlB,E,uDCxHpB,MAsGA,EAtGsDnJ,IAM/C,IANgD,KACrDsB,EAAO,KAAI,UACXP,EAAY,GAAE,QACd8C,EAAU,UAAS,MACnBqH,EAAQ,UAAS,gBACjBC,GAAkB,GACnBnL,EACC,MAAMoL,EAAU,CACdC,GAAI,CAAEC,QAAS,UAAWC,KAAM,UAAWC,MAAO,UAAWC,OAAQ,WACrEC,GAAI,CAAEJ,QAAS,UAAWC,KAAM,cAAeC,MAAO,UAAWC,OAAQ,aACzEE,GAAI,CAAEL,QAAS,YAAaC,KAAM,UAAWC,MAAO,UAAWC,OAAQ,cAGnEG,EAAeT,EAAkB,eAAiBD,EAGxD,MAAgB,YAAZrH,GAEA/C,EAAAA,EAAAA,MAAA,OACEC,UAAW,oCAAoCA,IAC/C8K,KAAK,SACL,aAAW,UAAS7K,SAAA,EAEpBC,EAAAA,EAAAA,KAAA,OACEF,UAAW,wDAAwDqK,EAAQ9J,GAAMgK,UACjFQ,MAAO,CACLC,eAAgBH,EAChBI,iBAAkBJ,MAGtB3K,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASC,SAAC,kBAMhB,SAAZ6C,GAEA/C,EAAAA,EAAAA,MAAA,OACEC,UAAW,0DAA0DA,IACrE8K,KAAK,SACL,aAAW,UAAS7K,SAAA,EAEpBC,EAAAA,EAAAA,KAAA,OACEF,UAAW,GAAGqK,EAAQ9J,GAAMiK,wBAC5BO,MAAO,CAAEG,gBAAiBL,MAE5B3K,EAAAA,EAAAA,KAAA,OACEF,UAAW,GAAGqK,EAAQ9J,GAAMiK,wBAC5BO,MAAO,CAAEG,gBAAiBL,MAE5B3K,EAAAA,EAAAA,KAAA,OACEF,UAAW,GAAGqK,EAAQ9J,GAAMiK,wBAC5BO,MAAO,CAAEG,gBAAiBL,MAE5B3K,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASC,SAAC,kBAMhB,UAAZ6C,GAEA/C,EAAAA,EAAAA,MAAA,OACEC,UAAW,oCAAoCA,IAC/C8K,KAAK,SACL,aAAW,UAAS7K,SAAA,EAEpBC,EAAAA,EAAAA,KAAA,OACEF,UAAW,GAAGqK,EAAQ9J,GAAMkK,kCAC5BM,MAAO,CAAEG,gBAAiBL,MAE5B3K,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASC,SAAC,kBAMhB,WAAZ6C,GAEA/C,EAAAA,EAAAA,MAAA,OACEC,UAAW,oCAAoCA,IAC/C8K,KAAK,SACL,aAAW,UAAS7K,SAAA,EAEpBC,EAAAA,EAAAA,KAAA,OACEF,UAAW,GAAGqK,EAAQ9J,GAAMmK,oCAC5BK,MAAO,CAAEZ,MAAOU,GAAe5K,UAE/BC,EAAAA,EAAAA,KAAA,OACEF,UAAW,GAAGqK,EAAQ9J,GAAMkK,0CAC5BM,MAAO,CAAEG,gBAAiBL,QAG9B3K,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASC,SAAC,kBAKzB,IAAI,C,gDC9Gb,SAASmJ,EAAgBnK,EAItBkM,GAAQ,IAJe,MACxB7L,EAAK,QACL8L,KACGC,GACJpM,EACC,OAAoBqM,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ/L,EAAqBgM,EAAAA,cAAoB,QAAS,CAC3D9K,GAAI4K,GACH9L,GAAS,KAAmBgM,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,8BAEP,CACA,MACA,EADiCX,EAAAA,WAAiBlC,E,gDCvBlD,SAASxI,EAAS3B,EAIfkM,GAAQ,IAJQ,MACjB7L,EAAK,QACL8L,KACGC,GACJpM,EACC,OAAoBqM,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ/L,EAAqBgM,EAAAA,cAAoB,QAAS,CAC3D9K,GAAI4K,GACH9L,GAAS,KAAmBgM,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,qDAEP,CACA,MACA,EADiCX,EAAAA,WAAiB1K,E,gDCvBlD,SAASG,EAAW9B,EAIjBkM,GAAQ,IAJU,MACnB7L,EAAK,QACL8L,KACGC,GACJpM,EACC,OAAoBqM,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ/L,EAAqBgM,EAAAA,cAAoB,QAAS,CAC3D9K,GAAI4K,GACH9L,GAAS,KAAmBgM,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,0EAEP,CACA,MACA,EADiCX,EAAAA,WAAiBvK,E,gDCvBlD,SAASmL,EAAmBjN,EAIzBkM,GAAQ,IAJkB,MAC3B7L,EAAK,QACL8L,KACGC,GACJpM,EACC,OAAoBqM,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ/L,EAAqBgM,EAAAA,cAAoB,QAAS,CAC3D9K,GAAI4K,GACH9L,GAAS,KAAmBgM,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,kFAEP,CACA,MACA,EADiCX,EAAAA,WAAiBY,GCvBlD,SAASC,EAAalN,EAInBkM,GAAQ,IAJY,MACrB7L,EAAK,QACL8L,KACGC,GACJpM,EACC,OAAoBqM,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ/L,EAAqBgM,EAAAA,cAAoB,QAAS,CAC3D9K,GAAI4K,GACH9L,GAAS,KAAmBgM,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,+BAEP,CACA,MACA,EADiCX,EAAAA,WAAiBa,G,2CCoBlD,SAASvK,EAAS3C,GAqBK,IArB2B,QAChDO,EAAO,KACPqC,EAAI,WACJuK,EAAU,MACV9M,EAAK,YACLC,EAAW,QACXuK,GAAU,EAAK,WACfhI,GAAa,EAAI,SACjBuK,EAAWC,EAAAA,GAAOC,kBAAiB,WACnCxK,GAAa,EAAI,kBACjByK,EAAiB,QACjB1D,EAAO,aACP2D,EAAe,mBAAkB,UACjCzM,EAAY,GAAE,gBACdyJ,EAAkB,GAAE,cACpBD,EAAgB,GAAE,gBAClBE,EAAkB,GAAE,aACpBgD,EAAY,eACZC,EAAc,qBACdC,EAAuB,MAAK,OAC5B7D,GACkB9J,EAElB,MAAO4N,EAAYC,IAAiBrH,EAAAA,EAAAA,UAG1BkH,EAAiB,CAAElN,IAAKkN,EAAgBI,UAAWH,GAAyB,OAE/EI,EAAYC,IAAiBxH,EAAAA,EAAAA,UAAS,KACtCyH,EAAaC,IAAkB1H,EAAAA,EAAAA,UAAS,IACxC2H,EAAcC,IAAmB5H,EAAAA,EAAAA,UAAmB,KACpD6H,EAAYC,IAAiB9H,EAAAA,EAAAA,UAAwB,MAWtD+H,GAAazF,EAAAA,EAAAA,UAAQ,IACpB8E,EAEE,IAAIhL,GAAM4L,MAAK,CAACC,EAAGC,KACxB,MAAMC,EAASF,EAAEb,EAAWpN,KACtBoO,EAASF,EAAEd,EAAWpN,KAG5B,OAAc,MAAVmO,GAA4B,MAAVC,EAAuB,EAC/B,MAAVD,EAAgD,QAAzBf,EAAWE,WAAuB,EAAI,EACnD,MAAVc,EAAgD,QAAzBhB,EAAWE,UAAsB,GAAK,EAG3C,kBAAXa,GAAyC,kBAAXC,EACP,QAAzBhB,EAAWE,UACda,EAAOE,cAAcD,GACrBA,EAAOC,cAAcF,GAGvBA,EAASC,EACqB,QAAzBhB,EAAWE,WAAuB,EAAI,EAE3Ca,EAASC,EACqB,QAAzBhB,EAAWE,UAAsB,GAAK,EAExC,CAAC,IAxBclL,GA0BvB,CAACA,EAAMgL,IAGJkB,GAAehG,EAAAA,EAAAA,UAAQ,IACtBiF,EAEEQ,EAAWxF,QAAQgG,GACxBzC,OAAO0C,QAAQD,GAAKE,MAAKC,IAAoB,IAAlBC,EAAM3N,GAAM0N,EAErC,OAAc,OAAV1N,QAA4BoG,IAAVpG,IACD,kBAAVA,GAEJ4N,OAAO5N,GAAO6N,cAAcC,SAASvB,EAAWsB,eAAc,MARjDd,GAWvB,CAACA,EAAYR,IAGVwB,EAAaC,KAAKC,KAAKX,EAAanH,OAASyF,GAC7CsC,GAAgB5G,EAAAA,EAAAA,UAAQ,KAC5B,MAAM6G,GAAc1B,EAAc,GAAKb,EACvC,OAAO0B,EAAa7M,MAAM0N,EAAYA,EAAavC,EAAS,GAC3D,CAAC0B,EAAcb,EAAab,IAEzBwC,EAAoBC,IACxB3B,EAAe2B,EAAK,EA0ChBC,EAAqBlK,IACzB,IAAImK,EAAU,4BAEd,GAAsB,kBAAXnK,EAAqB,CAC9B,MAAMoK,EAAcpK,EAAOyJ,cAEvBW,EAAYV,SAAS,WAAaU,EAAYV,SAAS,aACvDU,EAAYV,SAAS,aAAeU,EAAYV,SAAS,cACzDU,EAAYV,SAAS,WACvBS,EAAU,8BACDC,EAAYV,SAAS,YAAcU,EAAYV,SAAS,cACjES,EAAU,gCACDC,EAAYV,SAAS,aAAeU,EAAYV,SAAS,WAC1DU,EAAYV,SAAS,WAAaU,EAAYV,SAAS,SAC/DS,EAAU,0BACDC,EAAYV,SAAS,cAC9BS,EAAU,4BAEd,CAEA,OACE9O,EAAAA,EAAAA,KAAA,QAAMF,UAAW,2EAA2EgP,IAAU/O,SACnG4E,GACI,EAIX,OACE9E,EAAAA,EAAAA,MAAA,OACEC,UAAW,oHAAoHA,IAC/H,cAAa+I,EAAO9I,SAAA,EAGlBX,GAASC,KACTQ,EAAAA,EAAAA,MAAA,OAAKC,UAAW,sCAAsCyJ,IAAkBxJ,SAAA,CACpD,kBAAVX,GACNY,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sCAAqCC,SAAEX,IAErDA,EAEsB,kBAAhBC,GACNW,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BAA4BC,SAAEV,IAE3CA,MAMNQ,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kGAAiGC,SAAA,EAC9GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kBAAiBC,SAAA,EAC9BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uEAAsEC,UACnFC,EAAAA,EAAAA,KAACgM,EAAmB,CAAClM,UAAU,6BAEjCE,EAAAA,EAAAA,KAAA,SACE0C,KAAK,OACLc,YAAY,YACZ1D,UAAU,sMACVS,MAAOuM,EACPrJ,SAAWpC,IACT0L,EAAc1L,EAAE0B,OAAOxC,OACvB0M,EAAe,EAAE,EAEnB,cAAa,GAAGpE,iBAIpBhJ,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,CACzCmN,EAAaxG,OAAS,IACrB7G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,wBAAuBC,SAAA,CAAEmN,EAAaxG,OAAO,gBAC7D1G,EAAAA,EAAAA,KAAA,UACEF,UAAU,uGACVsB,QAASA,KACP+L,EAAgB,IACZb,GAAmBA,EAAkB,GAAG,EAE9C,cAAa,GAAGzD,oBAAyB9I,SAC1C,aAKJ6I,SAKL5I,EAAAA,EAAAA,KAAA,OAAKF,UAAW,mBAAmBwJ,IAAgBvJ,SAChD6J,GACC5J,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCAAwCC,UACrDC,EAAAA,EAAAA,KAACmI,EAAAA,EAAc,CAAC9H,KAAK,KAAKuC,QAAQ,eAGpC/C,EAAAA,EAAAA,MAAA,SAAOC,UAAU,sCAAqCC,SAAA,EACpDC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,aAAYC,UAC3BF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CACG8B,IACC7B,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iBAAgBC,UAC5BC,EAAAA,EAAAA,KAAA,SACE0C,KAAK,WACL5C,UAAU,kEACV2D,SAtHKuL,IACvB,MAAMC,EAAkBD,EAAMjM,OAAOmM,QACjCC,MAAMC,KAAK,CAAE1I,OAAQ+H,EAAc/H,SAAU,CAACvF,EAAGkO,IAAMA,IACvD,GAIJ,GAFAlC,EAAgB8B,GAEZ3C,EAAmB,CACrB,MAAMgD,EAAgBL,EACnB1M,KAAIgN,GAAOd,EAAcc,KACzBzH,QAAQmB,QAA6BtC,IAATsC,IAC/BqD,EAAkBgD,EACpB,GA2GkBJ,QAAShC,EAAaxG,SAAW+H,EAAc/H,QAAU+H,EAAc/H,OAAS,EAChF,cAAa,GAAGmC,mBAIrBvJ,EAAQiD,KAAKiN,IACZxP,EAAAA,EAAAA,KAAA,MAEEF,UAAW,kBAAkB0P,EAAOC,OAAS,qEAAqED,EAAO/P,SAAW,mCAAqC,qCAAqC+P,EAAOE,MAAQF,EAAOE,MAAQ,MAAMF,EAAO1P,WAAa,KACtQsB,QAASA,IAAMoO,EAAO/P,UAtNpBF,KAClB,IAAIsN,EAA4B,MAC5BF,GAAcA,EAAWpN,MAAQA,GAAgC,QAAzBoN,EAAWE,YACrDA,EAAY,QAEdD,EAAc,CAAErN,MAAKsN,aAAY,EAiNiB8C,CAAWH,EAAOjQ,KACpD,cAAa,GAAGsJ,YAAiB2G,EAAOjQ,MAAMQ,UAE9CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,QAAAD,SAAOyP,EAAOhQ,QACbgQ,EAAO/P,WACNO,EAAAA,EAAAA,KAAA,QAAMF,UAAW,oCACL,OAAV6M,QAAU,IAAVA,OAAU,EAAVA,EAAYpN,OAAQiQ,EAAOjQ,IAAM,eAAiB,iBACjDQ,UACU,OAAV4M,QAAU,IAAVA,OAAU,EAAVA,EAAYpN,OAAQiQ,EAAOjQ,KAAgC,QAAzBoN,EAAWE,WAC1C7M,EAAAA,EAAAA,KAACiM,EAAa,CAACnM,UAAU,aACf,OAAV6M,QAAU,IAAVA,OAAU,EAAVA,EAAYpN,OAAQiQ,EAAOjQ,KAAgC,SAAzBoN,EAAWE,WAC3C7M,EAAAA,EAAAA,KAAC4P,EAAAA,EAAe,CAAC9P,UAAU,aAC3BE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,gBAAeC,SAAC,iBAfvCyP,EAAOjQ,aAuBpBS,EAAAA,EAAAA,KAAA,SAAOF,UAAU,oCAAmCC,SACjD0O,EAAc/H,OAAS,EACtB+H,EAAclM,KAAI,CAACuL,EAAKrL,KACtB5C,EAAAA,EAAAA,MAAA,MAEEC,UAAW,qCACToM,EAAa,iBAAmB,MAC9BgB,EAAamB,SAAS5L,GAAS,0BAA4B,2BAC7D2K,IAAe3K,EAAQ,aAAe,2BACtC+J,EAAeA,EAAasB,EAAKrL,GAAS,KAC5CrB,QAASA,IAAM8K,GAAcA,EAAW4B,GACxC+B,aAAcA,IAAMxC,EAAc5K,GAClCqN,aAAcA,IAAMzC,EAAc,MAClC,cAAa,GAAGxE,SAAcpG,IAAQ1C,SAAA,CAErC8B,IACC7B,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,UACzCC,EAAAA,EAAAA,KAAA,SACE0C,KAAK,WACL5C,UAAU,kEACVoP,QAAShC,EAAamB,SAAS5L,GAC/BgB,SAAUA,OACVrC,QAAUC,GAjMV0O,EAACtN,EAAeuM,KACtCA,EAAM1N,kBAEN,MAAM2N,EAAkB,IAAI/B,GAE5B,GAAIA,EAAamB,SAAS5L,GAAQ,CAChC,MAAM8M,EAAMN,EAAgBe,QAAQvN,GACpCwM,EAAgBgB,OAAOV,EAAK,EAC9B,MACEN,EAAgBiB,KAAKzN,GAKvB,GAFA0K,EAAgB8B,GAEZ3C,EAAmB,CACrB,MAAMgD,EAAgBL,EACnB1M,KAAIgN,GAAOd,EAAcc,KACzBzH,QAAQmB,QAA6BtC,IAATsC,IAC/BqD,EAAkBgD,EACpB,GA8KsCS,CAAgBtN,EAAOpB,GACvC,cAAa,GAAGwH,SAAcpG,iBAInCnD,EAAQiD,KAAKiN,IACZxP,EAAAA,EAAAA,KAAA,MAEEF,UAAW,oFAAoF0P,EAAOC,OAAS,UAAUD,EAAO1P,WAAa,KAC7I,cAAa,GAAG+I,SAAcpG,UAAc+M,EAAOjQ,MAAMQ,SAExDyP,EAAO9P,OACJ8P,EAAO9P,OAAOoO,EAAI0B,EAAOjQ,KAAMuO,GAC/B0B,EAAOjQ,IAAI6O,cAAcC,SAAS,UAChCQ,EAAkBf,EAAI0B,EAAOjQ,MAC7BuO,EAAI0B,EAAOjQ,MARZiQ,EAAOjQ,SAzBXkD,MAuCTzC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAAA,MACEmQ,QAAS7Q,EAAQoH,QAAU7E,EAAa,EAAI,GAC5C/B,UAAU,uCACV,cAAa,GAAG+I,kBAAuB9I,SAEtCwM,aAUd3K,GAAc0M,EAAa,IAC1BzO,EAAAA,EAAAA,MAAA,OAAKC,UAAW,wEAAwE0J,IAAkBzJ,SAAA,EACxGF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,CAAC,YAC1BiN,EAAc,GAAKb,EAAY,EAAE,OAAKoC,KAAK6B,IAAIpD,EAAcb,EAAU0B,EAAanH,QAAQ,OAAKmH,EAAanH,OAAO,eAElI7G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,UACEoB,QAASA,IAAMuN,EAAiBJ,KAAK8B,IAAI,EAAGrD,EAAc,IAC1DsD,SAA0B,IAAhBtD,EACVlN,UAAW,iCACO,IAAhBkN,EACI,mCACA,mCAEN,cAAa,GAAGnE,oBAAyB9I,SAC1C,aAGAoP,MAAMC,KAAK,CAAE1I,OAAQ6H,KAAK6B,IAAI,EAAG9B,KAAe,CAACnN,EAAGkO,KAEnD,IAAIkB,EAWJ,OATEA,EADEjC,GAAc,GAEPtB,GAAe,EADdqC,EAAI,EAGLrC,GAAesB,EAAa,EAC3BA,EAAa,EAAIe,EAEjBrC,EAAc,EAAIqC,GAI5BrP,EAAAA,EAAAA,KAAA,UAEEoB,QAASA,IAAMuN,EAAiB4B,GAChCzQ,UAAW,iCACTkN,IAAgBuD,EACZ,wBACA,mCAEN,cAAa,GAAG1H,gBAAqB0H,IAAUxQ,SAE9CwQ,GATIA,EAUE,KAGbvQ,EAAAA,EAAAA,KAAA,UACEoB,QAASA,IAAMuN,EAAiBJ,KAAK6B,IAAI9B,EAAYtB,EAAc,IACnEsD,SAAUtD,IAAgBsB,EAC1BxO,UAAW,iCACTkN,IAAgBsB,EACZ,mCACA,mCAEN,cAAa,GAAGzF,oBAAyB9I,SAC1C,iBAQb,CAEA,SAAeqJ,EAAAA,EAAAA,MAAK1H,E,yDCpZpB,MAAMiB,EAAgC5D,IAmB/B,IAnBgC,SACrCgB,EAAQ,QACR6C,EAAU,UAAS,KACnBvC,EAAO,KAAI,UACXP,EAAY,GAAE,QACdsB,EAAO,SACPkP,GAAW,EAAK,KAChB5N,EAAO,SAAQ,KACf/B,EAAI,aACJ6P,EAAe,OAAM,UACrBC,GAAY,EAAK,QACjB7G,GAAU,EAAK,QACf8G,GAAU,EAAK,KACf7N,EAAI,OACJE,EAAM,IACN4N,EAAG,MACHvR,EAAK,UACLwR,EAAS,OACT/H,GACD9J,EACC,MAwBM8R,EAAgB,kKAtBC,CACrBC,QAAS,uEACTC,UAAW,0EACXC,QAAS,4FACTC,OAAQ,oEACRC,QAAS,0EACTC,KAAM,2EACNC,KAAM,kFAiBWxO,WAdC,CAClByO,GAAI,oBACJjH,GAAI,sBACJK,GAAI,oBACJC,GAAI,wBACJ4G,GAAI,qBAUUjR,WAPQiQ,EAAW,gCAAkC,yBAClDG,EAAY,SAAW,WACrBC,EAAU,eAAiB,qBAS5C5Q,QAGEyR,GACJ1R,EAAAA,EAAAA,MAAAsD,EAAAA,SAAA,CAAApD,SAAA,CACG6J,IACC/J,EAAAA,EAAAA,MAAA,OACEC,UAAU,+CACVyL,MAAM,6BACNC,KAAK,OACLC,QAAQ,YACR,cAAY,OAAM1L,SAAA,EAElBC,EAAAA,EAAAA,KAAA,UACEF,UAAU,aACV0R,GAAG,KACHC,GAAG,KACHC,EAAE,KACF/F,OAAO,eACPD,YAAY,OAEd1L,EAAAA,EAAAA,KAAA,QACEF,UAAU,aACV0L,KAAK,eACLO,EAAE,uHAKPpL,GAAyB,SAAjB6P,IAA4B5G,IACnC5J,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAEY,IAGzBZ,EAEAY,GAAyB,UAAjB6P,IACPxQ,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAEY,OAM9B,OAAIkC,GAEA7C,EAAAA,EAAAA,KAAA,KACE6C,KAAMA,EACN/C,UAAW+Q,EACX9N,OAAQA,EACR4N,IAAKA,IAAmB,WAAX5N,EAAsB,2BAAwB4D,GAC3DvF,QAASA,EACThC,MAAOA,EACP,aAAYwR,EACZ,cAAa/H,EAAO9I,SAEnBwR,KAOLvR,EAAAA,EAAAA,KAAA,UACE0C,KAAMA,EACN5C,UAAW+Q,EACXzP,QAASA,EACTkP,SAAUA,GAAY1G,EACtBxK,MAAOA,EACP,aAAYwR,EACZ,cAAa/H,EAAO9I,SAEnBwR,GACM,EAIb,GAAenI,EAAAA,EAAAA,MAAKzG,E,6ECjIpB,MAAMM,EAA8BlE,IAiB7B,IAjB8B,OACnCiE,EAAM,QACNlB,EAAO,MACP1C,EAAK,SACLW,EAAQ,KACRM,EAAO,KAAI,OACX6C,EAAM,WACNyO,GAAa,EAAI,qBACjBC,GAAuB,EAAI,gBAC3BC,GAAkB,EAAI,SACtBC,GAAW,EAAI,UACfhS,EAAY,GAAE,cACdwJ,EAAgB,GAAE,gBAClBC,EAAkB,GAAE,gBACpBC,EAAkB,GAAE,kBACpBuI,EAAoB,GAAE,OACtBlJ,GACD9J,EACC,MAAMiT,GAAWlM,EAAAA,EAAAA,QAAuB,MA2DxC,IAxDAE,EAAAA,EAAAA,YAAU,KACR,MAAMiM,EAAgB5Q,IAChBsQ,GAAwB,WAAVtQ,EAAE9B,KAClBuC,GACF,EASF,OANIkB,IACFkP,SAASC,iBAAiB,UAAWF,GAErCC,SAASE,KAAKvH,MAAMwH,SAAW,UAG1B,KACLH,SAASI,oBAAoB,UAAWL,GACxCC,SAASE,KAAKvH,MAAMwH,SAAW,MAAM,CACtC,GACA,CAACrP,EAAQlB,EAAS6P,KAGrB3L,EAAAA,EAAAA,YAAU,KACR,IAAKhD,IAAWgP,EAAS/L,QAAS,OAElC,MAAMsM,EAAoBP,EAAS/L,QAAQuM,iBACzC,4EAGF,GAAiC,IAA7BD,EAAkB7L,OAAc,OAEpC,MAAM+L,EAAeF,EAAkB,GACjCG,EAAcH,EAAkBA,EAAkB7L,OAAS,GAE3DiM,EAAgBtR,IACN,QAAVA,EAAE9B,MAEF8B,EAAEuR,SACAV,SAASW,gBAAkBJ,IAC7BC,EAAYI,QACZzR,EAAE0R,kBAGAb,SAASW,gBAAkBH,IAC7BD,EAAaK,QACbzR,EAAE0R,kBAEN,EAMF,OAHAb,SAASC,iBAAiB,UAAWQ,GACrCF,EAAaK,QAEN,KACLZ,SAASI,oBAAoB,UAAWK,EAAa,CACtD,GACA,CAAC3P,KAECA,EAAQ,OAAO,KAGpB,MAUMgQ,GACJnT,EAAAA,EAAAA,MAACoT,EAAAA,SAAQ,CAAAlT,SAAA,EAEPC,EAAAA,EAAAA,KAAA,OACEF,UAAW,gEAAgEiS,IAC3E3Q,QAASwQ,EAAuB9P,OAAU6E,EAC1C,cAAa,GAAGkC,gBAIlB7I,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qCAAoCC,UACjDC,EAAAA,EAAAA,KAAA,OAAKF,UAAW,yBAAyBgS,EAAW,SAAW,yCAAyC/R,UACtGF,EAAAA,EAAAA,MAAA,OACE+L,IAAKoG,EACLlS,UAAW,GAxBD,CAClBuR,GAAI,WACJjH,GAAI,WACJK,GAAI,WACJC,GAAI,YACJ4G,GAAI,YACJ4B,KAAM,mBAkB4B7S,2GAA8GP,IACxIsB,QAAUC,GAAMA,EAAEC,kBAClB,cAAauH,EAAO9I,SAAA,EAGpBF,EAAAA,EAAAA,MAAA,OAAKC,UAAW,wEAAwEyJ,IAAkBxJ,SAAA,CACtF,kBAAVX,GACNY,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sCAAqCC,SAAEX,IAErDA,EAEDyS,IACC7R,EAAAA,EAAAA,KAAA,UACE0C,KAAK,SACL5C,UAAU,wGACVsB,QAASU,EACT,aAAW,cACX,cAAa,GAAG+G,iBAAsB9I,UAEtCC,EAAAA,EAAAA,KAACyB,EAAAA,EAAS,CAAC3B,UAAU,kBAM3BE,EAAAA,EAAAA,KAAA,OAAKF,UAAW,aAAawJ,IAAgBvJ,SAC1CA,IAIFmD,IACClD,EAAAA,EAAAA,KAAA,OAAKF,UAAW,4EAA4E0J,IAAkBzJ,SAC3GmD,cAUf,OAAOiQ,EAAAA,EAAAA,cAAaH,EAAcd,SAASE,KAAK,EAGlD,GAAehJ,EAAAA,EAAAA,MAAKnG,E,gDClLpB,SAASzC,EAAYzB,EAIlBkM,GAAQ,IAJW,MACpB7L,EAAK,QACL8L,KACGC,GACJpM,EACC,OAAoBqM,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ/L,EAAqBgM,EAAAA,cAAoB,QAAS,CAC3D9K,GAAI4K,GACH9L,GAAS,KAAmBgM,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,mQAEP,CACA,MACA,EADiCX,EAAAA,WAAiB5K,E,gDCvBlD,SAASgB,EAASzC,EAIfkM,GAAQ,IAJQ,MACjB7L,EAAK,QACL8L,KACGC,GACJpM,EACC,OAAoBqM,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ/L,EAAqBgM,EAAAA,cAAoB,QAAS,CAC3D9K,GAAI4K,GACH9L,GAAS,KAAmBgM,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,0BAEP,CACA,MACA,EADiCX,EAAAA,WAAiB5J,E,gDCvBlD,SAASD,EAAOxC,EAIbkM,GAAQ,IAJM,MACf7L,EAAK,QACL8L,KACGC,GACJpM,EACC,OAAoBqM,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ/L,EAAqBgM,EAAAA,cAAoB,QAAS,CAC3D9K,GAAI4K,GACH9L,GAAS,KAAmBgM,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,6LACYX,EAAAA,cAAoB,OAAQ,CAC3CS,cAAe,QACfC,eAAgB,QAChBC,EAAG,wCAEP,CACA,MACA,EADiCX,EAAAA,WAAiB7J,E,gDC3BlD,SAASd,EAAS1B,EAIfkM,GAAQ,IAJQ,MACjB7L,EAAK,QACL8L,KACGC,GACJpM,EACC,OAAoBqM,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ/L,EAAqBgM,EAAAA,cAAoB,QAAS,CAC3D9K,GAAI4K,GACH9L,GAAS,KAAmBgM,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,sWAEP,CACA,MACA,EADiCX,EAAAA,WAAiB3K,E", "sources": ["features/verifications/components/VerificationList.tsx", "features/verifications/components/VerificationDetails.tsx", "features/verifications/components/ApproveVerificationModal.tsx", "features/verifications/components/RejectVerificationModal.tsx", "features/verifications/api/verificationsApi.ts", "features/verifications/hooks/useVerifications.ts", "pages/VerificationsPage.tsx", "components/layout/PageHeader.tsx", "components/common/Card.tsx", "components/common/LoadingSpinner.tsx", "../node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ClockIcon.js", "../node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js", "../node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ChevronUpIcon.js", "components/common/DataTable.tsx", "components/common/Button.tsx", "components/common/Modal.tsx", "../node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js", "../node_modules/@heroicons/react/24/outline/esm/CheckIcon.js", "../node_modules/@heroicons/react/24/outline/esm/EyeIcon.js", "../node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js"], "sourcesContent": ["/**\r\n * Verification List Component\r\n *\r\n * This component displays a list of verifications in a data table.\r\n */\r\n\r\nimport React from 'react';\r\nimport DataTable, { type Column } from '../../../components/common/DataTable';\r\nimport Avatar from '../../../components/common/Avatar';\r\nimport { type Verification } from '../types/index';\r\nimport {\r\n  CheckCircleIcon,\r\n  XCircleIcon,\r\n  ClockIcon,\r\n  CheckIcon,\r\n  XMarkIcon,\r\n  EyeIcon,\r\n  EnvelopeIcon,\r\n  PhoneIcon\r\n} from '@heroicons/react/24/outline';\r\n\r\ninterface VerificationListProps {\r\n  verifications: Verification[];\r\n  onViewVerification: (verification: Verification) => void;\r\n  onApproveClick: (verification: Verification) => void;\r\n  onRejectClick: (verification: Verification) => void;\r\n  title?: string;\r\n  description?: string;\r\n}\r\n\r\nconst VerificationList: React.FC<VerificationListProps> = ({\r\n  verifications,\r\n  onViewVerification,\r\n  onApproveClick,\r\n  onRejectClick,\r\n  title = 'Verifications',\r\n  description\r\n}) => {\r\n  const columns: Column<Verification>[] = [\r\n    {\r\n      key: 'companyName',\r\n      label: 'Company Name',\r\n      sortable: true,\r\n      render: (_value: string, verification: Verification) => (\r\n        <div className=\"flex items-center\">\r\n          <div className=\"mr-3\">\r\n            <Avatar\r\n              alt={verification.companyName}\r\n              name={verification.companyName}\r\n              size=\"sm\"\r\n            />\r\n          </div>\r\n          <div>\r\n            <div className=\"font-medium text-gray-900\">{verification.companyName}</div>\r\n            <div className=\"text-xs text-gray-500\">ID: {verification.id}</div>\r\n          </div>\r\n        </div>\r\n      )\r\n    },\r\n    {\r\n      key: 'email',\r\n      label: 'Email',\r\n      sortable: true,\r\n      render: (value: string) => (\r\n        <div className=\"flex items-center\">\r\n          <EnvelopeIcon className=\"w-4 h-4 text-gray-400 mr-2\" />\r\n          <span>{value}</span>\r\n        </div>\r\n      )\r\n    },\r\n    {\r\n      key: 'phone',\r\n      label: 'Phone',\r\n      sortable: true,\r\n      render: (value: string) => (\r\n        <div className=\"flex items-center\">\r\n          <PhoneIcon className=\"w-4 h-4 text-gray-400 mr-2\" />\r\n          <span>{value}</span>\r\n        </div>\r\n      )\r\n    },\r\n    {\r\n      key: 'status',\r\n      label: 'Status',\r\n      sortable: true,\r\n      render: (value: string) => {\r\n        // Handle undefined or null values\r\n        if (!value) {\r\n          return (\r\n            <div className=\"flex items-center\">\r\n              <ClockIcon className=\"w-4 h-4 text-gray-400 mr-1\" />\r\n              <span>Unknown</span>\r\n            </div>\r\n          );\r\n        }\r\n\r\n        let icon;\r\n        switch(value) {\r\n          case 'verified':\r\n            icon = <CheckCircleIcon className=\"w-4 h-4 text-green-500 mr-1\" />;\r\n            break;\r\n          case 'pending':\r\n            icon = <ClockIcon className=\"w-4 h-4 text-yellow-500 mr-1\" />;\r\n            break;\r\n          case 'rejected':\r\n            icon = <XCircleIcon className=\"w-4 h-4 text-red-500 mr-1\" />;\r\n            break;\r\n          default:\r\n            icon = <ClockIcon className=\"w-4 h-4 text-gray-400 mr-1\" />;\r\n        }\r\n        return (\r\n          <div className=\"flex items-center\">\r\n            {icon}\r\n            <span>{value ? value.charAt(0).toUpperCase() + value.slice(1) : 'Unknown'}</span>\r\n          </div>\r\n        );\r\n      }\r\n    },\r\n    {\r\n      key: 'submittedDate',\r\n      label: 'Submitted Date',\r\n      sortable: true,\r\n      render: (value) => new Date(value).toLocaleDateString()\r\n    },\r\n    {\r\n      key: 'actions',\r\n      label: 'Actions',\r\n      render: (_: any, verification: Verification) => (\r\n        <div className=\"flex items-center space-x-2\">\r\n          <button\r\n            className=\"p-1 text-gray-500 hover:text-primary rounded-full hover:bg-gray-100\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              onViewVerification(verification);\r\n            }}\r\n            title=\"View verification details\"\r\n          >\r\n            <EyeIcon className=\"w-5 h-5\" />\r\n          </button>\r\n          <button\r\n            className=\"p-1 text-gray-500 hover:text-green-600 rounded-full hover:bg-gray-100\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              onApproveClick(verification);\r\n            }}\r\n            title=\"Approve verification\"\r\n          >\r\n            <CheckIcon className=\"w-5 h-5\" />\r\n          </button>\r\n          <button\r\n            className=\"p-1 text-gray-500 hover:text-red-600 rounded-full hover:bg-gray-100\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              onRejectClick(verification);\r\n            }}\r\n            title=\"Reject verification\"\r\n          >\r\n            <XMarkIcon className=\"w-5 h-5\" />\r\n          </button>\r\n        </div>\r\n      )\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <DataTable<Verification>\r\n      columns={columns}\r\n      data={verifications}\r\n      title={title}\r\n      description={description}\r\n      pagination={true}\r\n      selectable={false}\r\n    />\r\n  );\r\n};\r\n\r\nexport default VerificationList;\r\n", "/**\r\n * Verification Details Component\r\n * \r\n * This component displays detailed information about a verification.\r\n */\r\n\r\nimport React from 'react';\r\nimport type{ Verification } from '../types/index';\r\nimport Button from '../../../components/common/Button';\r\nimport { CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';\r\n\r\ninterface VerificationDetailsProps {\r\n  verification: Verification;\r\n  onClose: () => void;\r\n  onApprove: () => void;\r\n  onReject: () => void;\r\n}\r\n\r\nconst VerificationDetails: React.FC<VerificationDetailsProps> = ({\r\n  verification,\r\n  onClose,\r\n  onApprove,\r\n  onReject\r\n}) => {\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n        <div>\r\n          <h3 className=\"text-sm font-medium text-gray-500\">Company Information</h3>\r\n          <div className=\"mt-2 space-y-2\">\r\n            <p className=\"text-sm text-gray-700\">\r\n              <span className=\"font-medium\">Company Name:</span> {verification.companyName}\r\n            </p>\r\n            <p className=\"text-sm text-gray-700\">\r\n              <span className=\"font-medium\">Contact Person:</span> {verification.contactPerson}\r\n            </p>\r\n            <p className=\"text-sm text-gray-700\">\r\n              <span className=\"font-medium\">Email:</span> {verification.email}\r\n            </p>\r\n            <p className=\"text-sm text-gray-700\">\r\n              <span className=\"font-medium\">Phone:</span> {verification.phone}\r\n            </p>\r\n            <p className=\"text-sm text-gray-700\">\r\n              <span className=\"font-medium\">Submitted Date:</span> {new Date(verification.submittedDate).toLocaleString()}\r\n            </p>\r\n          </div>\r\n        </div>\r\n        \r\n        <div>\r\n          <h3 className=\"text-sm font-medium text-gray-500\">Submitted Documents</h3>\r\n          <div className=\"mt-2\">\r\n            <ul className=\"divide-y divide-gray-200\">\r\n              {verification.documents.map((doc, index) => (\r\n                <li key={index} className=\"py-2\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div>\r\n                      <p className=\"text-sm font-medium text-gray-700\">{doc.name}</p>\r\n                      <p className=\"text-xs text-gray-500\">{doc.type} Document</p>\r\n                    </div>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"xs\"\r\n                      href={doc.url}\r\n                      target=\"_blank\"\r\n                    >\r\n                      View\r\n                    </Button>\r\n                  </div>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <div className=\"border-t border-gray-200 pt-4\">\r\n        <div className=\"flex justify-end space-x-3\">\r\n          <Button\r\n            variant=\"outline\"\r\n            onClick={onClose}\r\n          >\r\n            Close\r\n          </Button>\r\n          <Button\r\n            variant=\"success\"\r\n            icon={<CheckIcon className=\"h-4 w-4\" />}\r\n            onClick={onApprove}\r\n          >\r\n            Approve\r\n          </Button>\r\n          <Button\r\n            variant=\"danger\"\r\n            icon={<XMarkIcon className=\"h-4 w-4\" />}\r\n            onClick={onReject}\r\n          >\r\n            Reject\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default VerificationDetails;\r\n", "/**\r\n * Approve Verification Modal Component\r\n * \r\n * This component displays a confirmation modal for approving a verification.\r\n */\r\n\r\nimport React from 'react';\r\nimport Modal from '../../../components/common/Modal';\r\nimport Button from '../../../components/common/Button';\r\nimport type{ Verification } from '../types/index';\r\n\r\ninterface ApproveVerificationModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onApprove: () => void;\r\n  verification: Verification | null;\r\n}\r\n\r\nconst ApproveVerificationModal: React.FC<ApproveVerificationModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  onApprove,\r\n  verification\r\n}) => {\r\n  if (!verification) return null;\r\n\r\n  return (\r\n    <Modal\r\n      isOpen={isOpen}\r\n      onClose={onClose}\r\n      title=\"Approve Supplier\"\r\n      size=\"sm\"\r\n      footer={\r\n        <>\r\n          <Button\r\n            variant=\"outline\"\r\n            onClick={onClose}\r\n          >\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            variant=\"success\"\r\n            onClick={onApprove}\r\n          >\r\n            Approve\r\n          </Button>\r\n        </>\r\n      }\r\n    >\r\n      <p className=\"text-sm text-gray-600\">\r\n        Are you sure you want to approve <span className=\"font-medium\">{verification.companyName}</span> as a verified supplier?\r\n      </p>\r\n      <p className=\"text-sm text-gray-600 mt-2\">\r\n        This will grant them access to list products and receive orders on the platform.\r\n      </p>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default ApproveVerificationModal;\r\n", "/**\r\n * Reject Verification Modal Component\r\n * \r\n * This component displays a modal for rejecting a verification with a reason.\r\n */\r\n\r\nimport React from 'react';\r\nimport Modal from '../../../components/common/Modal';\r\nimport Button from '../../../components/common/Button';\r\nimport type{ Verification } from '../types/index';\r\n\r\ninterface RejectVerificationModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onReject: () => void;\r\n  verification: Verification | null;\r\n  rejectReason: string;\r\n  setRejectReason: (reason: string) => void;\r\n}\r\n\r\nconst RejectVerificationModal: React.FC<RejectVerificationModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  onReject,\r\n  verification,\r\n  rejectReason,\r\n  setRejectReason\r\n}) => {\r\n  if (!verification) return null;\r\n\r\n  return (\r\n    <Modal\r\n      isOpen={isOpen}\r\n      onClose={onClose}\r\n      title=\"Reject Supplier\"\r\n      size=\"sm\"\r\n      footer={\r\n        <>\r\n          <Button\r\n            variant=\"outline\"\r\n            onClick={onClose}\r\n          >\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            variant=\"danger\"\r\n            onClick={onReject}\r\n          >\r\n            Reject\r\n          </Button>\r\n        </>\r\n      }\r\n    >\r\n      <div className=\"space-y-4\">\r\n        <p className=\"text-sm text-gray-600\">\r\n          Are you sure you want to reject <span className=\"font-medium\">{verification.companyName}</span>'s verification request?\r\n        </p>\r\n        \r\n        <div>\r\n          <label htmlFor=\"reject-reason\" className=\"block text-sm font-medium text-gray-700\">\r\n            Reason for Rejection\r\n          </label>\r\n          <textarea\r\n            id=\"reject-reason\"\r\n            rows={3}\r\n            className=\"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm\"\r\n            placeholder=\"Please provide a reason for rejection\"\r\n            value={rejectReason}\r\n            onChange={(e) => setRejectReason(e.target.value)}\r\n          />\r\n        </div>\r\n        \r\n        <p className=\"text-xs text-gray-500\">\r\n          This reason will be sent to the supplier via email.\r\n        </p>\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default RejectVerificationModal;\r\n", "/**\r\n * Verifications API Service\r\n *\r\n * This file provides methods for interacting with the verifications API endpoints.\r\n */\r\n\r\nimport apiClient from '../../../api';\r\nimport { handleApiError } from '../../../utils/errorHandling';\r\nimport { responseValidators } from '../../../utils/apiHelpers';\r\nimport type { Verification, VerificationRequest, VerificationUpdateData } from '../types';\r\n\r\nexport const verificationsApi = {\r\n  /**\r\n   * Get all verifications\r\n   */\r\n  getVerifications: async (params?: Record<string, any>): Promise<Verification[]> => {\r\n    try {\r\n      const response = await apiClient.get<Verification[]>('/verifications', { params });\r\n      return responseValidators.getList(response, 'verifications');\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get verification by ID\r\n   */\r\n  getVerificationById: async (id: string): Promise<Verification> => {\r\n    try {\r\n      const response = await apiClient.get<Verification>(`/verifications/${id}`);\r\n      return responseValidators.getById(response, 'verification', id);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Create verification request\r\n   */\r\n  createVerification: async (verificationData: VerificationRequest): Promise<Verification> => {\r\n    try {\r\n      const response = await apiClient.post<Verification>('/verifications', verificationData);\r\n      return responseValidators.create(response, 'verification');\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update verification status\r\n   */\r\n  updateVerificationStatus: async (id: string, status: Verification['status']): Promise<Verification> => {\r\n    try {\r\n      const response = await apiClient.put<Verification>(`/verifications/${id}/status`, { status });\r\n      return responseValidators.update(response, 'verification', id);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get verifications by status\r\n   */\r\n  getVerificationsByStatus: async (status: Verification['status']): Promise<Verification[]> => {\r\n    try {\r\n      const response = await apiClient.get<Verification[]>('/verifications', { params: { status } });\r\n      return responseValidators.getList(response, 'verifications', true);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get verifications by user\r\n   */\r\n  getVerificationsByUser: async (userId: string): Promise<Verification[]> => {\r\n    try {\r\n      const response = await apiClient.get<Verification[]>('/verifications', { params: { userId } });\r\n      return responseValidators.getList(response, 'verifications', true);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update verification\r\n   */\r\n  updateVerification: async (id: string, data: VerificationUpdateData): Promise<Verification> => {\r\n    try {\r\n      const response = await apiClient.put<Verification>(`/verifications/${id}`, data);\r\n      return responseValidators.update(response, 'verification', id);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Approve verification\r\n   */\r\n  approveVerification: async (id: string, notes?: string): Promise<Verification> => {\r\n    try {\r\n      const response = await apiClient.put<Verification>(`/verifications/${id}/approve`, { notes });\r\n      return responseValidators.update(response, 'verification', id);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Reject verification\r\n   */\r\n  rejectVerification: async (id: string, notes?: string): Promise<Verification> => {\r\n    try {\r\n      const response = await apiClient.put<Verification>(`/verifications/${id}/reject`, { notes });\r\n      return responseValidators.update(response, 'verification', id);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  }\r\n};\r\n\r\nexport default verificationsApi;\r\n", "/**\r\n * Verifications Hook\r\n *\r\n * This hook provides methods and state for working with verifications.\r\n */\r\n\r\nimport { useState, useCallback, useEffect, useRef } from 'react';\r\nimport type{ Verification, VerificationUpdateData } from '../types/index';\r\nimport verificationsApi from '../api/verificationsApi';\r\nimport useNotification from '../../../hooks/useNotification';\r\n\r\nexport const useVerifications = () => {\r\n  const [verifications, setVerifications] = useState<Verification[]>([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<Error | null>(null);\r\n  const { showNotification } = useNotification();\r\n\r\n  // Use ref to avoid dependency issues with showNotification\r\n  const showNotificationRef = useRef(showNotification);\r\n  const hasInitialFetched = useRef(false);\r\n\r\n  // Update ref when showNotification changes\r\n  useEffect(() => {\r\n    showNotificationRef.current = showNotification;\r\n  });\r\n\r\n  // Fetch all verifications\r\n  const fetchVerifications = useCallback(async () => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const data = await verificationsApi.getVerifications();\r\n      setVerifications(data);\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to fetch verifications'\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Get a verification by ID\r\n  const getVerificationById = useCallback(async (id: string) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const verification = await verificationsApi.getVerificationById(id);\r\n      return verification;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to fetch verification ${id}`\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Get verifications by status\r\n  const getVerificationsByStatus = useCallback(async (status: Verification['status']) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const filteredVerifications = await verificationsApi.getVerificationsByStatus(status);\r\n      return filteredVerifications;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: `Failed to fetch verifications with status ${status}`\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Update a verification\r\n  const updateVerification = useCallback(async (id: string, data: VerificationUpdateData) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const updatedVerification = await verificationsApi.updateVerification(id, data);\r\n      setVerifications(prevVerifications =>\r\n        prevVerifications.map(verification => verification.id === id ? updatedVerification : verification)\r\n      );\r\n      showNotificationRef.current({\r\n        type: 'success',\r\n        title: 'Success',\r\n        message: 'Verification updated successfully'\r\n      });\r\n      return updatedVerification;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to update verification'\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Approve a verification\r\n  const approveVerification = useCallback(async (id: string, notes: string = '') => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const approvedVerification = await verificationsApi.approveVerification(id, notes);\r\n      setVerifications(prevVerifications =>\r\n        prevVerifications.map(verification => verification.id === id ? approvedVerification : verification)\r\n      );\r\n      return approvedVerification;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to approve verification'\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Reject a verification\r\n  const rejectVerification = useCallback(async (id: string, notes: string) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const rejectedVerification = await verificationsApi.rejectVerification(id, notes);\r\n      setVerifications(prevVerifications =>\r\n        prevVerifications.map(verification => verification.id === id ? rejectedVerification : verification)\r\n      );\r\n      return rejectedVerification;\r\n    } catch (err) {\r\n      setError(err as Error);\r\n      showNotificationRef.current({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: 'Failed to reject verification'\r\n      });\r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Load verifications on mount\r\n  useEffect(() => {\r\n    if (!hasInitialFetched.current) {\r\n      hasInitialFetched.current = true;\r\n      fetchVerifications();\r\n    }\r\n  }, []);\r\n\r\n  return {\r\n    verifications,\r\n    isLoading,\r\n    error,\r\n    fetchVerifications,\r\n    getVerificationById,\r\n    getVerificationsByStatus,\r\n    updateVerification,\r\n    approveVerification,\r\n    rejectVerification\r\n  };\r\n};\r\n\r\nexport default useVerifications;\r\n", "/**\r\n * VerificationsPage Component\r\n *\r\n * The verifications page for the ConnectChain admin panel.\r\n */\r\n\r\nimport React, { useState, useMemo } from 'react';\r\nimport PageHeader from '../components/layout/PageHeader';\r\nimport Card from '../components/common/Card';\r\nimport Modal from '../components/common/Modal';\r\nimport LoadingSpinner from '../components/common/LoadingSpinner';\r\nimport useNotification from '../hooks/useNotification';\r\nimport {\r\n  VerificationList,\r\n  VerificationDetails,\r\n  ApproveVerificationModal,\r\n  RejectVerificationModal,\r\n  Verification,\r\n  useVerifications\r\n} from '../features/verifications/index';\r\n\r\nconst VerificationsPage: React.FC = () => {\r\n  // Use the useVerifications hook for API integration\r\n  const { verifications, isLoading, approveVerification, rejectVerification } = useVerifications();\r\n\r\n  const { showSuccess, showError } = useNotification();\r\n  const [selectedVerification, setSelectedVerification] = useState<Verification | null>(null);\r\n  const [isViewModalOpen, setIsViewModalOpen] = useState(false);\r\n  const [isApproveModalOpen, setIsApproveModalOpen] = useState(false);\r\n  const [isRejectModalOpen, setIsRejectModalOpen] = useState(false);\r\n  const [rejectReason, setRejectReason] = useState('');\r\n\r\n  // Track processed verifications to remove them from the list\r\n  const [processedVerificationIds, setProcessedVerificationIds] = useState<Set<string>>(new Set());\r\n\r\n  // Filter out processed verifications to show only pending ones\r\n  const displayedVerifications = useMemo(() => {\r\n    return verifications.filter(verification =>\r\n      !processedVerificationIds.has(verification.id) && verification.status === 'pending'\r\n    );\r\n  }, [verifications, processedVerificationIds]);\r\n\r\n\r\n\r\n  const handleViewVerification = (verification: Verification) => {\r\n    setSelectedVerification(verification);\r\n    setIsViewModalOpen(true);\r\n  };\r\n\r\n  const handleApproveClick = (verification: Verification) => {\r\n    setSelectedVerification(verification);\r\n    setIsApproveModalOpen(true);\r\n  };\r\n\r\n  const handleRejectClick = (verification: Verification) => {\r\n    setSelectedVerification(verification);\r\n    setIsRejectModalOpen(true);\r\n  };\r\n\r\n  const handleApprove = async () => {\r\n    if (!selectedVerification) return;\r\n\r\n    try {\r\n      await approveVerification(selectedVerification.id);\r\n\r\n      // Add to processed list to remove from display\r\n      setProcessedVerificationIds(prev => new Set(prev).add(selectedVerification.id));\r\n\r\n      showSuccess(`${selectedVerification.companyName} has been approved`);\r\n      setIsApproveModalOpen(false);\r\n      setSelectedVerification(null);\r\n    } catch (error) {\r\n      console.error('Error approving verification:', error);\r\n      // Error notification is handled by the hook\r\n      // Don't add to processed list if there was an error\r\n    }\r\n  };\r\n\r\n  const handleReject = async () => {\r\n    if (!rejectReason.trim()) {\r\n      showError('Please provide a reason for rejection');\r\n      return;\r\n    }\r\n\r\n    if (!selectedVerification) return;\r\n\r\n    try {\r\n      await rejectVerification(selectedVerification.id, rejectReason);\r\n\r\n      // Add to processed list to remove from display\r\n      setProcessedVerificationIds(prev => new Set(prev).add(selectedVerification.id));\r\n\r\n      showSuccess(`${selectedVerification.companyName} has been rejected`);\r\n      setIsRejectModalOpen(false);\r\n      setRejectReason('');\r\n      setSelectedVerification(null);\r\n    } catch (error) {\r\n      console.error('Error rejecting verification:', error);\r\n      // Error notification is handled by the hook\r\n      // Don't add to processed list if there was an error\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <PageHeader\r\n        title=\"Supplier Verifications\"\r\n        description=\"Review and manage supplier verification requests\"\r\n        breadcrumbs={[{ label: 'Verifications' }]}\r\n      />\r\n\r\n      <Card>\r\n        {isLoading ? (\r\n          <div className=\"flex justify-center py-8\">\r\n            <LoadingSpinner />\r\n          </div>\r\n        ) : displayedVerifications.length === 0 ? (\r\n          <div className=\"text-center py-12\">\r\n            <div className=\"text-gray-500 text-lg mb-2\">No pending verifications</div>\r\n            <div className=\"text-gray-400 text-sm\">\r\n              All verification requests have been processed or there are no new requests.\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <VerificationList\r\n            verifications={displayedVerifications}\r\n            onViewVerification={handleViewVerification}\r\n            onApproveClick={handleApproveClick}\r\n            onRejectClick={handleRejectClick}\r\n            title=\"Pending Verifications\"\r\n            description=\"Suppliers waiting for verification approval\"\r\n          />\r\n        )}\r\n      </Card>\r\n\r\n      {/* View Verification Modal */}\r\n      <Modal\r\n        isOpen={isViewModalOpen}\r\n        onClose={() => setIsViewModalOpen(false)}\r\n        title=\"Verification Details\"\r\n        size=\"lg\"\r\n      >\r\n        {selectedVerification && (\r\n          <VerificationDetails\r\n            verification={selectedVerification}\r\n            onClose={() => setIsViewModalOpen(false)}\r\n            onApprove={() => {\r\n              setIsViewModalOpen(false);\r\n              setIsApproveModalOpen(true);\r\n            }}\r\n            onReject={() => {\r\n              setIsViewModalOpen(false);\r\n              setIsRejectModalOpen(true);\r\n            }}\r\n          />\r\n        )}\r\n      </Modal>\r\n\r\n      {/* Approve Confirmation Modal */}\r\n      <ApproveVerificationModal\r\n        isOpen={isApproveModalOpen}\r\n        onClose={() => setIsApproveModalOpen(false)}\r\n        onApprove={handleApprove}\r\n        verification={selectedVerification}\r\n      />\r\n\r\n      {/* Reject Modal */}\r\n      <RejectVerificationModal\r\n        isOpen={isRejectModalOpen}\r\n        onClose={() => setIsRejectModalOpen(false)}\r\n        onReject={handleReject}\r\n        verification={selectedVerification}\r\n        rejectReason={rejectReason}\r\n        setRejectReason={setRejectReason}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default VerificationsPage;\r\n", "/**\r\n * PageHeader Component\r\n * \r\n * A consistent header component for pages with title, description, and actions.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';\r\n\r\nexport interface BreadcrumbItem {\r\n  label: string;\r\n  path?: string;\r\n}\r\n\r\nexport interface PageHeaderProps {\r\n  title: string;\r\n  description?: string | ReactNode;\r\n  actions?: ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n  className?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst PageHeader: React.FC<PageHeaderProps> = ({\r\n  title,\r\n  description,\r\n  actions,\r\n  breadcrumbs,\r\n  className = '',\r\n  testId,\r\n}) => {\r\n  return (\r\n    <div \r\n      className={`mb-6 ${className}`}\r\n      data-testid={testId}\r\n    >\r\n      {/* Breadcrumbs */}\r\n      {breadcrumbs && breadcrumbs.length > 0 && (\r\n        <nav className=\"flex mb-4\" aria-label=\"Breadcrumb\">\r\n          <ol className=\"flex items-center space-x-1 text-sm text-gray-500\">\r\n            <li>\r\n              <Link \r\n                to=\"/\" \r\n                className=\"flex items-center hover:text-primary\"\r\n                aria-label=\"Home\"\r\n              >\r\n                <HomeIcon className=\"h-4 w-4\" />\r\n              </Link>\r\n            </li>\r\n            \r\n            {breadcrumbs.map((item, index) => (\r\n              <li key={index} className=\"flex items-center\">\r\n                <ChevronRightIcon className=\"h-4 w-4 mx-1 text-gray-400\" />\r\n                {item.path && index < breadcrumbs.length - 1 ? (\r\n                  <Link \r\n                    to={item.path} \r\n                    className=\"hover:text-primary\"\r\n                  >\r\n                    {item.label}\r\n                  </Link>\r\n                ) : (\r\n                  <span className=\"font-medium text-gray-700\">{item.label}</span>\r\n                )}\r\n              </li>\r\n            ))}\r\n          </ol>\r\n        </nav>\r\n      )}\r\n      \r\n      {/* Header Content */}\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n        <div>\r\n          <h1 className=\"text-2xl font-bold text-gray-800\">{title}</h1>\r\n          {description && typeof description === 'string' ? (\r\n            <p className=\"mt-1 text-sm text-gray-500\">{description}</p>\r\n          ) : (\r\n            description\r\n          )}\r\n        </div>\r\n        \r\n        {actions && (\r\n          <div className=\"flex flex-wrap gap-3 mt-2 sm:mt-0\">\r\n            {actions}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(PageHeader);\r\n", "/**\r\n * Card Component\r\n *\r\n * A reusable card component for displaying content in a contained box.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\nexport interface CardProps {\r\n  title?: string | ReactNode;\r\n  subtitle?: string | ReactNode;\r\n  children: ReactNode;\r\n  className?: string;\r\n  bodyClassName?: string;\r\n  headerClassName?: string;\r\n  footerClassName?: string;\r\n  icon?: ReactNode;\r\n  footer?: ReactNode;\r\n  onClick?: () => void;\r\n  hoverable?: boolean;\r\n  noPadding?: boolean;\r\n  bordered?: boolean;\r\n  loading?: boolean;\r\n  testId?: string;\r\n}\r\n\r\nconst Card: React.FC<CardProps> = ({\r\n  title,\r\n  subtitle,\r\n  children,\r\n  className = '',\r\n  bodyClassName = '',\r\n  headerClassName = '',\r\n  footerClassName = '',\r\n  icon,\r\n  footer,\r\n  onClick,\r\n  hoverable = false,\r\n  noPadding = false,\r\n  bordered = true,\r\n  loading = false,\r\n  testId,\r\n}) => {\r\n  // Base classes\r\n  const cardClasses = `\r\n    bg-white rounded-xl ${bordered ? 'border border-gray-100' : ''} overflow-hidden transition-all duration-300\r\n    ${hoverable ? 'hover:shadow-md hover:border-gray-200 transform hover:-translate-y-1' : 'shadow-sm'}\r\n    ${onClick ? 'cursor-pointer' : ''}\r\n    ${className}\r\n  `;\r\n\r\n  // Header classes\r\n  const headerClasses = `\r\n    px-6 py-4 border-b border-gray-100 flex items-center justify-between\r\n    ${headerClassName}\r\n  `;\r\n\r\n  // Body classes\r\n  const bodyClasses = `\r\n    ${noPadding ? '' : 'p-6'}\r\n    ${bodyClassName}\r\n  `;\r\n\r\n  // Footer classes\r\n  const footerClasses = `\r\n    px-6 py-4 bg-gray-50 border-t border-gray-100\r\n    ${footerClassName}\r\n  `;\r\n\r\n  // Loading skeleton\r\n  if (loading) {\r\n    return (\r\n      <div className={cardClasses} data-testid={testId}>\r\n        {(title || subtitle || icon) && (\r\n          <div className={headerClasses}>\r\n            <div className=\"w-full\">\r\n              {title && <div className=\"h-6 bg-gray-200 rounded w-1/3 animate-pulse\"></div>}\r\n              {subtitle && <div className=\"h-4 mt-2 bg-gray-200 rounded w-1/2 animate-pulse\"></div>}\r\n            </div>\r\n            {icon && <div className=\"h-8 w-8 bg-gray-200 rounded-full animate-pulse\"></div>}\r\n          </div>\r\n        )}\r\n\r\n        <div className={bodyClasses}>\r\n          <div className=\"h-24 bg-gray-200 rounded animate-pulse\"></div>\r\n        </div>\r\n\r\n        {footer && (\r\n          <div className={footerClasses}>\r\n            <div className=\"h-8 bg-gray-200 rounded w-1/4 animate-pulse\"></div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className={cardClasses}\r\n      onClick={onClick}\r\n      data-testid={testId}\r\n    >\r\n      {(title || subtitle || icon) && (\r\n        <div className={headerClasses}>\r\n          <div>\r\n            {typeof title === 'string' ? (\r\n              <h3 className=\"text-lg font-semibold text-primary\">{title}</h3>\r\n            ) : (\r\n              title\r\n            )}\r\n            {typeof subtitle === 'string' ? (\r\n              <p className=\"mt-1 text-sm text-gray-500\">{subtitle}</p>\r\n            ) : (\r\n              subtitle\r\n            )}\r\n          </div>\r\n          {icon && <div className=\"text-primary\">{icon}</div>}\r\n        </div>\r\n      )}\r\n\r\n      <div className={bodyClasses}>{children}</div>\r\n\r\n      {footer && (\r\n        <div className={footerClasses}>\r\n          {footer}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(Card);\r\n", "// src/components/common/LoadingSpinner.tsx\r\nimport React from 'react';\r\nimport './LoadingSpinner.css';\r\n\r\ninterface LoadingSpinnerProps {\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  variant?: 'spinner' | 'dots' | 'pulse' | 'ripple';\r\n  color?: string;\r\n  useCurrentColor?: boolean;\r\n}\r\n\r\nconst LoadingSpinner: React.FC<LoadingSpinnerProps> = ({\r\n  size = 'md',\r\n  className = '',\r\n  variant = 'spinner',\r\n  color = '#F28B22', // Primary color\r\n  useCurrentColor = false\r\n}) => {\r\n  const sizeMap = {\r\n    sm: { spinner: 'w-5 h-5', dots: 'w-1 h-1', pulse: 'w-4 h-4', ripple: 'w-6 h-6' },\r\n    md: { spinner: 'w-8 h-8', dots: 'w-1.5 h-1.5', pulse: 'w-6 h-6', ripple: 'w-10 h-10' },\r\n    lg: { spinner: 'w-12 h-12', dots: 'w-2 h-2', pulse: 'w-8 h-8', ripple: 'w-16 h-16' }\r\n  };\r\n\r\n  const currentColor = useCurrentColor ? 'currentColor' : color;\r\n\r\n  // Simple rotating ring spinner\r\n  if (variant === 'spinner') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`spinner-smooth rounded-full border-2 border-gray-200 ${sizeMap[size].spinner}`}\r\n          style={{\r\n            borderTopColor: currentColor,\r\n            borderRightColor: currentColor,\r\n          }}\r\n        />\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Three bouncing dots\r\n  if (variant === 'dots') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center space-x-1 dots-bounce ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`${sizeMap[size].dots} rounded-full dot`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <div\r\n          className={`${sizeMap[size].dots} rounded-full dot`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <div\r\n          className={`${sizeMap[size].dots} rounded-full dot`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Pulsing circle\r\n  if (variant === 'pulse') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`${sizeMap[size].pulse} rounded-full pulse-smooth`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Ripple effect\r\n  if (variant === 'ripple') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`${sizeMap[size].ripple} rounded-full ripple-effect`}\r\n          style={{ color: currentColor }}\r\n        >\r\n          <div\r\n            className={`${sizeMap[size].pulse} rounded-full pulse-smooth mx-auto`}\r\n            style={{ backgroundColor: currentColor }}\r\n          />\r\n        </div>\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\nexport default LoadingSpinner;", "import * as React from \"react\";\nfunction ChevronRightIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChevronRightIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ClockIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ClockIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction XCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(XCircleIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction MagnifyingGlassIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MagnifyingGlassIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ChevronUpIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m4.5 15.75 7.5-7.5 7.5 7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChevronUpIcon);\nexport default ForwardRef;", "/**\r\n * DataTable Component\r\n *\r\n * A reusable data table component with sorting, filtering, pagination, and row selection.\r\n */\r\n\r\nimport React, { useState, useMemo, memo } from 'react';\r\nimport { MagnifyingGlassIcon, ChevronUpIcon, ChevronDownIcon } from '@heroicons/react/24/outline';\r\nimport LoadingSpinner from './LoadingSpinner';\r\nimport { CONFIG } from '../../constants/config';\r\n\r\nexport interface Column<T = Record<string, any>> {\r\n  key: string;\r\n  label: string;\r\n  sortable?: boolean;\r\n  render?: (value: any, row: T) => React.ReactNode;\r\n  width?: string;\r\n  align?: 'left' | 'center' | 'right';\r\n  className?: string;\r\n}\r\n\r\nexport interface DataTableProps<T = Record<string, any>> {\r\n  columns: Column<T>[];\r\n  data: T[];\r\n  onRowClick?: ((row: T) => void) | undefined;\r\n  title?: string | React.ReactNode;\r\n  description?: string | React.ReactNode;\r\n  loading?: boolean;\r\n  pagination?: boolean;\r\n  pageSize?: number;\r\n  selectable?: boolean;\r\n  onSelectionChange?: (selectedRows: T[]) => void;\r\n  actions?: React.ReactNode;\r\n  emptyMessage?: string;\r\n  className?: string;\r\n  headerClassName?: string;\r\n  bodyClassName?: string;\r\n  footerClassName?: string;\r\n  rowClassName?: (row: T, index: number) => string;\r\n  initialSortKey?: string;\r\n  initialSortDirection?: 'asc' | 'desc';\r\n  testId?: string;\r\n}\r\n\r\nfunction DataTable<T extends Record<string, any>>({\r\n  columns,\r\n  data,\r\n  onRowClick,\r\n  title,\r\n  description,\r\n  loading = false,\r\n  pagination = true,\r\n  pageSize = CONFIG.DEFAULT_PAGE_SIZE,\r\n  selectable = true,\r\n  onSelectionChange,\r\n  actions,\r\n  emptyMessage = 'No results found',\r\n  className = '',\r\n  headerClassName = '',\r\n  bodyClassName = '',\r\n  footerClassName = '',\r\n  rowClassName,\r\n  initialSortKey,\r\n  initialSortDirection = 'asc',\r\n  testId,\r\n}: DataTableProps<T>) {\r\n  // State\r\n  const [sortConfig, setSortConfig] = useState<{\r\n    key: string;\r\n    direction: 'asc' | 'desc';\r\n  } | null>(initialSortKey ? { key: initialSortKey, direction: initialSortDirection } : null);\r\n\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [selectedRows, setSelectedRows] = useState<number[]>([]);\r\n  const [hoveredRow, setHoveredRow] = useState<number | null>(null);\r\n\r\n  // Sorting\r\n  const handleSort = (key: string) => {\r\n    let direction: 'asc' | 'desc' = 'asc';\r\n    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {\r\n      direction = 'desc';\r\n    }\r\n    setSortConfig({ key, direction });\r\n  };\r\n\r\n  const sortedData = useMemo(() => {\r\n    if (!sortConfig) return data;\r\n\r\n    return [...data].sort((a, b) => {\r\n      const aValue = a[sortConfig.key];\r\n      const bValue = b[sortConfig.key];\r\n\r\n      // Handle null or undefined values\r\n      if (aValue == null && bValue == null) return 0;\r\n      if (aValue == null) return sortConfig.direction === 'asc' ? -1 : 1;\r\n      if (bValue == null) return sortConfig.direction === 'asc' ? 1 : -1;\r\n\r\n      // Handle different data types\r\n      if (typeof aValue === 'string' && typeof bValue === 'string') {\r\n        return sortConfig.direction === 'asc'\r\n          ? aValue.localeCompare(bValue)\r\n          : bValue.localeCompare(aValue);\r\n      }\r\n\r\n      if (aValue < bValue) {\r\n        return sortConfig.direction === 'asc' ? -1 : 1;\r\n      }\r\n      if (aValue > bValue) {\r\n        return sortConfig.direction === 'asc' ? 1 : -1;\r\n      }\r\n      return 0;\r\n    });\r\n  }, [data, sortConfig]);\r\n\r\n  // Filtering\r\n  const filteredData = useMemo(() => {\r\n    if (!searchTerm) return sortedData;\r\n\r\n    return sortedData.filter((row) =>\r\n      Object.entries(row).some(([_key, value]) => {\r\n        // Skip filtering on complex objects\r\n        if (value === null || value === undefined) return false;\r\n        if (typeof value === 'object') return false;\r\n\r\n        return String(value).toLowerCase().includes(searchTerm.toLowerCase());\r\n      })\r\n    );\r\n  }, [sortedData, searchTerm]);\r\n\r\n  // Pagination\r\n  const totalPages = Math.ceil(filteredData.length / pageSize);\r\n  const paginatedData = useMemo(() => {\r\n    const startIndex = (currentPage - 1) * pageSize;\r\n    return filteredData.slice(startIndex, startIndex + pageSize);\r\n  }, [filteredData, currentPage, pageSize]);\r\n\r\n  const handlePageChange = (page: number) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  // Row selection\r\n  const handleRowSelect = (index: number, event: React.MouseEvent) => {\r\n    event.stopPropagation();\r\n\r\n    const newSelectedRows = [...selectedRows];\r\n\r\n    if (selectedRows.includes(index)) {\r\n      const idx = newSelectedRows.indexOf(index);\r\n      newSelectedRows.splice(idx, 1);\r\n    } else {\r\n      newSelectedRows.push(index);\r\n    }\r\n\r\n    setSelectedRows(newSelectedRows);\r\n\r\n    if (onSelectionChange) {\r\n      const selectedItems = newSelectedRows\r\n        .map(idx => paginatedData[idx])\r\n        .filter((item): item is T => item !== undefined);\r\n      onSelectionChange(selectedItems);\r\n    }\r\n  };\r\n\r\n  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const newSelectedRows = event.target.checked\r\n      ? Array.from({ length: paginatedData.length }, (_, i) => i)\r\n      : [];\r\n\r\n    setSelectedRows(newSelectedRows);\r\n\r\n    if (onSelectionChange) {\r\n      const selectedItems = newSelectedRows\r\n        .map(idx => paginatedData[idx])\r\n        .filter((item): item is T => item !== undefined);\r\n      onSelectionChange(selectedItems);\r\n    }\r\n  };\r\n\r\n  // Status badge renderer\r\n  const renderStatusBadge = (status: string) => {\r\n    let bgColor = 'bg-gray-100 text-gray-800';\r\n\r\n    if (typeof status === 'string') {\r\n      const statusLower = status.toLowerCase();\r\n\r\n      if (statusLower.includes('active') || statusLower.includes('approved') ||\r\n          statusLower.includes('verified') || statusLower.includes('completed') ||\r\n          statusLower.includes('success')) {\r\n        bgColor = 'bg-green-100 text-green-800';\r\n      } else if (statusLower.includes('pending') || statusLower.includes('processing')) {\r\n        bgColor = 'bg-yellow-100 text-yellow-800';\r\n      } else if (statusLower.includes('rejected') || statusLower.includes('banned') ||\r\n                statusLower.includes('failed') || statusLower.includes('error')) {\r\n        bgColor = 'bg-red-100 text-red-800';\r\n      } else if (statusLower.includes('inactive')) {\r\n        bgColor = 'bg-gray-100 text-gray-800';\r\n      }\r\n    }\r\n\r\n    return (\r\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${bgColor}`}>\r\n        {status}\r\n      </span>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-md ${className}`}\r\n      data-testid={testId}\r\n    >\r\n      {/* Header */}\r\n      {(title || description) && (\r\n        <div className={`px-6 py-4 border-b border-gray-100 ${headerClassName}`}>\r\n          {typeof title === 'string' ? (\r\n            <h3 className=\"text-lg font-semibold text-gray-800\">{title}</h3>\r\n          ) : (\r\n            title\r\n          )}\r\n          {typeof description === 'string' ? (\r\n            <p className=\"mt-1 text-sm text-gray-500\">{description}</p>\r\n          ) : (\r\n            description\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      {/* Search and Actions */}\r\n      <div className=\"p-4 border-b border-gray-100 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\r\n        <div className=\"relative flex-1\">\r\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n            <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\r\n          </div>\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"Search...\"\r\n            className=\"block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200\"\r\n            value={searchTerm}\r\n            onChange={(e) => {\r\n              setSearchTerm(e.target.value);\r\n              setCurrentPage(1); // Reset to first page on search\r\n            }}\r\n            data-testid={`${testId}-search`}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-2\">\r\n          {selectedRows.length > 0 && (\r\n            <div className=\"flex items-center space-x-2\">\r\n              <span className=\"text-sm text-gray-500\">{selectedRows.length} selected</span>\r\n              <button\r\n                className=\"px-3 py-1.5 bg-red-50 text-red-600 rounded-md text-sm font-medium hover:bg-red-100 transition-colors\"\r\n                onClick={() => {\r\n                  setSelectedRows([]);\r\n                  if (onSelectionChange) onSelectionChange([]);\r\n                }}\r\n                data-testid={`${testId}-clear-selection`}\r\n              >\r\n                Clear\r\n              </button>\r\n            </div>\r\n          )}\r\n          {actions}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Table */}\r\n      <div className={`overflow-x-auto ${bodyClassName}`}>\r\n        {loading ? (\r\n          <div className=\"flex justify-center items-center py-20\">\r\n            <LoadingSpinner size=\"lg\" variant=\"spinner\" />\r\n          </div>\r\n        ) : (\r\n          <table className=\"min-w-full divide-y divide-gray-100\">\r\n            <thead className=\"bg-gray-50\">\r\n              <tr>\r\n                {selectable && (\r\n                  <th className=\"w-12 px-6 py-3\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      className=\"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary\"\r\n                      onChange={handleSelectAll}\r\n                      checked={selectedRows.length === paginatedData.length && paginatedData.length > 0}\r\n                      data-testid={`${testId}-select-all`}\r\n                    />\r\n                  </th>\r\n                )}\r\n                {columns.map((column) => (\r\n                  <th\r\n                    key={column.key}\r\n                    className={`px-6 py-3 text-${column.align || 'left'} text-xs font-medium text-gray-500 uppercase tracking-wider ${column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''} transition-colors duration-200 ${column.width ? column.width : ''} ${column.className || ''}`}\r\n                    onClick={() => column.sortable && handleSort(column.key)}\r\n                    data-testid={`${testId}-column-${column.key}`}\r\n                  >\r\n                    <div className=\"flex items-center space-x-1\">\r\n                      <span>{column.label}</span>\r\n                      {column.sortable && (\r\n                        <span className={`transition-colors duration-200 ${\r\n                          sortConfig?.key === column.key ? 'text-primary' : 'text-gray-400'\r\n                        }`}>\r\n                          {sortConfig?.key === column.key && sortConfig.direction === 'asc'\r\n                            ? <ChevronUpIcon className=\"h-4 w-4\" />\r\n                            : sortConfig?.key === column.key && sortConfig.direction === 'desc'\r\n                              ? <ChevronDownIcon className=\"h-4 w-4\" />\r\n                              : <span className=\"text-gray-300\">↕</span>}\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                  </th>\r\n                ))}\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"bg-white divide-y divide-gray-100\">\r\n              {paginatedData.length > 0 ? (\r\n                paginatedData.map((row, index) => (\r\n                  <tr\r\n                    key={index}\r\n                    className={`group transition-all duration-200 ${\r\n                      onRowClick ? 'cursor-pointer' : ''\r\n                    } ${selectedRows.includes(index) ? 'bg-primary bg-opacity-5' : ''}\r\n                    ${hoveredRow === index ? 'bg-gray-50' : ''}\r\n                    ${rowClassName ? rowClassName(row, index) : ''}`}\r\n                    onClick={() => onRowClick && onRowClick(row)}\r\n                    onMouseEnter={() => setHoveredRow(index)}\r\n                    onMouseLeave={() => setHoveredRow(null)}\r\n                    data-testid={`${testId}-row-${index}`}\r\n                  >\r\n                    {selectable && (\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          className=\"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary\"\r\n                          checked={selectedRows.includes(index)}\r\n                          onChange={() => {}} // Empty handler to avoid React warning about controlled component\r\n                          onClick={(e) => handleRowSelect(index, e)}\r\n                          data-testid={`${testId}-row-${index}-checkbox`}\r\n                        />\r\n                      </td>\r\n                    )}\r\n                    {columns.map((column) => (\r\n                      <td\r\n                        key={column.key}\r\n                        className={`px-6 py-4 whitespace-nowrap text-sm text-gray-600 group-hover:text-gray-900 text-${column.align || 'left'} ${column.className || ''}`}\r\n                        data-testid={`${testId}-row-${index}-cell-${column.key}`}\r\n                      >\r\n                        {column.render\r\n                          ? column.render(row[column.key], row)\r\n                          : column.key.toLowerCase().includes('status')\r\n                            ? renderStatusBadge(row[column.key])\r\n                            : row[column.key]}\r\n                      </td>\r\n                    ))}\r\n                  </tr>\r\n                ))\r\n              ) : (\r\n                <tr>\r\n                  <td\r\n                    colSpan={columns.length + (selectable ? 1 : 0)}\r\n                    className=\"px-6 py-10 text-center text-gray-500\"\r\n                    data-testid={`${testId}-empty-message`}\r\n                  >\r\n                    {emptyMessage}\r\n                  </td>\r\n                </tr>\r\n              )}\r\n            </tbody>\r\n          </table>\r\n        )}\r\n      </div>\r\n\r\n      {/* Pagination */}\r\n      {pagination && totalPages > 1 && (\r\n        <div className={`px-6 py-4 border-t border-gray-100 flex items-center justify-between ${footerClassName}`}>\r\n          <div className=\"text-sm text-gray-500\">\r\n            Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, filteredData.length)} of {filteredData.length} entries\r\n          </div>\r\n          <div className=\"flex space-x-1\">\r\n            <button\r\n              onClick={() => handlePageChange(Math.max(1, currentPage - 1))}\r\n              disabled={currentPage === 1}\r\n              className={`px-3 py-1 rounded-md text-sm ${\r\n                currentPage === 1\r\n                  ? 'text-gray-400 cursor-not-allowed'\r\n                  : 'text-gray-700 hover:bg-gray-100'\r\n              }`}\r\n              data-testid={`${testId}-pagination-prev`}\r\n            >\r\n              Previous\r\n            </button>\r\n            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\r\n              // Show pages around current page\r\n              let pageNum: number;\r\n              if (totalPages <= 5) {\r\n                pageNum = i + 1;\r\n              } else if (currentPage <= 3) {\r\n                pageNum = i + 1;\r\n              } else if (currentPage >= totalPages - 2) {\r\n                pageNum = totalPages - 4 + i;\r\n              } else {\r\n                pageNum = currentPage - 2 + i;\r\n              }\r\n\r\n              return (\r\n                <button\r\n                  key={pageNum}\r\n                  onClick={() => handlePageChange(pageNum)}\r\n                  className={`px-3 py-1 rounded-md text-sm ${\r\n                    currentPage === pageNum\r\n                      ? 'bg-primary text-white'\r\n                      : 'text-gray-700 hover:bg-gray-100'\r\n                  }`}\r\n                  data-testid={`${testId}-pagination-${pageNum}`}\r\n                >\r\n                  {pageNum}\r\n                </button>\r\n              );\r\n            })}\r\n            <button\r\n              onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}\r\n              disabled={currentPage === totalPages}\r\n              className={`px-3 py-1 rounded-md text-sm ${\r\n                currentPage === totalPages\r\n                  ? 'text-gray-400 cursor-not-allowed'\r\n                  : 'text-gray-700 hover:bg-gray-100'\r\n              }`}\r\n              data-testid={`${testId}-pagination-next`}\r\n            >\r\n              Next\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default memo(DataTable) as typeof DataTable;\r\n\r\n\r\n\r\n\r\n", "/**\r\n * Button Component\r\n * \r\n * A reusable button component with various styles and states.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\nexport type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'danger' | 'success' | 'text' | 'link';\r\nexport type ButtonSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';\r\n\r\nexport interface ButtonProps {\r\n  children: ReactNode;\r\n  variant?: ButtonVariant;\r\n  size?: ButtonSize;\r\n  className?: string;\r\n  onClick?: () => void;\r\n  disabled?: boolean;\r\n  type?: 'button' | 'submit' | 'reset';\r\n  icon?: ReactNode;\r\n  iconPosition?: 'left' | 'right';\r\n  fullWidth?: boolean;\r\n  loading?: boolean;\r\n  rounded?: boolean;\r\n  href?: string;\r\n  target?: string;\r\n  rel?: string;\r\n  title?: string;\r\n  ariaLabel?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst Button: React.FC<ButtonProps> = ({\r\n  children,\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  onClick,\r\n  disabled = false,\r\n  type = 'button',\r\n  icon,\r\n  iconPosition = 'left',\r\n  fullWidth = false,\r\n  loading = false,\r\n  rounded = false,\r\n  href,\r\n  target,\r\n  rel,\r\n  title,\r\n  ariaLabel,\r\n  testId,\r\n}) => {\r\n  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2';\r\n  \r\n  const variantClasses = {\r\n    primary: 'bg-primary text-white hover:bg-primary/90 focus-visible:ring-primary',\r\n    secondary: 'bg-gray-200 text-gray-800 hover:bg-gray-300 focus-visible:ring-gray-300',\r\n    outline: 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus-visible:ring-primary',\r\n    danger: 'bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500',\r\n    success: 'bg-green-600 text-white hover:bg-green-700 focus-visible:ring-green-500',\r\n    text: 'bg-transparent text-primary hover:bg-gray-100 focus-visible:ring-primary',\r\n    link: 'bg-transparent text-primary hover:underline focus-visible:ring-transparent p-0',\r\n  };\r\n  \r\n  const sizeClasses = {\r\n    xs: 'text-xs px-2 py-1',\r\n    sm: 'text-xs px-3 py-1.5',\r\n    md: 'text-sm px-4 py-2',\r\n    lg: 'text-base px-5 py-2.5',\r\n    xl: 'text-lg px-6 py-3',\r\n  };\r\n  \r\n  const disabledClasses = disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer';\r\n  const widthClass = fullWidth ? 'w-full' : '';\r\n  const roundedClass = rounded ? 'rounded-full' : 'rounded-lg';\r\n  \r\n  const buttonClasses = `\r\n    ${baseClasses}\r\n    ${variantClasses[variant]}\r\n    ${sizeClasses[size]}\r\n    ${disabledClasses}\r\n    ${widthClass}\r\n    ${roundedClass}\r\n    ${className}\r\n  `;\r\n  \r\n  const content = (\r\n    <>\r\n      {loading && (\r\n        <svg\r\n          className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-current\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n          aria-hidden=\"true\"\r\n        >\r\n          <circle\r\n            className=\"opacity-25\"\r\n            cx=\"12\"\r\n            cy=\"12\"\r\n            r=\"10\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"4\"\r\n          />\r\n          <path\r\n            className=\"opacity-75\"\r\n            fill=\"currentColor\"\r\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n          />\r\n        </svg>\r\n      )}\r\n\r\n      {icon && iconPosition === 'left' && !loading && (\r\n        <span className=\"mr-2\">{icon}</span>\r\n      )}\r\n\r\n      {children}\r\n\r\n      {icon && iconPosition === 'right' && (\r\n        <span className=\"ml-2\">{icon}</span>\r\n      )}\r\n    </>\r\n  );\r\n  \r\n  // If href is provided, render an anchor tag\r\n  if (href) {\r\n    return (\r\n      <a\r\n        href={href}\r\n        className={buttonClasses}\r\n        target={target}\r\n        rel={rel || (target === '_blank' ? 'noopener noreferrer' : undefined)}\r\n        onClick={onClick}\r\n        title={title}\r\n        aria-label={ariaLabel}\r\n        data-testid={testId}\r\n      >\r\n        {content}\r\n      </a>\r\n    );\r\n  }\r\n  \r\n  // Otherwise render a button\r\n  return (\r\n    <button\r\n      type={type}\r\n      className={buttonClasses}\r\n      onClick={onClick}\r\n      disabled={disabled || loading}\r\n      title={title}\r\n      aria-label={ariaLabel}\r\n      data-testid={testId}\r\n    >\r\n      {content}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default memo(Button);\r\n", "/**\r\n * Modal Component\r\n * \r\n * A reusable modal dialog component.\r\n */\r\n\r\nimport React, { Fragment, useEffect, useRef, memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\nimport { XMarkIcon } from '@heroicons/react/24/outline';\r\nimport { createPortal } from 'react-dom';\r\n\r\nexport interface ModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title: string | ReactNode;\r\n  children: ReactNode;\r\n  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full';\r\n  footer?: ReactNode;\r\n  closeOnEsc?: boolean;\r\n  closeOnBackdropClick?: boolean;\r\n  showCloseButton?: boolean;\r\n  centered?: boolean;\r\n  className?: string;\r\n  bodyClassName?: string;\r\n  headerClassName?: string;\r\n  footerClassName?: string;\r\n  backdropClassName?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst Modal: React.FC<ModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  children,\r\n  size = 'md',\r\n  footer,\r\n  closeOnEsc = true,\r\n  closeOnBackdropClick = true,\r\n  showCloseButton = true,\r\n  centered = true,\r\n  className = '',\r\n  bodyClassName = '',\r\n  headerClassName = '',\r\n  footerClassName = '',\r\n  backdropClassName = '',\r\n  testId,\r\n}) => {\r\n  const modalRef = useRef<HTMLDivElement>(null);\r\n  \r\n  // Handle Escape key press\r\n  useEffect(() => {\r\n    const handleEscape = (e: KeyboardEvent) => {\r\n      if (closeOnEsc && e.key === 'Escape') {\r\n        onClose();\r\n      }\r\n    };\r\n\r\n    if (isOpen) {\r\n      document.addEventListener('keydown', handleEscape);\r\n      // Prevent scrolling on the body when modal is open\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener('keydown', handleEscape);\r\n      document.body.style.overflow = 'auto';\r\n    };\r\n  }, [isOpen, onClose, closeOnEsc]);\r\n  \r\n  // Focus trap inside modal\r\n  useEffect(() => {\r\n    if (!isOpen || !modalRef.current) return;\r\n    \r\n    const focusableElements = modalRef.current.querySelectorAll(\r\n      'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\r\n    );\r\n    \r\n    if (focusableElements.length === 0) return;\r\n    \r\n    const firstElement = focusableElements[0] as HTMLElement;\r\n    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;\r\n    \r\n    const handleTabKey = (e: KeyboardEvent) => {\r\n      if (e.key !== 'Tab') return;\r\n      \r\n      if (e.shiftKey) {\r\n        if (document.activeElement === firstElement) {\r\n          lastElement.focus();\r\n          e.preventDefault();\r\n        }\r\n      } else {\r\n        if (document.activeElement === lastElement) {\r\n          firstElement.focus();\r\n          e.preventDefault();\r\n        }\r\n      }\r\n    };\r\n    \r\n    document.addEventListener('keydown', handleTabKey);\r\n    firstElement.focus();\r\n    \r\n    return () => {\r\n      document.removeEventListener('keydown', handleTabKey);\r\n    };\r\n  }, [isOpen]);\r\n\r\n  if (!isOpen) return null;\r\n  \r\n  // Size classes\r\n  const sizeClasses = {\r\n    xs: 'max-w-xs',\r\n    sm: 'max-w-md',\r\n    md: 'max-w-lg',\r\n    lg: 'max-w-2xl',\r\n    xl: 'max-w-4xl',\r\n    full: 'max-w-full mx-4',\r\n  };\r\n  \r\n  // Modal content\r\n  const modalContent = (\r\n    <Fragment>\r\n      {/* Backdrop */}\r\n      <div \r\n        className={`fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity ${backdropClassName}`}\r\n        onClick={closeOnBackdropClick ? onClose : undefined}\r\n        data-testid={`${testId}-backdrop`}\r\n      />\r\n\r\n      {/* Modal */}\r\n      <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n        <div className={`flex min-h-full items-${centered ? 'center' : 'start'} justify-center p-4 text-center`}>\r\n          <div \r\n            ref={modalRef}\r\n            className={`${sizeClasses[size]} w-full transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all ${className}`}\r\n            onClick={(e) => e.stopPropagation()}\r\n            data-testid={testId}\r\n          >\r\n            {/* Header */}\r\n            <div className={`flex items-center justify-between px-6 py-4 border-b border-gray-100 ${headerClassName}`}>\r\n              {typeof title === 'string' ? (\r\n                <h3 className=\"text-lg font-semibold text-gray-800\">{title}</h3>\r\n              ) : (\r\n                title\r\n              )}\r\n              {showCloseButton && (\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary rounded-full p-1\"\r\n                  onClick={onClose}\r\n                  aria-label=\"Close modal\"\r\n                  data-testid={`${testId}-close-button`}\r\n                >\r\n                  <XMarkIcon className=\"h-6 w-6\" />\r\n                </button>\r\n              )}\r\n            </div>\r\n\r\n            {/* Content */}\r\n            <div className={`px-6 py-4 ${bodyClassName}`}>\r\n              {children}\r\n            </div>\r\n\r\n            {/* Footer */}\r\n            {footer && (\r\n              <div className={`px-6 py-4 bg-gray-50 border-t border-gray-100 flex justify-end space-x-3 ${footerClassName}`}>\r\n                {footer}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Fragment>\r\n  );\r\n  \r\n  // Use portal to render modal at the end of the document body\r\n  return createPortal(modalContent, document.body);\r\n};\r\n\r\nexport default memo(Modal);\r\n", "import * as React from \"react\";\nfunction EnvelopeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EnvelopeIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction CheckIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m4.5 12.75 6 6 9-13.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CheckIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction EyeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EyeIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction PhoneIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PhoneIcon);\nexport default ForwardRef;"], "names": ["_ref", "verifications", "onViewVerification", "onApproveClick", "onRejectClick", "title", "description", "columns", "key", "label", "sortable", "render", "_value", "verification", "_jsxs", "className", "children", "_jsx", "Avatar", "alt", "companyName", "name", "size", "id", "value", "EnvelopeIcon", "PhoneIcon", "ClockIcon", "icon", "CheckCircleIcon", "XCircleIcon", "char<PERSON>t", "toUpperCase", "slice", "Date", "toLocaleDateString", "_", "onClick", "e", "stopPropagation", "EyeIcon", "CheckIcon", "XMarkIcon", "DataTable", "data", "pagination", "selectable", "onClose", "onApprove", "onReject", "<PERSON><PERSON><PERSON>", "email", "phone", "submittedDate", "toLocaleString", "documents", "map", "doc", "index", "type", "<PERSON><PERSON>", "variant", "href", "url", "target", "isOpen", "Modal", "footer", "_Fragment", "rejectReason", "setRejectReason", "htmlFor", "rows", "placeholder", "onChange", "getVerifications", "async", "response", "apiClient", "get", "params", "responseValidators", "getList", "error", "handleApiError", "getVerificationById", "getById", "createVerification", "post", "verificationData", "create", "updateVerificationStatus", "status", "put", "update", "getVerificationsByStatus", "getVerificationsByUser", "userId", "updateVerification", "approveVerification", "notes", "rejectVerification", "useVerifications", "setVerifications", "useState", "isLoading", "setIsLoading", "setError", "showNotification", "useNotification", "showNotificationRef", "useRef", "hasInitialFetched", "useEffect", "current", "fetchVerifications", "useCallback", "verificationsApi", "err", "message", "updatedVerification", "prevVerifications", "arguments", "length", "undefined", "approvedVerification", "rejectedVerification", "VerificationsPage", "showSuccess", "showError", "selectedVerification", "setSelectedVerification", "isViewModalOpen", "setIsViewModalOpen", "isApproveModalOpen", "setIsApproveModalOpen", "isRejectModalOpen", "setIsRejectModalOpen", "processedVerificationIds", "setProcessedVerificationIds", "Set", "displayedVerifications", "useMemo", "filter", "has", "<PERSON><PERSON><PERSON><PERSON>", "breadcrumbs", "Card", "LoadingSpinner", "VerificationList", "VerificationDetails", "ApproveVerificationModal", "prev", "add", "console", "RejectVerificationModal", "trim", "actions", "testId", "Link", "to", "HomeIcon", "item", "ChevronRightIcon", "path", "memo", "subtitle", "bodyClassName", "headerClassName", "footerClassName", "hoverable", "noPadding", "bordered", "loading", "cardClasses", "headerClasses", "bodyClasses", "footerClasses", "color", "useCurrentColor", "sizeMap", "sm", "spinner", "dots", "pulse", "ripple", "md", "lg", "currentColor", "role", "style", "borderTopColor", "borderRightColor", "backgroundColor", "svgRef", "titleId", "props", "React", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "strokeLinecap", "strokeLinejoin", "d", "MagnifyingGlassIcon", "ChevronUpIcon", "onRowClick", "pageSize", "CONFIG", "DEFAULT_PAGE_SIZE", "onSelectionChange", "emptyMessage", "rowClassName", "initialSortKey", "initialSortDirection", "sortConfig", "setSortConfig", "direction", "searchTerm", "setSearchTerm", "currentPage", "setCurrentPage", "selectedRows", "setSelectedRows", "hoveredRow", "setHoveredRow", "sortedData", "sort", "a", "b", "aValue", "bValue", "localeCompare", "filteredData", "row", "entries", "some", "_ref2", "_key", "String", "toLowerCase", "includes", "totalPages", "Math", "ceil", "paginatedData", "startIndex", "handlePageChange", "page", "renderStatusBadge", "bgColor", "statusLower", "event", "newSelectedRows", "checked", "Array", "from", "i", "selectedItems", "idx", "column", "align", "width", "handleSort", "ChevronDownIcon", "onMouseEnter", "onMouseLeave", "handleRowSelect", "indexOf", "splice", "push", "colSpan", "min", "max", "disabled", "pageNum", "iconPosition", "fullWidth", "rounded", "rel", "aria<PERSON><PERSON><PERSON>", "buttonClasses", "primary", "secondary", "outline", "danger", "success", "text", "link", "xs", "xl", "content", "cx", "cy", "r", "closeOnEsc", "closeOnBackdropClick", "showCloseButton", "centered", "backdropClassName", "modalRef", "handleEscape", "document", "addEventListener", "body", "overflow", "removeEventListener", "focusableElements", "querySelectorAll", "firstElement", "lastElement", "handleTabKey", "shift<PERSON>ey", "activeElement", "focus", "preventDefault", "modalContent", "Fragment", "full", "createPortal"], "sourceRoot": ""}