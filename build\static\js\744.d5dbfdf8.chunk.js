"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[744],{1512:(e,r,t)=>{t.d(r,{A:()=>l});var s=t(5043),n=t(4703),a=t(9705);const l=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{enableNotifications:r=!0,enableReporting:t=!0,onError:l}=e,{showNotification:o}=(0,a.A)(),[i,c]=(0,s.useState)({hasError:!1,error:null,errorType:null}),d=(0,s.useCallback)((()=>{c({hasError:!1,error:null,errorType:null})}),[]),u=(0,s.useCallback)(((e,s)=>{const a=(0,n.hS)(e,r?e=>{o({type:e.type,title:e.title,message:e.message})}:void 0);return c({hasError:!0,error:a,errorType:"api",...s&&{context:s}}),t&&e instanceof Error&&(0,n.N7)(e,s),l&&l(e,s),a}),[r,t,o,l]),m=(0,s.useCallback)(((e,t,s,a)=>{const i=(0,n.co)(e,t,s);return c({hasError:!0,error:i,errorType:"validation",...a&&{context:a}}),r&&o({type:"error",title:"Validation Error",message:i.message}),l&&l(i,a),i}),[r,o,l]),x=(0,s.useCallback)(((e,s,a)=>{(0,n.NC)(e,s,r?e=>{o({type:e.type,title:e.title,message:e.message})}:void 0),c({hasError:!0,error:e,errorType:"form",...a&&{context:a}}),t&&e instanceof Error&&(0,n.N7)(e,a),l&&l(e,a)}),[r,t,o,l]),h=(0,s.useCallback)(((e,s)=>{const a=e instanceof Error?e:new Error(String(e));return c({hasError:!0,error:a,errorType:"general",...s&&{context:s}}),r&&o({type:"error",title:"Error",message:a.message}),t&&(0,n.N7)(a,s),(0,n.vV)(a,s),l&&l(e,s),a}),[r,t,o,l]),g=(0,s.useCallback)((async(e,r)=>{try{return d(),await e()}catch(t){return u(t,r),null}}),[d,u]),p=(0,s.useCallback)((async(e,r,t)=>{try{return d(),await e()}catch(s){return x(s,r,t),null}}),[d,x]);return{...i,handleApiError:u,handleValidationError:m,handleFormError:x,handleGeneralError:h,clearError:d,withErrorHandling:g,withFormErrorHandling:p,isApiError:e=>e&&"object"===typeof e&&"status"in e,isValidationError:e=>e&&"object"===typeof e&&"field"in e}}},3619:(e,r,t)=>{t.d(r,{Ay:()=>c});var s=t(5043),n=t(7515),a=t(4703),l=t(579);const o=e=>{let{error:r,resetError:t}=e;return(0,l.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[200px] p-4 border border-red-200 rounded-lg bg-red-50",children:[(0,l.jsx)("div",{className:"text-red-500 text-2xl mb-2",children:"\u26a0\ufe0f"}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-red-800 mb-2",children:"Something went wrong"}),(0,l.jsx)("p",{className:"text-red-600 text-sm mb-4 text-center max-w-md",children:r.message||"An unexpected error occurred"}),(0,l.jsx)("button",{onClick:t,className:"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors",children:"Try Again"})]})};function i(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{fallback:t=o,onError:i,enableReporting:c=!0,context:d}=r,u=(0,s.forwardRef)(((r,s)=>(0,l.jsx)(n.A,{fallback:(0,l.jsx)(t,{error:new Error,resetError:()=>window.location.reload()}),onError:(r,t)=>{c&&(0,a.N7)(r,d||e.displayName||e.name,{componentStack:t.componentStack,errorBoundary:!0}),i&&i(r,t)},children:(0,l.jsx)(e,{...r,ref:s})})));return u.displayName=`withErrorBoundary(${e.displayName||e.name})`,u}const c=i},3893:(e,r,t)=>{t.d(r,{Yq:()=>s,vv:()=>n});const s=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return"-";try{const t=new Date(e),s={year:"numeric",month:"short",day:"numeric",...r};return new Intl.DateTimeFormat("en-US",s).format(t)}catch(t){return console.error("Error formatting date:",t),e}},n=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD",t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"en-US";try{return new Intl.NumberFormat(t,{style:"currency",currency:r,minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}catch(s){return console.error("Error formatting currency:",s),`${r} ${e.toFixed(2)}`}}},4692:(e,r,t)=>{t.d(r,{A:()=>c});t(5043);var s=t(4538),n=t(7012),a=t(7098),l=t(5889),o=t(3867),i=t(579);const c=e=>{let{status:r,type:t="user",className:c=""}=e;if(!r)return(0,i.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 ${c}`,children:"Unknown"});const d=r.toLowerCase();let u="",m=null;"active"===d||"verified"===d||"completed"===d?(u="bg-green-100 text-green-800",m=(0,i.jsx)(s.A,{className:"w-4 h-4 mr-1"})):"pending"===d||"processing"===d?(u="bg-blue-100 text-blue-800",m=(0,i.jsx)(n.A,{className:"w-4 h-4 mr-1"})):"banned"===d||"rejected"===d?(u="bg-red-100 text-red-800",m=(0,i.jsx)(a.A,{className:"w-4 h-4 mr-1"})):"shipped"===d?(u="bg-purple-100 text-purple-800",m=(0,i.jsx)(l.A,{className:"w-4 h-4 mr-1"})):"warning"===d?(u="bg-yellow-100 text-yellow-800",m=(0,i.jsx)(o.A,{className:"w-4 h-4 mr-1"})):u="bg-gray-100 text-gray-800";const x=r?r.charAt(0).toUpperCase()+r.slice(1):"Unknown";return(0,i.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${u} ${c}`,children:[m,x]})}},4791:(e,r,t)=>{t.d(r,{A:()=>a});var s=t(5043);function n(e,r){let{title:t,titleId:n,...a}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":n},a),t?s.createElement("title",{id:n},t):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"}))}const a=s.forwardRef(n)},5445:(e,r,t)=>{t.d(r,{A:()=>a});var s=t(7422),n=t(579);const a=e=>{let{data:r,columns:t,onRowClick:a,title:l,pagination:o=!0,loading:i=!1,emptyMessage:c="No data available",className:d=""}=e;return(0,n.jsx)(s.A,{columns:t,data:r,onRowClick:a,title:l,pagination:o,loading:i,emptyMessage:c,className:d})}},6944:(e,r,t)=>{t.r(r),t.d(r,{default:()=>g});var s=t(5043),n=t(7907),a=t(3593),l=t(8100),o=t(4791);function i(e,r){let{title:t,titleId:n,...a}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":n},a),t?s.createElement("title",{id:n},t):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0ZM3 19.235v-.11a6.375 6.375 0 0 1 12.75 0v.109A12.318 12.318 0 0 1 9.374 21c-2.331 0-4.512-.645-6.374-1.766Z"}))}const c=s.forwardRef(i);var d=t(5892),u=t(1512),m=t(4703),x=t(3619),h=t(579);const g=(0,x.Ay)((()=>{const[e,r]=(0,s.useState)("all"),[t,i]=(0,s.useState)(!1),[x,g]=(0,s.useState)(!1),[p,f]=(0,s.useState)(null),[y,b]=(0,s.useState)(!1),[w,v]=(0,s.useState)(!1),[j,k]=(0,s.useState)(null),[E,N]=(0,s.useState)(!1),{users:C,isLoading:A,createEntity:U,deleteEntity:S,updateUserStatus:$}=(0,d.kp)(),{handleGeneralError:F,withErrorHandling:D,withFormErrorHandling:L,clearError:T}=(0,u.A)({enableNotifications:!0,enableReporting:!0}),R=(0,s.useMemo)((()=>"all"===e?C:C.filter((r=>r.status===e))),[C,e]),z=(0,s.useCallback)((e=>{f(e),b(!0)}),[]),M=(0,s.useCallback)((e=>{z(e)}),[z]),B=(0,s.useCallback)((e=>{k(e),v(!0)}),[]),O=(0,s.useCallback)((async()=>{if(!j)return;N(!0);const e=await D((async()=>(await S(j.id),!0)),`Delete user ${j.name}`);N(!1),e?(v(!1),k(null)):console.error("Failed to delete user")}),[j,D,S]),P=(0,s.useCallback)((async()=>{i(!0);const e=await(0,m.fN)((async()=>(await new Promise(((e,r)=>{setTimeout((()=>{Math.random()<.1?r(new Error("Export failed")):e(!0)}),1500)})),console.log("Exporting users..."),!0)),{timeout:5e3,retries:2,operationName:"Export Users"});e.success||F(e.error,"Export Users"),i(!1)}),[F]),V=(0,s.useCallback)((async e=>{i(!0),T();const r=await L((async()=>{const r=await U(e);return g(!1),r}),void 0,"Add User");i(!1),r&&console.log("User added successfully:",r)}),[L,U,T]),I=(0,s.useCallback)((async e=>{const r=C.find((r=>r.id===e));if(!r)return;const t="active"===r.status?"banned":"active";await D((async()=>(await $(e,t),p&&p.id===e&&f({...p,status:t}),b(!1),!0)),`Toggle status for user ${r.name}`)||console.error("Failed to toggle user status")}),[C,D,$,p]);return(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("h1",{className:"text-2xl font-bold text-gray-800",children:"Users"}),(0,h.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Manage your users and their permissions"})]}),(0,h.jsxs)("div",{className:"flex flex-wrap gap-3",children:[(0,h.jsx)(n.A,{variant:"outline",icon:(0,h.jsx)(o.A,{className:"h-5 w-5"}),onClick:P,loading:t||A,children:"Export Users"}),(0,h.jsx)(n.A,{icon:(0,h.jsx)(c,{className:"h-5 w-5"}),onClick:()=>g(!0),children:"Add User"})]})]}),(0,h.jsxs)(a.A,{children:[(0,h.jsxs)("div",{className:"flex flex-wrap gap-3 mb-6",children:[(0,h.jsx)(n.A,{variant:"all"===e?"primary":"outline",size:"sm",onClick:()=>r("all"),children:"All Users"}),(0,h.jsx)(n.A,{variant:"active"===e?"primary":"outline",size:"sm",onClick:()=>r("active"),children:"Active Users"}),(0,h.jsx)(n.A,{variant:"banned"===e?"primary":"outline",size:"sm",onClick:()=>r("banned"),children:"Banned Users"})]}),(0,h.jsx)(d.w2,{users:R,onViewUser:z,onEditUser:e=>{console.log("Edit user (deprecated):",e)},onDeleteUser:B,onUserClick:M,title:`${e.charAt(0).toUpperCase()+e.slice(1)} (${R.length})`,loading:A})]}),(0,h.jsx)(l.A,{isOpen:x,onClose:()=>g(!1),title:"Add New User",size:"lg",children:(0,h.jsx)(d.pC,{onSubmit:V,onCancel:()=>g(!1),isLoading:t})}),p&&(0,h.jsx)(l.A,{isOpen:y,onClose:()=>b(!1),title:"User Details",size:"lg",footer:(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(n.A,{variant:"outline",onClick:()=>b(!1),children:"Close"}),(0,h.jsx)(n.A,{variant:"active"===p.status?"danger":"success",onClick:()=>I(p.id),children:"active"===p.status?"Ban User":"Activate User"})]}),children:(0,h.jsx)(d.Pt,{user:p})}),j&&(0,h.jsx)(l.A,{isOpen:w,onClose:()=>v(!1),title:"Delete User",size:"sm",footer:(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(n.A,{variant:"outline",onClick:()=>v(!1),disabled:E,children:"Cancel"}),(0,h.jsx)(n.A,{variant:"danger",onClick:O,loading:E,children:"Delete User"})]}),children:(0,h.jsxs)("div",{className:"text-sm text-gray-500",children:['Are you sure you want to delete "',j.name,'"? This action cannot be undone.']})})]})}),{fallback:e=>{let{error:r,resetError:t}=e;return(0,h.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[400px] p-8",children:[(0,h.jsx)("div",{className:"text-red-500 text-4xl mb-4",children:"\u26a0\ufe0f"}),(0,h.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Users Page Error"}),(0,h.jsx)("p",{className:"text-gray-600 mb-4 text-center max-w-md",children:r.message||"An error occurred while loading the users page"}),(0,h.jsx)("button",{onClick:t,className:"px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition-colors",children:"Reload Page"})]})},context:"UsersPage"})},8100:(e,r,t)=>{t.d(r,{A:()=>i});var s=t(5043),n=t(7591),a=t(7950),l=t(579);const o=e=>{let{isOpen:r,onClose:t,title:o,children:i,size:c="md",footer:d,closeOnEsc:u=!0,closeOnBackdropClick:m=!0,showCloseButton:x=!0,centered:h=!0,className:g="",bodyClassName:p="",headerClassName:f="",footerClassName:y="",backdropClassName:b="",testId:w}=e;const v=(0,s.useRef)(null);if((0,s.useEffect)((()=>{const e=e=>{u&&"Escape"===e.key&&t()};return r&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="auto"}}),[r,t,u]),(0,s.useEffect)((()=>{if(!r||!v.current)return;const e=v.current.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');if(0===e.length)return;const t=e[0],s=e[e.length-1],n=e=>{"Tab"===e.key&&(e.shiftKey?document.activeElement===t&&(s.focus(),e.preventDefault()):document.activeElement===s&&(t.focus(),e.preventDefault()))};return document.addEventListener("keydown",n),t.focus(),()=>{document.removeEventListener("keydown",n)}}),[r]),!r)return null;const j=(0,l.jsxs)(s.Fragment,{children:[(0,l.jsx)("div",{className:`fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity ${b}`,onClick:m?t:void 0,"data-testid":`${w}-backdrop`}),(0,l.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,l.jsx)("div",{className:`flex min-h-full items-${h?"center":"start"} justify-center p-4 text-center`,children:(0,l.jsxs)("div",{ref:v,className:`${{xs:"max-w-xs",sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[c]} w-full transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all ${g}`,onClick:e=>e.stopPropagation(),"data-testid":w,children:[(0,l.jsxs)("div",{className:`flex items-center justify-between px-6 py-4 border-b border-gray-100 ${f}`,children:["string"===typeof o?(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:o}):o,x&&(0,l.jsx)("button",{type:"button",className:"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary rounded-full p-1",onClick:t,"aria-label":"Close modal","data-testid":`${w}-close-button`,children:(0,l.jsx)(n.A,{className:"h-6 w-6"})})]}),(0,l.jsx)("div",{className:`px-6 py-4 ${p}`,children:i}),d&&(0,l.jsx)("div",{className:`px-6 py-4 bg-gray-50 border-t border-gray-100 flex justify-end space-x-3 ${y}`,children:d})]})})})]});return(0,a.createPortal)(j,document.body)},i=(0,s.memo)(o)}}]);
//# sourceMappingURL=744.d5dbfdf8.chunk.js.map