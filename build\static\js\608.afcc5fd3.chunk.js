"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[608],{679:(e,r,t)=>{t.r(r),t.d(r,{default:()=>u});var s=t(5043),a=t(3216),i=t(2806),n=t(2659),l=t(5892),d=t(245),c=t(3927),o=t(579);const u=()=>{const{id:e}=(0,a.g)(),r=(0,a.Zp)(),t=e||"",{getUserById:u,updateUser:m}=(0,l.kp)({initialFetch:!1}),{getOrdersByCustomer:x}=(0,d.h)(),[b,h]=(0,s.useState)(null),[p,y]=(0,s.useState)([]),[f,g]=(0,s.useState)(!0),[v,j]=(0,s.useState)(!1),[k,N]=(0,s.useState)("details"),[w,A]=(0,s.useState)(null);(0,s.useEffect)((()=>{if(!t)return g(!1),void A("No user ID provided");(async()=>{try{g(!0),A(null);const e=await u(t);h(e);const r=await x(t);y(r)}catch(w){console.error("Error fetching user data:",w);const r=w instanceof Error?w.message:"Failed to fetch user data";A(r)}finally{g(!1)}})()}),[t,x,u]);const C=(0,s.useCallback)((async e=>{if(b)try{j(!0);const r=await m(b.id,e);h(r),N("details")}catch(w){throw console.error("Error updating user:",w),w}finally{j(!1)}}),[b,m]);if(f)return(0,o.jsx)("div",{className:"flex justify-center items-center min-h-screen",children:(0,o.jsx)(c.A,{size:"lg"})});if(w&&!b)return(0,o.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[400px] space-y-4",children:[(0,o.jsx)("div",{className:"text-red-600 text-lg font-medium",children:"Error Loading User"}),(0,o.jsx)("div",{className:"text-gray-600",children:w}),(0,o.jsx)("button",{onClick:()=>r("/users"),className:"px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark",children:"Back to Users"})]});if(!b)return(0,o.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[400px] space-y-4",children:[(0,o.jsx)("div",{className:"text-gray-600 text-lg font-medium",children:"User not found"}),(0,o.jsx)("button",{onClick:()=>r("/users"),className:"px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark",children:"Back to Users"})]});const S={totalOrders:p.length,totalSpent:p.reduce(((e,r)=>e+r.totalAmount),0),averageOrderValue:p.length>0?p.reduce(((e,r)=>e+r.totalAmount),0)/p.length:0,orderFrequency:0,orderHistory:p.map((e=>({date:e.orderDate,amount:e.totalAmount})))};return(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsx)(i.A,{title:`User: ${b.name}`,description:"View and edit user details",breadcrumbs:[{label:"Users",path:"/users"},{label:b.name}]}),(0,o.jsx)(n.A,{tabs:[{id:"details",label:"Details"},{id:"edit",label:"Edit"},{id:"analytics",label:"Analytics"}],activeTab:k,onChange:N}),"details"===k&&(0,o.jsx)(l.ID,{user:b,userOrders:p}),"edit"===k&&(0,o.jsx)(l.Sk,{user:b,onSubmit:C,isLoading:v}),"analytics"===k&&(0,o.jsx)(l.cA,{userId:t,userData:S})]})}},2659:(e,r,t)=>{t.d(r,{A:()=>a});t(5043);var s=t(579);const a=e=>{let{tabs:r,activeTab:t,onChange:a,className:i=""}=e;return(0,s.jsx)("div",{className:`border-b border-gray-200 ${i}`,children:(0,s.jsx)("nav",{className:"-mb-px flex space-x-8",children:r.map((e=>{const r=e.id===t;return(0,s.jsx)("button",{onClick:()=>!e.disabled&&a(e.id),className:`\n                whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm\n                focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2\n                ${r?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}\n                ${e.disabled?"opacity-50 cursor-not-allowed":"cursor-pointer"}\n              `,disabled:e.disabled,children:e.label},e.id)}))})})}}}]);
//# sourceMappingURL=608.afcc5fd3.chunk.js.map