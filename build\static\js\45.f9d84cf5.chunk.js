"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[45],{1512:(e,t,s)=>{s.d(t,{A:()=>n});var r=s(5043),a=s(4703),l=s(9705);const n=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{enableNotifications:t=!0,enableReporting:s=!0,onError:n}=e,{showNotification:i}=(0,l.A)(),[o,c]=(0,r.useState)({hasError:!1,error:null,errorType:null}),d=(0,r.useCallback)((()=>{c({hasError:!1,error:null,errorType:null})}),[]),m=(0,r.useCallback)(((e,r)=>{const l=(0,a.hS)(e,t?e=>{i({type:e.type,title:e.title,message:e.message})}:void 0);return c({hasError:!0,error:l,errorType:"api",...r&&{context:r}}),s&&e instanceof Error&&(0,a.N7)(e,r),n&&n(e,r),l}),[t,s,i,n]),u=(0,r.useCallback)(((e,s,r,l)=>{const o=(0,a.co)(e,s,r);return c({hasError:!0,error:o,errorType:"validation",...l&&{context:l}}),t&&i({type:"error",title:"Validation Error",message:o.message}),n&&n(o,l),o}),[t,i,n]),x=(0,r.useCallback)(((e,r,l)=>{(0,a.NC)(e,r,t?e=>{i({type:e.type,title:e.title,message:e.message})}:void 0),c({hasError:!0,error:e,errorType:"form",...l&&{context:l}}),s&&e instanceof Error&&(0,a.N7)(e,l),n&&n(e,l)}),[t,s,i,n]),h=(0,r.useCallback)(((e,r)=>{const l=e instanceof Error?e:new Error(String(e));return c({hasError:!0,error:l,errorType:"general",...r&&{context:r}}),t&&i({type:"error",title:"Error",message:l.message}),s&&(0,a.N7)(l,r),(0,a.vV)(l,r),n&&n(e,r),l}),[t,s,i,n]),g=(0,r.useCallback)((async(e,t)=>{try{return d(),await e()}catch(s){return m(s,t),null}}),[d,m]),p=(0,r.useCallback)((async(e,t,s)=>{try{return d(),await e()}catch(r){return x(r,t,s),null}}),[d,x]);return{...o,handleApiError:m,handleValidationError:u,handleFormError:x,handleGeneralError:h,clearError:d,withErrorHandling:g,withFormErrorHandling:p,isApiError:e=>e&&"object"===typeof e&&"status"in e,isValidationError:e=>e&&"object"===typeof e&&"field"in e}}},2806:(e,t,s)=>{s.d(t,{A:()=>c});var r=s(5043),a=s(5475),l=s(5501),n=s(6365),i=s(579);const o=e=>{let{title:t,description:s,actions:r,breadcrumbs:o,className:c="",testId:d}=e;return(0,i.jsxs)("div",{className:`mb-6 ${c}`,"data-testid":d,children:[o&&o.length>0&&(0,i.jsx)("nav",{className:"flex mb-4","aria-label":"Breadcrumb",children:(0,i.jsxs)("ol",{className:"flex items-center space-x-1 text-sm text-gray-500",children:[(0,i.jsx)("li",{children:(0,i.jsx)(a.N_,{to:"/",className:"flex items-center hover:text-primary","aria-label":"Home",children:(0,i.jsx)(l.A,{className:"h-4 w-4"})})}),o.map(((e,t)=>(0,i.jsxs)("li",{className:"flex items-center",children:[(0,i.jsx)(n.A,{className:"h-4 w-4 mx-1 text-gray-400"}),e.path&&t<o.length-1?(0,i.jsx)(a.N_,{to:e.path,className:"hover:text-primary",children:e.label}):(0,i.jsx)("span",{className:"font-medium text-gray-700",children:e.label})]},t)))]})}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-2xl font-bold text-gray-800",children:t}),s&&"string"===typeof s?(0,i.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:s}):s]}),r&&(0,i.jsx)("div",{className:"flex flex-wrap gap-3 mt-2 sm:mt-0",children:r})]})]})},c=(0,r.memo)(o)},3593:(e,t,s)=>{s.d(t,{A:()=>n});var r=s(5043),a=s(579);const l=e=>{let{title:t,subtitle:s,children:r,className:l="",bodyClassName:n="",headerClassName:i="",footerClassName:o="",icon:c,footer:d,onClick:m,hoverable:u=!1,noPadding:x=!1,bordered:h=!0,loading:g=!1,testId:p}=e;const y=`\n    bg-white rounded-xl ${h?"border border-gray-100":""} overflow-hidden transition-all duration-300\n    ${u?"hover:shadow-md hover:border-gray-200 transform hover:-translate-y-1":"shadow-sm"}\n    ${m?"cursor-pointer":""}\n    ${l}\n  `,b=`\n    px-6 py-4 border-b border-gray-100 flex items-center justify-between\n    ${i}\n  `,f=`\n    ${x?"":"p-6"}\n    ${n}\n  `,v=`\n    px-6 py-4 bg-gray-50 border-t border-gray-100\n    ${o}\n  `;return g?(0,a.jsxs)("div",{className:y,"data-testid":p,children:[(t||s||c)&&(0,a.jsxs)("div",{className:b,children:[(0,a.jsxs)("div",{className:"w-full",children:[t&&(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 animate-pulse"}),s&&(0,a.jsx)("div",{className:"h-4 mt-2 bg-gray-200 rounded w-1/2 animate-pulse"})]}),c&&(0,a.jsx)("div",{className:"h-8 w-8 bg-gray-200 rounded-full animate-pulse"})]}),(0,a.jsx)("div",{className:f,children:(0,a.jsx)("div",{className:"h-24 bg-gray-200 rounded animate-pulse"})}),d&&(0,a.jsx)("div",{className:v,children:(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 animate-pulse"})})]}):(0,a.jsxs)("div",{className:y,onClick:m,"data-testid":p,children:[(t||s||c)&&(0,a.jsxs)("div",{className:b,children:[(0,a.jsxs)("div",{children:["string"===typeof t?(0,a.jsx)("h3",{className:"text-lg font-semibold text-primary",children:t}):t,"string"===typeof s?(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:s}):s]}),c&&(0,a.jsx)("div",{className:"text-primary",children:c})]}),(0,a.jsx)("div",{className:f,children:r}),d&&(0,a.jsx)("div",{className:v,children:d})]})},n=(0,r.memo)(l)},3619:(e,t,s)=>{s.d(t,{Ay:()=>c});var r=s(5043),a=s(7515),l=s(4703),n=s(579);const i=e=>{let{error:t,resetError:s}=e;return(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[200px] p-4 border border-red-200 rounded-lg bg-red-50",children:[(0,n.jsx)("div",{className:"text-red-500 text-2xl mb-2",children:"\u26a0\ufe0f"}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-red-800 mb-2",children:"Something went wrong"}),(0,n.jsx)("p",{className:"text-red-600 text-sm mb-4 text-center max-w-md",children:t.message||"An unexpected error occurred"}),(0,n.jsx)("button",{onClick:s,className:"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors",children:"Try Again"})]})};function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{fallback:s=i,onError:o,enableReporting:c=!0,context:d}=t,m=(0,r.forwardRef)(((t,r)=>(0,n.jsx)(a.A,{fallback:(0,n.jsx)(s,{error:new Error,resetError:()=>window.location.reload()}),onError:(t,s)=>{c&&(0,l.N7)(t,d||e.displayName||e.name,{componentStack:s.componentStack,errorBoundary:!0}),o&&o(t,s)},children:(0,n.jsx)(e,{...t,ref:r})})));return m.displayName=`withErrorBoundary(${e.displayName||e.name})`,m}const c=o},3893:(e,t,s)=>{s.d(t,{Yq:()=>r,vv:()=>a});const r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return"-";try{const s=new Date(e),r={year:"numeric",month:"short",day:"numeric",...t};return new Intl.DateTimeFormat("en-US",r).format(s)}catch(s){return console.error("Error formatting date:",s),e}},a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"en-US";try{return new Intl.NumberFormat(s,{style:"currency",currency:t,minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}catch(r){return console.error("Error formatting currency:",r),`${t} ${e.toFixed(2)}`}}},3927:(e,t,s)=>{s.d(t,{A:()=>a});s(5043);var r=s(579);const a=e=>{let{size:t="md",className:s="",variant:a="spinner",color:l="#F28B22",useCurrentColor:n=!1}=e;const i={sm:{spinner:"w-5 h-5",dots:"w-1 h-1",pulse:"w-4 h-4",ripple:"w-6 h-6"},md:{spinner:"w-8 h-8",dots:"w-1.5 h-1.5",pulse:"w-6 h-6",ripple:"w-10 h-10"},lg:{spinner:"w-12 h-12",dots:"w-2 h-2",pulse:"w-8 h-8",ripple:"w-16 h-16"}},o=n?"currentColor":l;return"spinner"===a?(0,r.jsxs)("div",{className:`flex justify-center items-center ${s}`,role:"status","aria-label":"Loading",children:[(0,r.jsx)("div",{className:`spinner-smooth rounded-full border-2 border-gray-200 ${i[t].spinner}`,style:{borderTopColor:o,borderRightColor:o}}),(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"dots"===a?(0,r.jsxs)("div",{className:`flex justify-center items-center space-x-1 dots-bounce ${s}`,role:"status","aria-label":"Loading",children:[(0,r.jsx)("div",{className:`${i[t].dots} rounded-full dot`,style:{backgroundColor:o}}),(0,r.jsx)("div",{className:`${i[t].dots} rounded-full dot`,style:{backgroundColor:o}}),(0,r.jsx)("div",{className:`${i[t].dots} rounded-full dot`,style:{backgroundColor:o}}),(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"pulse"===a?(0,r.jsxs)("div",{className:`flex justify-center items-center ${s}`,role:"status","aria-label":"Loading",children:[(0,r.jsx)("div",{className:`${i[t].pulse} rounded-full pulse-smooth`,style:{backgroundColor:o}}),(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"ripple"===a?(0,r.jsxs)("div",{className:`flex justify-center items-center ${s}`,role:"status","aria-label":"Loading",children:[(0,r.jsx)("div",{className:`${i[t].ripple} rounded-full ripple-effect`,style:{color:o},children:(0,r.jsx)("div",{className:`${i[t].pulse} rounded-full pulse-smooth mx-auto`,style:{backgroundColor:o}})}),(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})]}):null}},4129:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(5043);function a(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))}const l=r.forwardRef(a)},4791:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(5043);function a(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"}))}const l=r.forwardRef(a)},6365:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(5043);function a(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))}const l=r.forwardRef(a)},7174:(e,t,s)=>{s.d(t,{HW:()=>n,O1:()=>l,OL:()=>a});var r=s(7304);r.t1.register(r.PP,r.kc,r.FN,r.No,r.E8,r.Bs,r.pr,r.hE,r.m_,r.s$,r.dN);const a={responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,color:"#000000",font:{size:16,weight:"bold"}},legend:{display:!1,labels:{color:"#000000"}},tooltip:{backgroundColor:"#F28B22",titleColor:"#FFFFFF",bodyColor:"#FFFFFF",padding:12,displayColors:!1}},scales:{x:{grid:{display:!1},ticks:{color:"#000000"}},y:{beginAtZero:!0,grid:{display:!0,color:"#E5E7EB"},ticks:{color:"#000000"}}}},l={responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,color:"#000000",font:{size:16,weight:"bold"}},legend:{position:"bottom",labels:{color:"#000000",font:{size:12}}},tooltip:{backgroundColor:"#F28B22",titleColor:"#FFFFFF",bodyColor:"#FFFFFF",padding:12}}},n=e=>{e&&e.destroy()}},7907:(e,t,s)=>{s.d(t,{A:()=>n});var r=s(5043),a=s(579);const l=e=>{let{children:t,variant:s="primary",size:r="md",className:l="",onClick:n,disabled:i=!1,type:o="button",icon:c,iconPosition:d="left",fullWidth:m=!1,loading:u=!1,rounded:x=!1,href:h,target:g,rel:p,title:y,ariaLabel:b,testId:f}=e;const v=`\n    inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2\n    ${{primary:"bg-primary text-white hover:bg-primary/90 focus-visible:ring-primary",secondary:"bg-gray-200 text-gray-800 hover:bg-gray-300 focus-visible:ring-gray-300",outline:"bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus-visible:ring-primary",danger:"bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500",success:"bg-green-600 text-white hover:bg-green-700 focus-visible:ring-green-500",text:"bg-transparent text-primary hover:bg-gray-100 focus-visible:ring-primary",link:"bg-transparent text-primary hover:underline focus-visible:ring-transparent p-0"}[s]}\n    ${{xs:"text-xs px-2 py-1",sm:"text-xs px-3 py-1.5",md:"text-sm px-4 py-2",lg:"text-base px-5 py-2.5",xl:"text-lg px-6 py-3"}[r]}\n    ${i?"opacity-60 cursor-not-allowed":"cursor-pointer"}\n    ${m?"w-full":""}\n    ${x?"rounded-full":"rounded-lg"}\n    ${l}\n  `,j=(0,a.jsxs)(a.Fragment,{children:[u&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-current",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","aria-hidden":"true",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),c&&"left"===d&&!u&&(0,a.jsx)("span",{className:"mr-2",children:c}),t,c&&"right"===d&&(0,a.jsx)("span",{className:"ml-2",children:c})]});return h?(0,a.jsx)("a",{href:h,className:v,target:g,rel:p||("_blank"===g?"noopener noreferrer":void 0),onClick:n,title:y,"aria-label":b,"data-testid":f,children:j}):(0,a.jsx)("button",{type:o,className:v,onClick:n,disabled:i||u,title:y,"aria-label":b,"data-testid":f,children:j})},n=(0,r.memo)(l)},8045:(e,t,s)=>{s.r(t),s.d(t,{default:()=>X});var r=s(5043),a=s(2806),l=s(7907),n=s(3927),i=s(3893),o=s(1512),c=s(4703),d=s(3619),m=s(4791);function u(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25M9 16.5v.75m3-3v3M15 12v5.25m-4.5-15H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))}const x=r.forwardRef(u);function h(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"}))}const g=r.forwardRef(h);var p=s(5590),y=s(3488),b=s(7012),f=s(2683),v=s(3593),j=s(579);const w=e=>{if(!r.isValidElement(e))return"bg-primary bg-opacity-10";const t=e.props.className||"";return console.log("Icon className:",t),t.includes("text-primary")?"bg-primary-500 bg-opacity-10":t.includes("text-blue")?"bg-blue-500 bg-opacity-10":t.includes("text-green")?"bg-green-500 bg-opacity-10":t.includes("text-yellow")?"bg-yellow-500 bg-opacity-10":t.includes("text-red")?"bg-red-500 bg-opacity-10":t.includes("text-purple")?"bg-purple-500 bg-opacity-10":t.includes("text-indigo")?"bg-indigo-500 bg-opacity-10":t.includes("text-pink")?"bg-pink-500 bg-opacity-10":t.includes("text-gray")?"bg-gray-500 bg-opacity-10":"bg-primary bg-opacity-10"},N=e=>{let{title:t,value:s,icon:a,change:l,isLoading:n=!1,onClick:i,hoverable:o=!0}=e;return(0,j.jsx)(v.A,{className:"transition-transform duration-300 hover:scale-105",...i&&{onClick:i},hoverable:o,children:(0,j.jsxs)("div",{className:"flex items-center justify-between",children:[(0,j.jsxs)("div",{children:[(0,j.jsx)("p",{className:"text-sm font-medium text-gray-500",children:t}),n?(0,j.jsx)("div",{className:"h-8 w-24 bg-gray-200 animate-pulse rounded mt-1"}):(0,j.jsx)("p",{className:"text-2xl font-semibold text-gray-900 mt-1",children:s}),l&&!n&&(0,j.jsxs)("div",{className:"flex items-center mt-1",children:[(0,j.jsxs)("span",{className:"text-sm font-medium "+(l.isPositive?"text-green-600":"text-red-600"),children:[l.isPositive?"\u2191":"\u2193"," ",l.value,"%"]}),(0,j.jsx)("span",{className:"text-sm text-gray-500 ml-1",children:"vs last period"})]})]}),(0,j.jsx)("div",{className:`p-3 rounded-full ${w(a)}`,children:r.isValidElement(a)?(()=>{const e=a,t=(e.props.className||"").match(/text-[a-z0-9-]+/),s=t?t[0]:"text-primary";return r.cloneElement(e,{className:`w-6 h-6 ${s}`})})():a})]})})};var k=s(6058),C=s(7174);const E=e=>{let{data:t,isLoading:s=!1,onPeriodChange:a}=e;const[i,o]=(0,r.useState)("week"),c=(0,r.useRef)(null);(0,r.useEffect)((()=>()=>{(0,C.HW)(c.current)}),[]);const d=(0,r.useCallback)((e=>{o(e),a&&a(e)}),[a]),m=(0,r.useMemo)((()=>({labels:t.map((e=>e.date)),datasets:[{label:"Sales",data:t.map((e=>e.amount)),borderColor:"#F28B22",backgroundColor:"rgba(242, 139, 34, 0.1)",tension:.4,fill:!0,pointBackgroundColor:"#F28B22",pointBorderColor:"#fff",pointBorderWidth:2,pointRadius:4,pointHoverRadius:6}]})),[t]),u=(0,r.useMemo)((()=>{var e,t;return{...C.OL,plugins:{...C.OL.plugins,tooltip:{...(null===(e=C.OL.plugins)||void 0===e?void 0:e.tooltip)||{},callbacks:{label:function(e){return`$${e.parsed.y.toLocaleString()}`}}}},scales:{...C.OL.scales,y:{...(null===(t=C.OL.scales)||void 0===t?void 0:t.y)||{},ticks:{callback:function(e){return"$"+e.toLocaleString()}}}}}}),[]);return(0,j.jsxs)(v.A,{className:"h-full",children:[(0,j.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6",children:[(0,j.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Sales Overview"}),(0,j.jsxs)("div",{className:"flex space-x-2 mt-3 sm:mt-0",children:[(0,j.jsx)(l.A,{size:"sm",variant:"day"===i?"primary":"outline",onClick:()=>d("day"),children:"Day"}),(0,j.jsx)(l.A,{size:"sm",variant:"week"===i?"primary":"outline",onClick:()=>d("week"),children:"Week"}),(0,j.jsx)(l.A,{size:"sm",variant:"month"===i?"primary":"outline",onClick:()=>d("month"),children:"Month"}),(0,j.jsx)(l.A,{size:"sm",variant:"year"===i?"primary":"outline",onClick:()=>d("year"),children:"Year"})]})]}),s?(0,j.jsx)("div",{className:"h-80 flex items-center justify-center",children:(0,j.jsx)(n.A,{size:"lg"})}):(0,j.jsx)("div",{className:"h-80",children:(0,j.jsx)(k.N1,{data:m,options:u,ref:e=>{e&&(c.current=e)}})})]})},A=(0,r.memo)(E),F=e=>{let{data:t,isLoading:s=!1,onPeriodChange:a,showCard:i=!0,title:o="User Growth"}=e;const[c,d]=(0,r.useState)("week"),m=(0,r.useRef)(null);(0,r.useEffect)((()=>()=>{(0,C.HW)(m.current)}),[]);const u=(0,r.useCallback)((e=>{d(e),a&&a(e)}),[a]),x=(0,r.useMemo)((()=>({labels:t.map((e=>e.date)),datasets:[{label:"Users",data:t.map((e=>e.users)),borderColor:"#F28B22",backgroundColor:"rgba(242, 139, 34, 0.1)",tension:.4,fill:!0,pointBackgroundColor:"#F28B22",pointBorderColor:"#fff",pointBorderWidth:2,pointRadius:4,pointHoverRadius:6}]})),[t]),h=(0,r.useMemo)((()=>{var e;return{...C.OL,plugins:{...C.OL.plugins,tooltip:{...(null===(e=C.OL.plugins)||void 0===e?void 0:e.tooltip)||{},callbacks:{label:function(e){return`${e.parsed.y.toLocaleString()} users`}}}}}}),[]),g=(0,j.jsx)(j.Fragment,{children:s?(0,j.jsx)("div",{className:"h-80 flex items-center justify-center",children:(0,j.jsx)(n.A,{size:"lg"})}):(0,j.jsx)("div",{className:"h-80",children:(0,j.jsx)(k.N1,{data:x,options:h,ref:e=>{e&&(m.current=e)}})})});return i?(0,j.jsxs)(v.A,{className:"h-full",children:[(0,j.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6",children:[(0,j.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:o}),(0,j.jsxs)("div",{className:"flex space-x-2 mt-3 sm:mt-0",children:[(0,j.jsx)(l.A,{size:"sm",variant:"day"===c?"primary":"outline",onClick:()=>u("day"),children:"Day"}),(0,j.jsx)(l.A,{size:"sm",variant:"week"===c?"primary":"outline",onClick:()=>u("week"),children:"Week"}),(0,j.jsx)(l.A,{size:"sm",variant:"month"===c?"primary":"outline",onClick:()=>u("month"),children:"Month"}),(0,j.jsx)(l.A,{size:"sm",variant:"year"===c?"primary":"outline",onClick:()=>u("year"),children:"Year"})]})]}),g]}):g},S=(0,r.memo)(F),$=e=>{var t;let{data:s,isLoading:a=!1,title:l="Category Distribution",showCard:n=!0,className:i=""}=e;const o=(0,r.useRef)(null);(0,r.useEffect)((()=>()=>{try{(0,C.HW)(o.current)}catch(e){console.warn("Chart destruction warning:",e)}}),[]);const c=(0,r.useMemo)((()=>({labels:s.labels||[],datasets:s.datasets||[]})),[s.labels,s.datasets]),d=(0,r.useMemo)((()=>{var e;return{...C.O1,plugins:{...C.O1.plugins,title:{...null===(e=C.O1.plugins)||void 0===e?void 0:e.title,display:!1}}}}),[]),m=s.labels&&s.labels.length>0&&s.datasets&&s.datasets.length>0&&(null===(t=s.datasets[0])||void 0===t?void 0:t.data)&&s.datasets[0].data.length>0,u=(0,j.jsx)(j.Fragment,{children:a?(0,j.jsx)("div",{className:"h-80 flex items-center justify-center",children:(0,j.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary"})}):m?(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("div",{className:"h-80",children:(0,j.jsx)(k.Fq,{ref:e=>{e&&(o.current=e)},data:c,options:d})}),(0,j.jsx)("div",{className:"mt-4 space-y-2",children:s.labels.map(((e,t)=>{var r,a,l,n;return(0,j.jsxs)("div",{className:"flex items-center justify-between",children:[(0,j.jsxs)("div",{className:"flex items-center",children:[(0,j.jsx)("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:(null===(r=s.datasets[0])||void 0===r||null===(a=r.backgroundColor)||void 0===a?void 0:a[t])||"#ccc"}}),(0,j.jsx)("span",{className:"text-sm text-gray-600",children:e})]}),(0,j.jsxs)("span",{className:"text-sm font-medium",children:[(null===(l=s.datasets[0])||void 0===l||null===(n=l.data)||void 0===n?void 0:n[t])||0,"%"]})]},t)}))})]}):(0,j.jsx)("div",{className:"h-80 flex items-center justify-center",children:(0,j.jsx)("div",{className:"text-center text-gray-500",children:"No data available"})})});return n?(0,j.jsx)(v.A,{title:(0,j.jsx)("h3",{className:"text-lg font-semibold text-black",children:l}),className:i,children:u}):(0,j.jsx)("div",{className:i,children:u})},L=r.memo($);var O=s(3216),D=s(5475),R=s(9531),B=s(724),U=s(8100),M=s(4538),z=s(5889),P=s(7098),W=s(4129);function T(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))}const G=r.forwardRef(T),V=e=>{let{order:t,isOpen:s,onClose:r}=e;if(!t)return null;return(0,j.jsx)(U.A,{isOpen:s,onClose:r,title:"Order Details",size:"md",footer:(0,j.jsx)(l.A,{variant:"outline",onClick:r,children:"Close"}),children:(0,j.jsxs)("div",{className:"space-y-6",children:[(0,j.jsxs)("div",{className:"flex items-center justify-between",children:[(0,j.jsxs)("div",{children:[(0,j.jsxs)("h3",{className:"text-lg font-medium text-gray-900",children:["Order #",t.id]}),(0,j.jsx)("div",{className:"mt-1",children:(0,j.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${(e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"processing":return"bg-blue-100 text-blue-800";case"completed":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return""}})(t.status)}`,children:[(e=>{switch(e){case"pending":return(0,j.jsx)(b.A,{className:"w-5 h-5 text-yellow-500"});case"processing":return(0,j.jsx)(M.A,{className:"w-5 h-5 text-blue-500"});case"completed":return(0,j.jsx)(z.A,{className:"w-5 h-5 text-green-500"});case"cancelled":return(0,j.jsx)(P.A,{className:"w-5 h-5 text-red-500"});default:return null}})(t.status),(0,j.jsx)("span",{className:"ml-1",children:t.status?t.status.charAt(0).toUpperCase()+t.status.slice(1):"Unknown"})]})})]}),(0,j.jsxs)("div",{className:"text-right",children:[(0,j.jsx)("div",{className:"text-sm text-gray-500",children:"Total Amount"}),(0,j.jsxs)("div",{className:"text-xl font-bold text-primary",children:["$",t.amount.toLocaleString()]})]})]}),(0,j.jsxs)("div",{className:"space-y-4",children:[(0,j.jsxs)("div",{className:"flex items-start",children:[(0,j.jsx)(W.A,{className:"w-5 h-5 text-gray-400 mt-0.5 mr-2"}),(0,j.jsxs)("div",{children:[(0,j.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"Customer"}),(0,j.jsx)("div",{className:"text-sm text-gray-900",children:t.customer})]})]}),(0,j.jsxs)("div",{className:"flex items-start",children:[(0,j.jsx)(G,{className:"w-5 h-5 text-gray-400 mt-0.5 mr-2"}),(0,j.jsxs)("div",{children:[(0,j.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"Order Date"}),(0,j.jsx)("div",{className:"text-sm text-gray-900",children:t.date})]})]}),(0,j.jsxs)("div",{className:"flex items-start",children:[(0,j.jsx)(f.A,{className:"w-5 h-5 text-gray-400 mt-0.5 mr-2"}),(0,j.jsxs)("div",{children:[(0,j.jsx)("div",{className:"text-sm font-medium text-gray-500",children:"Payment"}),(0,j.jsxs)("div",{className:"text-sm text-gray-900",children:["$",t.amount.toLocaleString()]})]})]})]}),(0,j.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,j.jsx)("p",{className:"text-sm text-gray-500",children:"This is a simplified view of the order. To see full details including items, shipping information, and more, please visit the Orders page."})})]})})},I=e=>{let{orders:t,isLoading:s=!1,onViewOrder:a}=e;const l=(0,O.Zp)(),[n,i]=(0,r.useState)(null),[o,c]=(0,r.useState)(!1),d=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"processing":return"bg-blue-100 text-blue-800";case"completed":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return(0,j.jsxs)(v.A,{className:"h-full",children:[(0,j.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,j.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Recent Orders"}),(0,j.jsx)(D.N_,{to:B.b.ORDERS,className:"text-sm text-primary hover:text-primary-dark font-medium",children:"View All"})]}),s?(0,j.jsx)("div",{className:"space-y-4",children:[...Array(5)].map(((e,t)=>(0,j.jsxs)("div",{className:"animate-pulse flex items-center justify-between",children:[(0,j.jsxs)("div",{className:"space-y-2",children:[(0,j.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24"}),(0,j.jsx)("div",{className:"h-3 bg-gray-200 rounded w-16"})]}),(0,j.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16"}),(0,j.jsx)("div",{className:"h-6 bg-gray-200 rounded w-20"}),(0,j.jsx)("div",{className:"h-8 bg-gray-200 rounded-full w-8"})]},t)))}):(0,j.jsx)("div",{className:"overflow-hidden",children:(0,j.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,j.jsx)("thead",{className:"bg-gray-50",children:(0,j.jsxs)("tr",{children:[(0,j.jsx)("th",{scope:"col",className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Order"}),(0,j.jsx)("th",{scope:"col",className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customer"}),(0,j.jsx)("th",{scope:"col",className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,j.jsx)("th",{scope:"col",className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,j.jsx)("th",{scope:"col",className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,j.jsx)("th",{scope:"col",className:"relative px-4 py-3",children:(0,j.jsx)("span",{className:"sr-only",children:"Actions"})})]})}),(0,j.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map((e=>(0,j.jsxs)("tr",{className:"hover:bg-gray-50 cursor-pointer",onClick:()=>{return t=e.id,void l(B.b.getOrderDetailsRoute(t));var t},children:[(0,j.jsxs)("td",{className:"px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:["#",e.id]}),(0,j.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-500",children:e.customer}),(0,j.jsxs)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-900",children:["$",e.amount.toLocaleString()]}),(0,j.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:(0,j.jsx)("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${d(e.status)}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,j.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-500",children:e.date}),(0,j.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,j.jsx)("button",{onClick:t=>((e,t)=>{t.stopPropagation(),i(e),c(!0),a&&a(e.id)})(e,t),className:"text-gray-500 hover:text-primary",children:(0,j.jsx)(R.A,{className:"h-5 w-5"})})})]},e.id)))})]})}),(0,j.jsx)(V,{order:n,isOpen:o,onClose:()=>c(!1)})]})};var H=s(1568);const _={getDashboardStats:async()=>{try{const t=await H.A.get("/dashboard/stats");if(t.data)return{summary:{totalUsers:(e=t.data).totalUsers,totalSuppliers:e.totalSuppliers,totalOrders:e.totalOrders,totalRevenue:e.totalRevenue,pendingVerifications:e.pendingVerifications,activeUsers:e.activeUsers},monthlyGrowth:e.monthlyGrowth,revenueData:{labels:[],datasets:[{label:"Revenue",data:[],borderColor:"#F28B22",backgroundColor:"rgba(242, 139, 34, 0.1)"}]},userGrowth:{labels:[],datasets:[{label:"Users",data:[],borderColor:"#F28B22",backgroundColor:"rgba(242, 139, 34, 0.1)"}]},categoryDistribution:{labels:[],datasets:[{data:[],backgroundColor:[],borderWidth:1}]},recentOrders:[],topSuppliers:[]};throw new Error("No dashboard statistics data received")}catch(t){throw(0,c.hS)(t)}var e},getSalesData:async e=>{try{const t=await H.A.get("/dashboard/sales",{params:{period:e}});if(t.data)return t.data.data.map((e=>({date:e.date,amount:e.sales,orders:e.orders})));throw new Error("No sales data received")}catch(t){throw(0,c.hS)(t)}},getUserGrowth:async e=>{try{const t=await H.A.get("/dashboard/user-growth",{params:{period:e}});if(t.data)return t.data.data.map((e=>({date:e.date,users:e.totalUsers,newUsers:e.newUsers,totalUsers:e.totalUsers})));throw new Error("No user growth data received")}catch(t){throw(0,c.hS)(t)}},getCategoryDistribution:async()=>{try{const e=await H.A.get("/dashboard/category-distribution");if(e.data)return(e=>{const t=["#F28B22","#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6","#06B6D4","#84CC16","#F97316","#EC4899"];return{labels:e.map((e=>e.category)),datasets:[{data:e.map((e=>e.count)),backgroundColor:e.map(((e,s)=>t[s%t.length]||"#F28B22")),borderWidth:1}]}})(e.data);throw new Error("No category distribution data received")}catch(e){throw(0,c.hS)(e)}}},Z=_,q=e=>{let{className:t}=e;const[s,a]=(0,r.useState)([]),[l,n]=(0,r.useState)(!1),[i,o]=(0,r.useState)(null),c=(0,r.useCallback)((async e=>{n(!0),o(null);try{const t=await _.getSalesData(e);a(t)}catch(t){o(t),console.error("Failed to fetch sales data:",t)}finally{n(!1)}}),[]);(0,r.useEffect)((()=>{c("month")}),[c]);const d=(0,r.useCallback)((e=>{c(e)}),[c]);return i?(0,j.jsxs)("div",{className:`bg-white rounded-lg shadow p-6 ${t||""}`,children:[(0,j.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Sales Data"}),(0,j.jsxs)("div",{className:"text-center text-red-500",children:[(0,j.jsx)("p",{children:"Failed to load sales data"}),(0,j.jsx)("button",{onClick:()=>c("month"),className:"mt-2 text-sm text-blue-600 hover:text-blue-800",children:"Try again"})]})]}):(0,j.jsx)("div",{className:t,children:(0,j.jsx)(A,{data:s,isLoading:l,onPeriodChange:d})})},Y=e=>{let{className:t}=e;const[s,a]=(0,r.useState)([]),[l,n]=(0,r.useState)(!1),[i,o]=(0,r.useState)(null),c=(0,r.useCallback)((async e=>{n(!0),o(null);try{const t=await _.getUserGrowth(e);a(t)}catch(t){o(t),console.error("Failed to fetch user growth data:",t)}finally{n(!1)}}),[]);(0,r.useEffect)((()=>{c("month")}),[c]);const d=(0,r.useCallback)((e=>{c("day"===e?"week":e)}),[c]);return i?(0,j.jsxs)("div",{className:`bg-white rounded-lg shadow p-6 ${t||""}`,children:[(0,j.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"User Growth"}),(0,j.jsxs)("div",{className:"text-center text-red-500",children:[(0,j.jsx)("p",{children:"Failed to load user growth data"}),(0,j.jsx)("button",{onClick:()=>c("month"),className:"mt-2 text-sm text-blue-600 hover:text-blue-800",children:"Try again"})]})]}):(0,j.jsx)("div",{className:t,children:(0,j.jsx)(S,{data:s,isLoading:l,onPeriodChange:d})})},K=e=>{let{className:t,title:s="Category Distribution"}=e;const[a,l]=(0,r.useState)({labels:[],datasets:[{data:[],backgroundColor:[],borderWidth:1}]}),[n,i]=(0,r.useState)(!1),[o,c]=(0,r.useState)(null),d=(0,r.useCallback)((async()=>{i(!0),c(null);try{const e=await _.getCategoryDistribution();l(e)}catch(e){c(e),console.error("Failed to fetch category distribution data:",e)}finally{i(!1)}}),[]);return(0,r.useEffect)((()=>{d()}),[d]),o?(0,j.jsxs)("div",{className:`bg-white rounded-lg shadow p-6 ${t||""}`,children:[(0,j.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:s}),(0,j.jsxs)("div",{className:"text-center text-red-500",children:[(0,j.jsx)("p",{children:"Failed to load category distribution data"}),(0,j.jsx)("button",{onClick:d,className:"mt-2 text-sm text-blue-600 hover:text-blue-800",children:"Try again"})]})]}):(0,j.jsx)("div",{className:t,children:(0,j.jsx)(L,{data:a,isLoading:n,title:s})})};var J=s(9705);const Q=()=>{const[e,t]=(0,r.useState)(null),[s,a]=(0,r.useState)(!1),[l,n]=(0,r.useState)(null),{showNotification:i}=(0,J.A)(),o=(0,r.useRef)(i),c=(0,r.useRef)(!1),d=(0,r.useRef)(null),m=3e5;(0,r.useEffect)((()=>{o.current=i}));const u=(0,r.useCallback)((()=>!!d.current&&Date.now()-d.current.timestamp<m),[m]),x=(0,r.useCallback)((async function(){if(!(arguments.length>0&&void 0!==arguments[0]&&arguments[0])&&u()&&d.current)return t(d.current.data),d.current.data;a(!0),n(null);try{const e=await Z.getDashboardStats();return t(e),d.current={data:e,timestamp:Date.now()},e}catch(e){const t=e;n(t);const s=t.message||"Failed to fetch dashboard statistics";throw console.error("Dashboard fetch error:",t),o.current({type:"error",title:"Dashboard Error",message:s}),t}finally{a(!1)}}),[u]),h=(0,r.useCallback)((async e=>{a(!0),n(null);try{return await Z.getSalesData(e)}catch(t){throw n(t),o.current({type:"error",title:"Error",message:`Failed to fetch sales data for ${e}`}),t}finally{a(!1)}}),[]),g=(0,r.useCallback)((async e=>{a(!0),n(null);try{return await Z.getUserGrowth(e)}catch(t){throw n(t),o.current({type:"error",title:"Error",message:`Failed to fetch user growth data for ${e}`}),t}finally{a(!1)}}),[]),p=(0,r.useCallback)((async()=>{a(!0),n(null);try{return await Z.getCategoryDistribution()}catch(e){throw n(e),o.current({type:"error",title:"Error",message:"Failed to fetch category distribution data"}),e}finally{a(!1)}}),[]);return(0,r.useEffect)((()=>{c.current||(c.current=!0,x().catch((e=>{console.error("Failed to fetch initial dashboard stats:",e)})))}),[x]),(0,r.useEffect)((()=>()=>{a(!1),n(null)}),[]),{stats:e,isLoading:s,error:l,fetchStats:x,fetchSalesData:h,fetchUserGrowth:g,fetchCategoryDistribution:p}},X=(0,d.Ay)((()=>{const[e,t]=(0,r.useState)(!1),{stats:s,isLoading:d,fetchStats:u}=Q(),{handleGeneralError:h}=(0,o.A)({enableNotifications:!0,enableReporting:!0});return d||!s?(0,j.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,j.jsx)(n.A,{size:"lg",variant:"pulse"})}):(0,j.jsxs)("div",{children:[(0,j.jsx)(a.A,{title:"Dashboard",description:"Welcome to ConnectChain Admin Dashboard",actions:(0,j.jsxs)("div",{className:"flex flex-wrap gap-3",children:[(0,j.jsx)(l.A,{variant:"outline",icon:(0,j.jsx)(m.A,{className:"h-5 w-5"}),children:"Export Report"}),(0,j.jsx)(l.A,{icon:(0,j.jsx)(x,{className:"h-5 w-5"}),children:"Generate Report"}),(0,j.jsx)(l.A,{variant:"outline",icon:(0,j.jsx)(g,{className:"h-5 w-5"}),onClick:async()=>{t(!0);const e=await(0,c.fN)((async()=>(await new Promise(((e,t)=>{setTimeout((()=>{Math.random()<.1?t(new Error("Failed to refresh dashboard data")):e(!0)}),1500)})),await u(!0),!0)),{timeout:1e4,retries:2,operationName:"Refresh Dashboard"});e.success||h(e.error,"Dashboard Refresh"),t(!1)},loading:e,children:"Refresh"})]})}),(0,j.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6",children:[(0,j.jsx)(N,{title:"Total Users",value:s.summary.totalUsers.toLocaleString(),icon:(0,j.jsx)(p.A,{className:"w-6 h-6 text-primary"}),change:{value:Math.abs(s.monthlyGrowth.users),isPositive:s.monthlyGrowth.users>=0}}),(0,j.jsx)(N,{title:"Total Orders",value:s.summary.totalOrders.toLocaleString(),icon:(0,j.jsx)(y.A,{className:"w-6 h-6 text-blue-500"}),change:{value:Math.abs(s.monthlyGrowth.orders),isPositive:s.monthlyGrowth.orders>=0}}),(0,j.jsx)(N,{title:"Active Users",value:s.summary.activeUsers.toLocaleString(),icon:(0,j.jsx)(b.A,{className:"w-6 h-6 text-green-500"}),change:{value:0,isPositive:!0}}),(0,j.jsx)(N,{title:"Revenue",value:(0,i.vv)(s.summary.totalRevenue),icon:(0,j.jsx)(f.A,{className:"w-6 h-6 text-green-500"}),change:{value:Math.abs(s.monthlyGrowth.revenue),isPositive:s.monthlyGrowth.revenue>=0}})]}),(0,j.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6",children:[(0,j.jsx)(q,{}),(0,j.jsx)(Y,{})]}),(0,j.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,j.jsx)("div",{className:"lg:col-span-2",children:(0,j.jsx)(I,{orders:s.recentOrders.map((e=>({id:e.orderNumber,customer:e.customerName,amount:e.amount,status:e.status,date:e.date}))),onViewOrder:e=>console.log("Order clicked:",e)})}),(0,j.jsx)(K,{title:"Category Distribution"})]})]})}),{fallback:e=>{let{error:t,resetError:s}=e;return(0,j.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[400px] p-8",children:[(0,j.jsx)("div",{className:"text-red-500 text-4xl mb-4",children:"\ud83d\udcca"}),(0,j.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Dashboard Error"}),(0,j.jsx)("p",{className:"text-gray-600 mb-4 text-center max-w-md",children:t.message||"An error occurred while loading the dashboard"}),(0,j.jsx)("button",{onClick:s,className:"px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition-colors",children:"Reload Dashboard"})]})},context:"DashboardPage"})},8100:(e,t,s)=>{s.d(t,{A:()=>o});var r=s(5043),a=s(7591),l=s(7950),n=s(579);const i=e=>{let{isOpen:t,onClose:s,title:i,children:o,size:c="md",footer:d,closeOnEsc:m=!0,closeOnBackdropClick:u=!0,showCloseButton:x=!0,centered:h=!0,className:g="",bodyClassName:p="",headerClassName:y="",footerClassName:b="",backdropClassName:f="",testId:v}=e;const j=(0,r.useRef)(null);if((0,r.useEffect)((()=>{const e=e=>{m&&"Escape"===e.key&&s()};return t&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="auto"}}),[t,s,m]),(0,r.useEffect)((()=>{if(!t||!j.current)return;const e=j.current.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');if(0===e.length)return;const s=e[0],r=e[e.length-1],a=e=>{"Tab"===e.key&&(e.shiftKey?document.activeElement===s&&(r.focus(),e.preventDefault()):document.activeElement===r&&(s.focus(),e.preventDefault()))};return document.addEventListener("keydown",a),s.focus(),()=>{document.removeEventListener("keydown",a)}}),[t]),!t)return null;const w=(0,n.jsxs)(r.Fragment,{children:[(0,n.jsx)("div",{className:`fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity ${f}`,onClick:u?s:void 0,"data-testid":`${v}-backdrop`}),(0,n.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,n.jsx)("div",{className:`flex min-h-full items-${h?"center":"start"} justify-center p-4 text-center`,children:(0,n.jsxs)("div",{ref:j,className:`${{xs:"max-w-xs",sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[c]} w-full transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all ${g}`,onClick:e=>e.stopPropagation(),"data-testid":v,children:[(0,n.jsxs)("div",{className:`flex items-center justify-between px-6 py-4 border-b border-gray-100 ${y}`,children:["string"===typeof i?(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:i}):i,x&&(0,n.jsx)("button",{type:"button",className:"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary rounded-full p-1",onClick:s,"aria-label":"Close modal","data-testid":`${v}-close-button`,children:(0,n.jsx)(a.A,{className:"h-6 w-6"})})]}),(0,n.jsx)("div",{className:`px-6 py-4 ${p}`,children:o}),d&&(0,n.jsx)("div",{className:`px-6 py-4 bg-gray-50 border-t border-gray-100 flex justify-end space-x-3 ${b}`,children:d})]})})})]});return(0,l.createPortal)(w,document.body)},o=(0,r.memo)(i)}}]);
//# sourceMappingURL=45.f9d84cf5.chunk.js.map