{"version": 3, "file": "static/js/908.2a219aac.chunk.js", "mappings": "yJA2BA,MAAMA,EAA4BC,IAgB3B,IAhB4B,MACjCC,EAAK,SACLC,EAAQ,SACRC,EAAQ,UACRC,EAAY,GAAE,cACdC,EAAgB,GAAE,gBAClBC,EAAkB,GAAE,gBACpBC,EAAkB,GAAE,KACpBC,EAAI,OACJC,EAAM,QACNC,EAAO,UACPC,GAAY,EAAK,UACjBC,GAAY,EAAK,SACjBC,GAAW,EAAI,QACfC,GAAU,EAAK,OACfC,GACDf,EAEC,MAAMgB,EAAc,6BACIH,EAAW,yBAA2B,uDAC1DF,EAAY,uEAAyE,oBACrFD,EAAU,iBAAmB,WAC7BN,QAIEa,EAAgB,mFAElBX,QAIEY,EAAc,SAChBN,EAAY,GAAK,cACjBP,QAIEc,EAAgB,4DAElBZ,QAIJ,OAAIO,GAEAM,EAAAA,EAAAA,MAAA,OAAKhB,UAAWY,EAAa,cAAaD,EAAOZ,SAAA,EAC7CF,GAASC,GAAYM,KACrBY,EAAAA,EAAAA,MAAA,OAAKhB,UAAWa,EAAcd,SAAA,EAC5BiB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,SAAQD,SAAA,CACpBF,IAASoB,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,gDACxBF,IAAYmB,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,wDAE7BI,IAAQa,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,uDAI5BiB,EAAAA,EAAAA,KAAA,OAAKjB,UAAWc,EAAYf,UAC1BkB,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,6CAGhBK,IACCY,EAAAA,EAAAA,KAAA,OAAKjB,UAAWe,EAAchB,UAC5BkB,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,sDAQvBgB,EAAAA,EAAAA,MAAA,OACEhB,UAAWY,EACXN,QAASA,EACT,cAAaK,EAAOZ,SAAA,EAElBF,GAASC,GAAYM,KACrBY,EAAAA,EAAAA,MAAA,OAAKhB,UAAWa,EAAcd,SAAA,EAC5BiB,EAAAA,EAAAA,MAAA,OAAAjB,SAAA,CACoB,kBAAVF,GACNoB,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,qCAAoCD,SAAEF,IAEpDA,EAEmB,kBAAbC,GACNmB,EAAAA,EAAAA,KAAA,KAAGjB,UAAU,6BAA4BD,SAAED,IAE3CA,KAGHM,IAAQa,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,eAAcD,SAAEK,QAI5Ca,EAAAA,EAAAA,KAAA,OAAKjB,UAAWc,EAAYf,SAAEA,IAE7BM,IACCY,EAAAA,EAAAA,KAAA,OAAKjB,UAAWe,EAAchB,SAC3BM,MAGD,EAIV,GAAea,EAAAA,EAAAA,MAAKvB,E,uDCxHpB,MAsGA,EAtGsDC,IAM/C,IANgD,KACrDuB,EAAO,KAAI,UACXnB,EAAY,GAAE,QACdoB,EAAU,UAAS,MACnBC,EAAQ,UAAS,gBACjBC,GAAkB,GACnB1B,EACC,MAAM2B,EAAU,CACdC,GAAI,CAAEC,QAAS,UAAWC,KAAM,UAAWC,MAAO,UAAWC,OAAQ,WACrEC,GAAI,CAAEJ,QAAS,UAAWC,KAAM,cAAeC,MAAO,UAAWC,OAAQ,aACzEE,GAAI,CAAEL,QAAS,YAAaC,KAAM,UAAWC,MAAO,UAAWC,OAAQ,cAGnEG,EAAeT,EAAkB,eAAiBD,EAGxD,MAAgB,YAAZD,GAEAJ,EAAAA,EAAAA,MAAA,OACEhB,UAAW,oCAAoCA,IAC/CgC,KAAK,SACL,aAAW,UAASjC,SAAA,EAEpBkB,EAAAA,EAAAA,KAAA,OACEjB,UAAW,wDAAwDuB,EAAQJ,GAAMM,UACjFQ,MAAO,CACLC,eAAgBH,EAChBI,iBAAkBJ,MAGtBd,EAAAA,EAAAA,KAAA,QAAMjB,UAAU,UAASD,SAAC,kBAMhB,SAAZqB,GAEAJ,EAAAA,EAAAA,MAAA,OACEhB,UAAW,0DAA0DA,IACrEgC,KAAK,SACL,aAAW,UAASjC,SAAA,EAEpBkB,EAAAA,EAAAA,KAAA,OACEjB,UAAW,GAAGuB,EAAQJ,GAAMO,wBAC5BO,MAAO,CAAEG,gBAAiBL,MAE5Bd,EAAAA,EAAAA,KAAA,OACEjB,UAAW,GAAGuB,EAAQJ,GAAMO,wBAC5BO,MAAO,CAAEG,gBAAiBL,MAE5Bd,EAAAA,EAAAA,KAAA,OACEjB,UAAW,GAAGuB,EAAQJ,GAAMO,wBAC5BO,MAAO,CAAEG,gBAAiBL,MAE5Bd,EAAAA,EAAAA,KAAA,QAAMjB,UAAU,UAASD,SAAC,kBAMhB,UAAZqB,GAEAJ,EAAAA,EAAAA,MAAA,OACEhB,UAAW,oCAAoCA,IAC/CgC,KAAK,SACL,aAAW,UAASjC,SAAA,EAEpBkB,EAAAA,EAAAA,KAAA,OACEjB,UAAW,GAAGuB,EAAQJ,GAAMQ,kCAC5BM,MAAO,CAAEG,gBAAiBL,MAE5Bd,EAAAA,EAAAA,KAAA,QAAMjB,UAAU,UAASD,SAAC,kBAMhB,WAAZqB,GAEAJ,EAAAA,EAAAA,MAAA,OACEhB,UAAW,oCAAoCA,IAC/CgC,KAAK,SACL,aAAW,UAASjC,SAAA,EAEpBkB,EAAAA,EAAAA,KAAA,OACEjB,UAAW,GAAGuB,EAAQJ,GAAMS,oCAC5BK,MAAO,CAAEZ,MAAOU,GAAehC,UAE/BkB,EAAAA,EAAAA,KAAA,OACEjB,UAAW,GAAGuB,EAAQJ,GAAMQ,0CAC5BM,MAAO,CAAEG,gBAAiBL,QAG9Bd,EAAAA,EAAAA,KAAA,QAAMjB,UAAU,UAASD,SAAC,kBAKzB,IAAI,C,gDC9Gb,SAASsC,EAAiBzC,EAIvB0C,GAAQ,IAJgB,MACzBzC,EAAK,QACL0C,KACGC,GACJ5C,EACC,OAAoB6C,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ3C,EAAqB4C,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACH1C,GAAS,KAAmB4C,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,iHAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBJ,E,gDCvBlD,SAASiB,EAAS1D,EAIf0C,GAAQ,IAJQ,MACjBzC,EAAK,QACL0C,KACGC,GACJ5C,EACC,OAAoB6C,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ3C,EAAqB4C,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACH1C,GAAS,KAAmB4C,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,saAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBa,E,gDCvBlD,SAASC,EAAS3D,EAIf0C,GAAQ,IAJQ,MACjBzC,EAAK,QACL0C,KACGC,GACJ5C,EACC,OAAoB6C,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ3C,EAAqB4C,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACH1C,GAAS,KAAmB4C,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,qDAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBc,E,gDCvBlD,SAASC,EAAW5D,EAIjB0C,GAAQ,IAJU,MACnBzC,EAAK,QACL0C,KACGC,GACJ5C,EACC,OAAoB6C,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ3C,EAAqB4C,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACH1C,GAAS,KAAmB4C,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,0EAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBe,E,gDCvBlD,SAASC,EAAmB7D,EAIzB0C,GAAQ,IAJkB,MAC3BzC,EAAK,QACL0C,KACGC,GACJ5C,EACC,OAAoB6C,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ3C,EAAqB4C,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACH1C,GAAS,KAAmB4C,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,kFAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBgB,GCvBlD,SAASC,EAAa9D,EAInB0C,GAAQ,IAJY,MACrBzC,EAAK,QACL0C,KACGC,GACJ5C,EACC,OAAoB6C,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQ3C,EAAqB4C,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACH1C,GAAS,KAAmB4C,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,+BAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBiB,G,2CCoBlD,SAASC,EAAS/D,GAqBK,IArB2B,QAChDgE,EAAO,KACPC,EAAI,WACJC,EAAU,MACVjE,EAAK,YACLkE,EAAW,QACXrD,GAAU,EAAK,WACfsD,GAAa,EAAI,SACjBC,EAAWC,EAAAA,GAAOC,kBAAiB,WACnCC,GAAa,EAAI,kBACjBC,EAAiB,QACjBC,EAAO,aACPC,EAAe,mBAAkB,UACjCvE,EAAY,GAAE,gBACdE,EAAkB,GAAE,cACpBD,EAAgB,GAAE,gBAClBE,EAAkB,GAAE,aACpBqE,EAAY,eACZC,EAAc,qBACdC,EAAuB,MAAK,OAC5B/D,GACkBf,EAElB,MAAO+E,EAAYC,IAAiBC,EAAAA,EAAAA,UAG1BJ,EAAiB,CAAEK,IAAKL,EAAgBM,UAAWL,GAAyB,OAE/EM,EAAYC,IAAiBJ,EAAAA,EAAAA,UAAS,KACtCK,EAAaC,IAAkBN,EAAAA,EAAAA,UAAS,IACxCO,EAAcC,IAAmBR,EAAAA,EAAAA,UAAmB,KACpDS,EAAYC,IAAiBV,EAAAA,EAAAA,UAAwB,MAWtDW,GAAaC,EAAAA,EAAAA,UAAQ,IACpBd,EAEE,IAAId,GAAM6B,MAAK,CAACC,EAAGC,KACxB,MAAMC,EAASF,EAAEhB,EAAWG,KACtBgB,EAASF,EAAEjB,EAAWG,KAG5B,OAAc,MAAVe,GAA4B,MAAVC,EAAuB,EAC/B,MAAVD,EAAgD,QAAzBlB,EAAWI,WAAuB,EAAI,EACnD,MAAVe,EAAgD,QAAzBnB,EAAWI,UAAsB,GAAK,EAG3C,kBAAXc,GAAyC,kBAAXC,EACP,QAAzBnB,EAAWI,UACdc,EAAOE,cAAcD,GACrBA,EAAOC,cAAcF,GAGvBA,EAASC,EACqB,QAAzBnB,EAAWI,WAAuB,EAAI,EAE3Cc,EAASC,EACqB,QAAzBnB,EAAWI,UAAsB,GAAK,EAExC,CAAC,IAxBclB,GA0BvB,CAACA,EAAMc,IAGJqB,GAAeP,EAAAA,EAAAA,UAAQ,IACtBT,EAEEQ,EAAWS,QAAQC,GACxBxD,OAAOyD,QAAQD,GAAKE,MAAKC,IAAoB,IAAlBC,EAAMC,GAAMF,EAErC,OAAc,OAAVE,QAA4BC,IAAVD,IACD,kBAAVA,GAEJE,OAAOF,GAAOG,cAAcC,SAAS3B,EAAW0B,eAAc,MARjDlB,GAWvB,CAACA,EAAYR,IAGV4B,EAAaC,KAAKC,KAAKd,EAAae,OAAS9C,GAC7C+C,GAAgBvB,EAAAA,EAAAA,UAAQ,KAC5B,MAAMwB,GAAc/B,EAAc,GAAKjB,EACvC,OAAO+B,EAAakB,MAAMD,EAAYA,EAAahD,EAAS,GAC3D,CAAC+B,EAAcd,EAAajB,IAEzBkD,EAAoBC,IACxBjC,EAAeiC,EAAK,EA0ChBC,EAAqBC,IACzB,IAAIC,EAAU,4BAEd,GAAsB,kBAAXD,EAAqB,CAC9B,MAAME,EAAcF,EAAOZ,cAEvBc,EAAYb,SAAS,WAAaa,EAAYb,SAAS,aACvDa,EAAYb,SAAS,aAAea,EAAYb,SAAS,cACzDa,EAAYb,SAAS,WACvBY,EAAU,8BACDC,EAAYb,SAAS,YAAca,EAAYb,SAAS,cACjEY,EAAU,gCACDC,EAAYb,SAAS,aAAea,EAAYb,SAAS,WAC1Da,EAAYb,SAAS,WAAaa,EAAYb,SAAS,SAC/DY,EAAU,0BACDC,EAAYb,SAAS,cAC9BY,EAAU,4BAEd,CAEA,OACEtG,EAAAA,EAAAA,KAAA,QAAMjB,UAAW,2EAA2EuH,IAAUxH,SACnGuH,GACI,EAIX,OACEtG,EAAAA,EAAAA,MAAA,OACEhB,UAAW,oHAAoHA,IAC/H,cAAaW,EAAOZ,SAAA,EAGlBF,GAASkE,KACT/C,EAAAA,EAAAA,MAAA,OAAKhB,UAAW,sCAAsCE,IAAkBH,SAAA,CACpD,kBAAVF,GACNoB,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,sCAAqCD,SAAEF,IAErDA,EAEsB,kBAAhBkE,GACN9C,EAAAA,EAAAA,KAAA,KAAGjB,UAAU,6BAA4BD,SAAEgE,IAE3CA,MAMN/C,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,kGAAiGD,SAAA,EAC9GiB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,kBAAiBD,SAAA,EAC9BkB,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,uEAAsED,UACnFkB,EAAAA,EAAAA,KAACwC,EAAmB,CAACzD,UAAU,6BAEjCiB,EAAAA,EAAAA,KAAA,SACEwG,KAAK,OACLC,YAAY,YACZ1H,UAAU,sMACVuG,MAAOvB,EACP2C,SAAWC,IACT3C,EAAc2C,EAAEC,OAAOtB,OACvBpB,EAAe,EAAE,EAEnB,cAAa,GAAGxE,iBAIpBK,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,8BAA6BD,SAAA,CACzCqF,EAAa2B,OAAS,IACrB/F,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,8BAA6BD,SAAA,EAC1CiB,EAAAA,EAAAA,MAAA,QAAMhB,UAAU,wBAAuBD,SAAA,CAAEqF,EAAa2B,OAAO,gBAC7D9F,EAAAA,EAAAA,KAAA,UACEjB,UAAU,uGACVM,QAASA,KACP+E,EAAgB,IACZhB,GAAmBA,EAAkB,GAAG,EAE9C,cAAa,GAAG1D,oBAAyBZ,SAC1C,aAKJuE,SAKLrD,EAAAA,EAAAA,KAAA,OAAKjB,UAAW,mBAAmBC,IAAgBF,SAChDW,GACCO,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,yCAAwCD,UACrDkB,EAAAA,EAAAA,KAAC6G,EAAAA,EAAc,CAAC3G,KAAK,KAAKC,QAAQ,eAGpCJ,EAAAA,EAAAA,MAAA,SAAOhB,UAAU,sCAAqCD,SAAA,EACpDkB,EAAAA,EAAAA,KAAA,SAAOjB,UAAU,aAAYD,UAC3BiB,EAAAA,EAAAA,MAAA,MAAAjB,SAAA,CACGqE,IACCnD,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,iBAAgBD,UAC5BkB,EAAAA,EAAAA,KAAA,SACEwG,KAAK,WACLzH,UAAU,kEACV2H,SAtHKI,IACvB,MAAMC,EAAkBD,EAAMF,OAAOI,QACjCC,MAAMC,KAAK,CAAEpB,OAAQC,EAAcD,SAAU,CAACqB,EAAGC,IAAMA,IACvD,GAIJ,GAFAhD,EAAgB2C,GAEZ3D,EAAmB,CACrB,MAAMiE,EAAgBN,EACnBO,KAAIC,GAAOxB,EAAcwB,KACzBvC,QAAQwC,QAA6BjC,IAATiC,IAC/BpE,EAAkBiE,EACpB,GA2GkBL,QAAS7C,EAAa2B,SAAWC,EAAcD,QAAUC,EAAcD,OAAS,EAChF,cAAa,GAAGpG,mBAIrBiD,EAAQ2E,KAAKG,IACZzH,EAAAA,EAAAA,KAAA,MAEEjB,UAAW,kBAAkB0I,EAAOC,OAAS,qEAAqED,EAAOE,SAAW,mCAAqC,qCAAqCF,EAAOG,MAAQH,EAAOG,MAAQ,MAAMH,EAAO1I,WAAa,KACtQM,QAASA,IAAMoI,EAAOE,UAtNpB9D,KAClB,IAAIC,EAA4B,MAC5BJ,GAAcA,EAAWG,MAAQA,GAAgC,QAAzBH,EAAWI,YACrDA,EAAY,QAEdH,EAAc,CAAEE,MAAKC,aAAY,EAiNiB+D,CAAWJ,EAAO5D,KACpD,cAAa,GAAGnE,YAAiB+H,EAAO5D,MAAM/E,UAE9CiB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,8BAA6BD,SAAA,EAC1CkB,EAAAA,EAAAA,KAAA,QAAAlB,SAAO2I,EAAOK,QACbL,EAAOE,WACN3H,EAAAA,EAAAA,KAAA,QAAMjB,UAAW,oCACL,OAAV2E,QAAU,IAAVA,OAAU,EAAVA,EAAYG,OAAQ4D,EAAO5D,IAAM,eAAiB,iBACjD/E,UACU,OAAV4E,QAAU,IAAVA,OAAU,EAAVA,EAAYG,OAAQ4D,EAAO5D,KAAgC,QAAzBH,EAAWI,WAC1C9D,EAAAA,EAAAA,KAACyC,EAAa,CAAC1D,UAAU,aACf,OAAV2E,QAAU,IAAVA,OAAU,EAAVA,EAAYG,OAAQ4D,EAAO5D,KAAgC,SAAzBH,EAAWI,WAC3C9D,EAAAA,EAAAA,KAAC+H,EAAAA,EAAe,CAAChJ,UAAU,aAC3BiB,EAAAA,EAAAA,KAAA,QAAMjB,UAAU,gBAAeD,SAAC,iBAfvC2I,EAAO5D,aAuBpB7D,EAAAA,EAAAA,KAAA,SAAOjB,UAAU,oCAAmCD,SACjDiH,EAAcD,OAAS,EACtBC,EAAcuB,KAAI,CAACrC,EAAK+C,KACtBjI,EAAAA,EAAAA,MAAA,MAEEhB,UAAW,qCACT8D,EAAa,iBAAmB,MAC9BsB,EAAauB,SAASsC,GAAS,0BAA4B,2BAC7D3D,IAAe2D,EAAQ,aAAe,2BACtCzE,EAAeA,EAAa0B,EAAK+C,GAAS,KAC5C3I,QAASA,IAAMwD,GAAcA,EAAWoC,GACxCgD,aAAcA,IAAM3D,EAAc0D,GAClCE,aAAcA,IAAM5D,EAAc,MAClC,cAAa,GAAG5E,SAAcsI,IAAQlJ,SAAA,CAErCqE,IACCnD,EAAAA,EAAAA,KAAA,MAAIjB,UAAU,8BAA6BD,UACzCkB,EAAAA,EAAAA,KAAA,SACEwG,KAAK,WACLzH,UAAU,kEACViI,QAAS7C,EAAauB,SAASsC,GAC/BtB,SAAUA,OACVrH,QAAUsH,GAjMVwB,EAACH,EAAelB,KACtCA,EAAMsB,kBAEN,MAAMrB,EAAkB,IAAI5C,GAE5B,GAAIA,EAAauB,SAASsC,GAAQ,CAChC,MAAMT,EAAMR,EAAgBsB,QAAQL,GACpCjB,EAAgBuB,OAAOf,EAAK,EAC9B,MACER,EAAgBwB,KAAKP,GAKvB,GAFA5D,EAAgB2C,GAEZ3D,EAAmB,CACrB,MAAMiE,EAAgBN,EACnBO,KAAIC,GAAOxB,EAAcwB,KACzBvC,QAAQwC,QAA6BjC,IAATiC,IAC/BpE,EAAkBiE,EACpB,GA8KsCc,CAAgBH,EAAOrB,GACvC,cAAa,GAAGjH,SAAcsI,iBAInCrF,EAAQ2E,KAAKG,IACZzH,EAAAA,EAAAA,KAAA,MAEEjB,UAAW,oFAAoF0I,EAAOC,OAAS,UAAUD,EAAO1I,WAAa,KAC7I,cAAa,GAAGW,SAAcsI,UAAcP,EAAO5D,MAAM/E,SAExD2I,EAAOe,OACJf,EAAOe,OAAOvD,EAAIwC,EAAO5D,KAAMoB,GAC/BwC,EAAO5D,IAAI4B,cAAcC,SAAS,UAChCU,EAAkBnB,EAAIwC,EAAO5D,MAC7BoB,EAAIwC,EAAO5D,MARZ4D,EAAO5D,SAzBXmE,MAuCThI,EAAAA,EAAAA,KAAA,MAAAlB,UACEkB,EAAAA,EAAAA,KAAA,MACEyI,QAAS9F,EAAQmD,QAAU3C,EAAa,EAAI,GAC5CpE,UAAU,uCACV,cAAa,GAAGW,kBAAuBZ,SAEtCwE,aAUdP,GAAc4C,EAAa,IAC1B5F,EAAAA,EAAAA,MAAA,OAAKhB,UAAW,wEAAwEG,IAAkBJ,SAAA,EACxGiB,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,wBAAuBD,SAAA,CAAC,YAC1BmF,EAAc,GAAKjB,EAAY,EAAE,OAAK4C,KAAK8C,IAAIzE,EAAcjB,EAAU+B,EAAae,QAAQ,OAAKf,EAAae,OAAO,eAElI/F,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,iBAAgBD,SAAA,EAC7BkB,EAAAA,EAAAA,KAAA,UACEX,QAASA,IAAM6G,EAAiBN,KAAK+C,IAAI,EAAG1E,EAAc,IAC1D2E,SAA0B,IAAhB3E,EACVlF,UAAW,iCACO,IAAhBkF,EACI,mCACA,mCAEN,cAAa,GAAGvE,oBAAyBZ,SAC1C,aAGAmI,MAAMC,KAAK,CAAEpB,OAAQF,KAAK8C,IAAI,EAAG/C,KAAe,CAACwB,EAAGC,KAEnD,IAAIyB,EAWJ,OATEA,EADElD,GAAc,GAEP1B,GAAe,EADdmD,EAAI,EAGLnD,GAAe0B,EAAa,EAC3BA,EAAa,EAAIyB,EAEjBnD,EAAc,EAAImD,GAI5BpH,EAAAA,EAAAA,KAAA,UAEEX,QAASA,IAAM6G,EAAiB2C,GAChC9J,UAAW,iCACTkF,IAAgB4E,EACZ,wBACA,mCAEN,cAAa,GAAGnJ,gBAAqBmJ,IAAU/J,SAE9C+J,GATIA,EAUE,KAGb7I,EAAAA,EAAAA,KAAA,UACEX,QAASA,IAAM6G,EAAiBN,KAAK8C,IAAI/C,EAAY1B,EAAc,IACnE2E,SAAU3E,IAAgB0B,EAC1B5G,UAAW,iCACTkF,IAAgB0B,EACZ,mCACA,mCAEN,cAAa,GAAGjG,oBAAyBZ,SAC1C,iBAQb,CAEA,SAAemB,EAAAA,EAAAA,MAAKyC,E,yDCpZpB,MAAMoG,EAAgCnK,IAmB/B,IAnBgC,SACrCG,EAAQ,QACRqB,EAAU,UAAS,KACnBD,EAAO,KAAI,UACXnB,EAAY,GAAE,QACdM,EAAO,SACPuJ,GAAW,EAAK,KAChBpC,EAAO,SAAQ,KACfrH,EAAI,aACJ4J,EAAe,OAAM,UACrBC,GAAY,EAAK,QACjBvJ,GAAU,EAAK,QACfwJ,GAAU,EAAK,KACfC,EAAI,OACJtC,EAAM,IACNuC,EAAG,MACHvK,EAAK,UACLwK,EAAS,OACT1J,GACDf,EACC,MAwBM0K,EAAgB,kKAtBC,CACrBC,QAAS,uEACTC,UAAW,0EACXC,QAAS,4FACTC,OAAQ,oEACRC,QAAS,0EACTC,KAAM,2EACNC,KAAM,kFAiBWzJ,WAdC,CAClB0J,GAAI,oBACJtJ,GAAI,sBACJK,GAAI,oBACJC,GAAI,wBACJiJ,GAAI,qBAUU5J,WAPQ0I,EAAW,gCAAkC,yBAClDI,EAAY,SAAW,WACrBC,EAAU,eAAiB,qBAS5ClK,QAGEgL,GACJhK,EAAAA,EAAAA,MAAAiK,EAAAA,SAAA,CAAAlL,SAAA,CACGW,IACCM,EAAAA,EAAAA,MAAA,OACEhB,UAAU,+CACV4C,MAAM,6BACNC,KAAK,OACLC,QAAQ,YACR,cAAY,OAAM/C,SAAA,EAElBkB,EAAAA,EAAAA,KAAA,UACEjB,UAAU,aACVkL,GAAG,KACHC,GAAG,KACHC,EAAE,KACFpI,OAAO,eACPD,YAAY,OAEd9B,EAAAA,EAAAA,KAAA,QACEjB,UAAU,aACV6C,KAAK,eACLQ,EAAE,uHAKPjD,GAAyB,SAAjB4J,IAA4BtJ,IACnCO,EAAAA,EAAAA,KAAA,QAAMjB,UAAU,OAAMD,SAAEK,IAGzBL,EAEAK,GAAyB,UAAjB4J,IACP/I,EAAAA,EAAAA,KAAA,QAAMjB,UAAU,OAAMD,SAAEK,OAM9B,OAAI+J,GAEAlJ,EAAAA,EAAAA,KAAA,KACEkJ,KAAMA,EACNnK,UAAWsK,EACXzC,OAAQA,EACRuC,IAAKA,IAAmB,WAAXvC,EAAsB,2BAAwBrB,GAC3DlG,QAASA,EACTT,MAAOA,EACP,aAAYwK,EACZ,cAAa1J,EAAOZ,SAEnBiL,KAOL/J,EAAAA,EAAAA,KAAA,UACEwG,KAAMA,EACNzH,UAAWsK,EACXhK,QAASA,EACTuJ,SAAUA,GAAYnJ,EACtBb,MAAOA,EACP,aAAYwK,EACZ,cAAa1J,EAAOZ,SAEnBiL,GACM,EAIb,GAAe9J,EAAAA,EAAAA,MAAK6I,E,oJC1IpB,MAAMsB,EAAuBA,KAC3B,MAAMC,GAAWC,EAAAA,EAAAA,OAGX,OAAEC,EAAM,UAAEC,IAAcC,EAAAA,EAAAA,MAEvBC,EAAcC,IAAmB/G,EAAAA,EAAAA,UAAoE,QACrGgH,EAAaC,IAAkBjH,EAAAA,EAAAA,WAAS,GAGzCkH,GAAiBtG,EAAAA,EAAAA,UAAQ,IACR,QAAjBkG,EAA+BH,EAC5BA,EAAOvF,QAAO+F,GAASA,EAAM1E,SAAWqE,KAC9C,CAACH,EAAQG,IAGNM,GAAmBC,EAAAA,EAAAA,cAAaF,IACpCV,EAASa,EAAAA,EAAOC,qBAAqBJ,EAAM9I,IAAI,GAC9C,CAACoI,IAEEe,GAAqBH,EAAAA,EAAAA,cAAY,KACrCJ,GAAe,GAEfQ,YAAW,KACTR,GAAe,GACfS,QAAQC,IAAI,sBAAsB,GACjC,KAAK,GACP,IAEH,OACExL,EAAAA,EAAAA,MAAA,OAAKhB,UAAU,YAAWD,SAAA,EACxBkB,EAAAA,EAAAA,KAACwL,EAAAA,EAAU,CACT5M,MAAM,SACNkE,YAAY,4CACZO,SACErD,EAAAA,EAAAA,KAAC8I,EAAAA,EAAM,CACL3I,QAAQ,UACRhB,MAAMa,EAAAA,EAAAA,KAACoB,EAAAA,EAAiB,CAACrC,UAAU,YACnCM,QAAS+L,EACT3L,QAASmL,EAAY9L,SACtB,qBAMLiB,EAAAA,EAAAA,MAACrB,EAAAA,EAAI,CAAAI,SAAA,EACHkB,EAAAA,EAAAA,KAACyL,EAAAA,GAAW,CACVf,aAAcA,EACdgB,eAAgBf,IAGjBH,GACCxK,EAAAA,EAAAA,KAAA,OAAKjB,UAAU,2BAA0BD,UACvCkB,EAAAA,EAAAA,KAAC6G,EAAAA,EAAc,OAGjB7G,EAAAA,EAAAA,KAAC2L,EAAAA,GAAS,CACRpB,OAAQO,EACRc,aAAcZ,EACdpM,MAAO,GAAG8L,EAAamB,OAAO,GAAGC,cAAgBpB,EAAazE,MAAM,cAAc6E,EAAehF,iBAInG,EAIV,GAAe7F,EAAAA,EAAAA,MAAKmK,E", "sources": ["components/common/Card.tsx", "components/common/LoadingSpinner.tsx", "../node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js", "../node_modules/@heroicons/react/24/outline/esm/TruckIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ClockIcon.js", "../node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js", "../node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ChevronUpIcon.js", "components/common/DataTable.tsx", "components/common/Button.tsx", "pages/OrdersPage.tsx"], "sourcesContent": ["/**\r\n * Card Component\r\n *\r\n * A reusable card component for displaying content in a contained box.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\nexport interface CardProps {\r\n  title?: string | ReactNode;\r\n  subtitle?: string | ReactNode;\r\n  children: ReactNode;\r\n  className?: string;\r\n  bodyClassName?: string;\r\n  headerClassName?: string;\r\n  footerClassName?: string;\r\n  icon?: ReactNode;\r\n  footer?: ReactNode;\r\n  onClick?: () => void;\r\n  hoverable?: boolean;\r\n  noPadding?: boolean;\r\n  bordered?: boolean;\r\n  loading?: boolean;\r\n  testId?: string;\r\n}\r\n\r\nconst Card: React.FC<CardProps> = ({\r\n  title,\r\n  subtitle,\r\n  children,\r\n  className = '',\r\n  bodyClassName = '',\r\n  headerClassName = '',\r\n  footerClassName = '',\r\n  icon,\r\n  footer,\r\n  onClick,\r\n  hoverable = false,\r\n  noPadding = false,\r\n  bordered = true,\r\n  loading = false,\r\n  testId,\r\n}) => {\r\n  // Base classes\r\n  const cardClasses = `\r\n    bg-white rounded-xl ${bordered ? 'border border-gray-100' : ''} overflow-hidden transition-all duration-300\r\n    ${hoverable ? 'hover:shadow-md hover:border-gray-200 transform hover:-translate-y-1' : 'shadow-sm'}\r\n    ${onClick ? 'cursor-pointer' : ''}\r\n    ${className}\r\n  `;\r\n\r\n  // Header classes\r\n  const headerClasses = `\r\n    px-6 py-4 border-b border-gray-100 flex items-center justify-between\r\n    ${headerClassName}\r\n  `;\r\n\r\n  // Body classes\r\n  const bodyClasses = `\r\n    ${noPadding ? '' : 'p-6'}\r\n    ${bodyClassName}\r\n  `;\r\n\r\n  // Footer classes\r\n  const footerClasses = `\r\n    px-6 py-4 bg-gray-50 border-t border-gray-100\r\n    ${footerClassName}\r\n  `;\r\n\r\n  // Loading skeleton\r\n  if (loading) {\r\n    return (\r\n      <div className={cardClasses} data-testid={testId}>\r\n        {(title || subtitle || icon) && (\r\n          <div className={headerClasses}>\r\n            <div className=\"w-full\">\r\n              {title && <div className=\"h-6 bg-gray-200 rounded w-1/3 animate-pulse\"></div>}\r\n              {subtitle && <div className=\"h-4 mt-2 bg-gray-200 rounded w-1/2 animate-pulse\"></div>}\r\n            </div>\r\n            {icon && <div className=\"h-8 w-8 bg-gray-200 rounded-full animate-pulse\"></div>}\r\n          </div>\r\n        )}\r\n\r\n        <div className={bodyClasses}>\r\n          <div className=\"h-24 bg-gray-200 rounded animate-pulse\"></div>\r\n        </div>\r\n\r\n        {footer && (\r\n          <div className={footerClasses}>\r\n            <div className=\"h-8 bg-gray-200 rounded w-1/4 animate-pulse\"></div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className={cardClasses}\r\n      onClick={onClick}\r\n      data-testid={testId}\r\n    >\r\n      {(title || subtitle || icon) && (\r\n        <div className={headerClasses}>\r\n          <div>\r\n            {typeof title === 'string' ? (\r\n              <h3 className=\"text-lg font-semibold text-primary\">{title}</h3>\r\n            ) : (\r\n              title\r\n            )}\r\n            {typeof subtitle === 'string' ? (\r\n              <p className=\"mt-1 text-sm text-gray-500\">{subtitle}</p>\r\n            ) : (\r\n              subtitle\r\n            )}\r\n          </div>\r\n          {icon && <div className=\"text-primary\">{icon}</div>}\r\n        </div>\r\n      )}\r\n\r\n      <div className={bodyClasses}>{children}</div>\r\n\r\n      {footer && (\r\n        <div className={footerClasses}>\r\n          {footer}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(Card);\r\n", "// src/components/common/LoadingSpinner.tsx\r\nimport React from 'react';\r\nimport './LoadingSpinner.css';\r\n\r\ninterface LoadingSpinnerProps {\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  variant?: 'spinner' | 'dots' | 'pulse' | 'ripple';\r\n  color?: string;\r\n  useCurrentColor?: boolean;\r\n}\r\n\r\nconst LoadingSpinner: React.FC<LoadingSpinnerProps> = ({\r\n  size = 'md',\r\n  className = '',\r\n  variant = 'spinner',\r\n  color = '#F28B22', // Primary color\r\n  useCurrentColor = false\r\n}) => {\r\n  const sizeMap = {\r\n    sm: { spinner: 'w-5 h-5', dots: 'w-1 h-1', pulse: 'w-4 h-4', ripple: 'w-6 h-6' },\r\n    md: { spinner: 'w-8 h-8', dots: 'w-1.5 h-1.5', pulse: 'w-6 h-6', ripple: 'w-10 h-10' },\r\n    lg: { spinner: 'w-12 h-12', dots: 'w-2 h-2', pulse: 'w-8 h-8', ripple: 'w-16 h-16' }\r\n  };\r\n\r\n  const currentColor = useCurrentColor ? 'currentColor' : color;\r\n\r\n  // Simple rotating ring spinner\r\n  if (variant === 'spinner') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`spinner-smooth rounded-full border-2 border-gray-200 ${sizeMap[size].spinner}`}\r\n          style={{\r\n            borderTopColor: currentColor,\r\n            borderRightColor: currentColor,\r\n          }}\r\n        />\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Three bouncing dots\r\n  if (variant === 'dots') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center space-x-1 dots-bounce ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`${sizeMap[size].dots} rounded-full dot`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <div\r\n          className={`${sizeMap[size].dots} rounded-full dot`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <div\r\n          className={`${sizeMap[size].dots} rounded-full dot`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Pulsing circle\r\n  if (variant === 'pulse') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`${sizeMap[size].pulse} rounded-full pulse-smooth`}\r\n          style={{ backgroundColor: currentColor }}\r\n        />\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Ripple effect\r\n  if (variant === 'ripple') {\r\n    return (\r\n      <div\r\n        className={`flex justify-center items-center ${className}`}\r\n        role=\"status\"\r\n        aria-label=\"Loading\"\r\n      >\r\n        <div\r\n          className={`${sizeMap[size].ripple} rounded-full ripple-effect`}\r\n          style={{ color: currentColor }}\r\n        >\r\n          <div\r\n            className={`${sizeMap[size].pulse} rounded-full pulse-smooth mx-auto`}\r\n            style={{ backgroundColor: currentColor }}\r\n          />\r\n        </div>\r\n        <span className=\"sr-only\">Loading...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\nexport default LoadingSpinner;", "import * as React from \"react\";\nfunction ArrowDownTrayIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ArrowDownTrayIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction TruckIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(TruckIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ClockIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ClockIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction XCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(XCircleIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction MagnifyingGlassIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MagnifyingGlassIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ChevronUpIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m4.5 15.75 7.5-7.5 7.5 7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChevronUpIcon);\nexport default ForwardRef;", "/**\r\n * DataTable Component\r\n *\r\n * A reusable data table component with sorting, filtering, pagination, and row selection.\r\n */\r\n\r\nimport React, { useState, useMemo, memo } from 'react';\r\nimport { MagnifyingGlassIcon, ChevronUpIcon, ChevronDownIcon } from '@heroicons/react/24/outline';\r\nimport LoadingSpinner from './LoadingSpinner';\r\nimport { CONFIG } from '../../constants/config';\r\n\r\nexport interface Column<T = Record<string, any>> {\r\n  key: string;\r\n  label: string;\r\n  sortable?: boolean;\r\n  render?: (value: any, row: T) => React.ReactNode;\r\n  width?: string;\r\n  align?: 'left' | 'center' | 'right';\r\n  className?: string;\r\n}\r\n\r\nexport interface DataTableProps<T = Record<string, any>> {\r\n  columns: Column<T>[];\r\n  data: T[];\r\n  onRowClick?: ((row: T) => void) | undefined;\r\n  title?: string | React.ReactNode;\r\n  description?: string | React.ReactNode;\r\n  loading?: boolean;\r\n  pagination?: boolean;\r\n  pageSize?: number;\r\n  selectable?: boolean;\r\n  onSelectionChange?: (selectedRows: T[]) => void;\r\n  actions?: React.ReactNode;\r\n  emptyMessage?: string;\r\n  className?: string;\r\n  headerClassName?: string;\r\n  bodyClassName?: string;\r\n  footerClassName?: string;\r\n  rowClassName?: (row: T, index: number) => string;\r\n  initialSortKey?: string;\r\n  initialSortDirection?: 'asc' | 'desc';\r\n  testId?: string;\r\n}\r\n\r\nfunction DataTable<T extends Record<string, any>>({\r\n  columns,\r\n  data,\r\n  onRowClick,\r\n  title,\r\n  description,\r\n  loading = false,\r\n  pagination = true,\r\n  pageSize = CONFIG.DEFAULT_PAGE_SIZE,\r\n  selectable = true,\r\n  onSelectionChange,\r\n  actions,\r\n  emptyMessage = 'No results found',\r\n  className = '',\r\n  headerClassName = '',\r\n  bodyClassName = '',\r\n  footerClassName = '',\r\n  rowClassName,\r\n  initialSortKey,\r\n  initialSortDirection = 'asc',\r\n  testId,\r\n}: DataTableProps<T>) {\r\n  // State\r\n  const [sortConfig, setSortConfig] = useState<{\r\n    key: string;\r\n    direction: 'asc' | 'desc';\r\n  } | null>(initialSortKey ? { key: initialSortKey, direction: initialSortDirection } : null);\r\n\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [selectedRows, setSelectedRows] = useState<number[]>([]);\r\n  const [hoveredRow, setHoveredRow] = useState<number | null>(null);\r\n\r\n  // Sorting\r\n  const handleSort = (key: string) => {\r\n    let direction: 'asc' | 'desc' = 'asc';\r\n    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {\r\n      direction = 'desc';\r\n    }\r\n    setSortConfig({ key, direction });\r\n  };\r\n\r\n  const sortedData = useMemo(() => {\r\n    if (!sortConfig) return data;\r\n\r\n    return [...data].sort((a, b) => {\r\n      const aValue = a[sortConfig.key];\r\n      const bValue = b[sortConfig.key];\r\n\r\n      // Handle null or undefined values\r\n      if (aValue == null && bValue == null) return 0;\r\n      if (aValue == null) return sortConfig.direction === 'asc' ? -1 : 1;\r\n      if (bValue == null) return sortConfig.direction === 'asc' ? 1 : -1;\r\n\r\n      // Handle different data types\r\n      if (typeof aValue === 'string' && typeof bValue === 'string') {\r\n        return sortConfig.direction === 'asc'\r\n          ? aValue.localeCompare(bValue)\r\n          : bValue.localeCompare(aValue);\r\n      }\r\n\r\n      if (aValue < bValue) {\r\n        return sortConfig.direction === 'asc' ? -1 : 1;\r\n      }\r\n      if (aValue > bValue) {\r\n        return sortConfig.direction === 'asc' ? 1 : -1;\r\n      }\r\n      return 0;\r\n    });\r\n  }, [data, sortConfig]);\r\n\r\n  // Filtering\r\n  const filteredData = useMemo(() => {\r\n    if (!searchTerm) return sortedData;\r\n\r\n    return sortedData.filter((row) =>\r\n      Object.entries(row).some(([_key, value]) => {\r\n        // Skip filtering on complex objects\r\n        if (value === null || value === undefined) return false;\r\n        if (typeof value === 'object') return false;\r\n\r\n        return String(value).toLowerCase().includes(searchTerm.toLowerCase());\r\n      })\r\n    );\r\n  }, [sortedData, searchTerm]);\r\n\r\n  // Pagination\r\n  const totalPages = Math.ceil(filteredData.length / pageSize);\r\n  const paginatedData = useMemo(() => {\r\n    const startIndex = (currentPage - 1) * pageSize;\r\n    return filteredData.slice(startIndex, startIndex + pageSize);\r\n  }, [filteredData, currentPage, pageSize]);\r\n\r\n  const handlePageChange = (page: number) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  // Row selection\r\n  const handleRowSelect = (index: number, event: React.MouseEvent) => {\r\n    event.stopPropagation();\r\n\r\n    const newSelectedRows = [...selectedRows];\r\n\r\n    if (selectedRows.includes(index)) {\r\n      const idx = newSelectedRows.indexOf(index);\r\n      newSelectedRows.splice(idx, 1);\r\n    } else {\r\n      newSelectedRows.push(index);\r\n    }\r\n\r\n    setSelectedRows(newSelectedRows);\r\n\r\n    if (onSelectionChange) {\r\n      const selectedItems = newSelectedRows\r\n        .map(idx => paginatedData[idx])\r\n        .filter((item): item is T => item !== undefined);\r\n      onSelectionChange(selectedItems);\r\n    }\r\n  };\r\n\r\n  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const newSelectedRows = event.target.checked\r\n      ? Array.from({ length: paginatedData.length }, (_, i) => i)\r\n      : [];\r\n\r\n    setSelectedRows(newSelectedRows);\r\n\r\n    if (onSelectionChange) {\r\n      const selectedItems = newSelectedRows\r\n        .map(idx => paginatedData[idx])\r\n        .filter((item): item is T => item !== undefined);\r\n      onSelectionChange(selectedItems);\r\n    }\r\n  };\r\n\r\n  // Status badge renderer\r\n  const renderStatusBadge = (status: string) => {\r\n    let bgColor = 'bg-gray-100 text-gray-800';\r\n\r\n    if (typeof status === 'string') {\r\n      const statusLower = status.toLowerCase();\r\n\r\n      if (statusLower.includes('active') || statusLower.includes('approved') ||\r\n          statusLower.includes('verified') || statusLower.includes('completed') ||\r\n          statusLower.includes('success')) {\r\n        bgColor = 'bg-green-100 text-green-800';\r\n      } else if (statusLower.includes('pending') || statusLower.includes('processing')) {\r\n        bgColor = 'bg-yellow-100 text-yellow-800';\r\n      } else if (statusLower.includes('rejected') || statusLower.includes('banned') ||\r\n                statusLower.includes('failed') || statusLower.includes('error')) {\r\n        bgColor = 'bg-red-100 text-red-800';\r\n      } else if (statusLower.includes('inactive')) {\r\n        bgColor = 'bg-gray-100 text-gray-800';\r\n      }\r\n    }\r\n\r\n    return (\r\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${bgColor}`}>\r\n        {status}\r\n      </span>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-md ${className}`}\r\n      data-testid={testId}\r\n    >\r\n      {/* Header */}\r\n      {(title || description) && (\r\n        <div className={`px-6 py-4 border-b border-gray-100 ${headerClassName}`}>\r\n          {typeof title === 'string' ? (\r\n            <h3 className=\"text-lg font-semibold text-gray-800\">{title}</h3>\r\n          ) : (\r\n            title\r\n          )}\r\n          {typeof description === 'string' ? (\r\n            <p className=\"mt-1 text-sm text-gray-500\">{description}</p>\r\n          ) : (\r\n            description\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      {/* Search and Actions */}\r\n      <div className=\"p-4 border-b border-gray-100 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\r\n        <div className=\"relative flex-1\">\r\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n            <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\r\n          </div>\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"Search...\"\r\n            className=\"block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200\"\r\n            value={searchTerm}\r\n            onChange={(e) => {\r\n              setSearchTerm(e.target.value);\r\n              setCurrentPage(1); // Reset to first page on search\r\n            }}\r\n            data-testid={`${testId}-search`}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-2\">\r\n          {selectedRows.length > 0 && (\r\n            <div className=\"flex items-center space-x-2\">\r\n              <span className=\"text-sm text-gray-500\">{selectedRows.length} selected</span>\r\n              <button\r\n                className=\"px-3 py-1.5 bg-red-50 text-red-600 rounded-md text-sm font-medium hover:bg-red-100 transition-colors\"\r\n                onClick={() => {\r\n                  setSelectedRows([]);\r\n                  if (onSelectionChange) onSelectionChange([]);\r\n                }}\r\n                data-testid={`${testId}-clear-selection`}\r\n              >\r\n                Clear\r\n              </button>\r\n            </div>\r\n          )}\r\n          {actions}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Table */}\r\n      <div className={`overflow-x-auto ${bodyClassName}`}>\r\n        {loading ? (\r\n          <div className=\"flex justify-center items-center py-20\">\r\n            <LoadingSpinner size=\"lg\" variant=\"spinner\" />\r\n          </div>\r\n        ) : (\r\n          <table className=\"min-w-full divide-y divide-gray-100\">\r\n            <thead className=\"bg-gray-50\">\r\n              <tr>\r\n                {selectable && (\r\n                  <th className=\"w-12 px-6 py-3\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      className=\"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary\"\r\n                      onChange={handleSelectAll}\r\n                      checked={selectedRows.length === paginatedData.length && paginatedData.length > 0}\r\n                      data-testid={`${testId}-select-all`}\r\n                    />\r\n                  </th>\r\n                )}\r\n                {columns.map((column) => (\r\n                  <th\r\n                    key={column.key}\r\n                    className={`px-6 py-3 text-${column.align || 'left'} text-xs font-medium text-gray-500 uppercase tracking-wider ${column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''} transition-colors duration-200 ${column.width ? column.width : ''} ${column.className || ''}`}\r\n                    onClick={() => column.sortable && handleSort(column.key)}\r\n                    data-testid={`${testId}-column-${column.key}`}\r\n                  >\r\n                    <div className=\"flex items-center space-x-1\">\r\n                      <span>{column.label}</span>\r\n                      {column.sortable && (\r\n                        <span className={`transition-colors duration-200 ${\r\n                          sortConfig?.key === column.key ? 'text-primary' : 'text-gray-400'\r\n                        }`}>\r\n                          {sortConfig?.key === column.key && sortConfig.direction === 'asc'\r\n                            ? <ChevronUpIcon className=\"h-4 w-4\" />\r\n                            : sortConfig?.key === column.key && sortConfig.direction === 'desc'\r\n                              ? <ChevronDownIcon className=\"h-4 w-4\" />\r\n                              : <span className=\"text-gray-300\">↕</span>}\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                  </th>\r\n                ))}\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"bg-white divide-y divide-gray-100\">\r\n              {paginatedData.length > 0 ? (\r\n                paginatedData.map((row, index) => (\r\n                  <tr\r\n                    key={index}\r\n                    className={`group transition-all duration-200 ${\r\n                      onRowClick ? 'cursor-pointer' : ''\r\n                    } ${selectedRows.includes(index) ? 'bg-primary bg-opacity-5' : ''}\r\n                    ${hoveredRow === index ? 'bg-gray-50' : ''}\r\n                    ${rowClassName ? rowClassName(row, index) : ''}`}\r\n                    onClick={() => onRowClick && onRowClick(row)}\r\n                    onMouseEnter={() => setHoveredRow(index)}\r\n                    onMouseLeave={() => setHoveredRow(null)}\r\n                    data-testid={`${testId}-row-${index}`}\r\n                  >\r\n                    {selectable && (\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          className=\"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary\"\r\n                          checked={selectedRows.includes(index)}\r\n                          onChange={() => {}} // Empty handler to avoid React warning about controlled component\r\n                          onClick={(e) => handleRowSelect(index, e)}\r\n                          data-testid={`${testId}-row-${index}-checkbox`}\r\n                        />\r\n                      </td>\r\n                    )}\r\n                    {columns.map((column) => (\r\n                      <td\r\n                        key={column.key}\r\n                        className={`px-6 py-4 whitespace-nowrap text-sm text-gray-600 group-hover:text-gray-900 text-${column.align || 'left'} ${column.className || ''}`}\r\n                        data-testid={`${testId}-row-${index}-cell-${column.key}`}\r\n                      >\r\n                        {column.render\r\n                          ? column.render(row[column.key], row)\r\n                          : column.key.toLowerCase().includes('status')\r\n                            ? renderStatusBadge(row[column.key])\r\n                            : row[column.key]}\r\n                      </td>\r\n                    ))}\r\n                  </tr>\r\n                ))\r\n              ) : (\r\n                <tr>\r\n                  <td\r\n                    colSpan={columns.length + (selectable ? 1 : 0)}\r\n                    className=\"px-6 py-10 text-center text-gray-500\"\r\n                    data-testid={`${testId}-empty-message`}\r\n                  >\r\n                    {emptyMessage}\r\n                  </td>\r\n                </tr>\r\n              )}\r\n            </tbody>\r\n          </table>\r\n        )}\r\n      </div>\r\n\r\n      {/* Pagination */}\r\n      {pagination && totalPages > 1 && (\r\n        <div className={`px-6 py-4 border-t border-gray-100 flex items-center justify-between ${footerClassName}`}>\r\n          <div className=\"text-sm text-gray-500\">\r\n            Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, filteredData.length)} of {filteredData.length} entries\r\n          </div>\r\n          <div className=\"flex space-x-1\">\r\n            <button\r\n              onClick={() => handlePageChange(Math.max(1, currentPage - 1))}\r\n              disabled={currentPage === 1}\r\n              className={`px-3 py-1 rounded-md text-sm ${\r\n                currentPage === 1\r\n                  ? 'text-gray-400 cursor-not-allowed'\r\n                  : 'text-gray-700 hover:bg-gray-100'\r\n              }`}\r\n              data-testid={`${testId}-pagination-prev`}\r\n            >\r\n              Previous\r\n            </button>\r\n            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\r\n              // Show pages around current page\r\n              let pageNum: number;\r\n              if (totalPages <= 5) {\r\n                pageNum = i + 1;\r\n              } else if (currentPage <= 3) {\r\n                pageNum = i + 1;\r\n              } else if (currentPage >= totalPages - 2) {\r\n                pageNum = totalPages - 4 + i;\r\n              } else {\r\n                pageNum = currentPage - 2 + i;\r\n              }\r\n\r\n              return (\r\n                <button\r\n                  key={pageNum}\r\n                  onClick={() => handlePageChange(pageNum)}\r\n                  className={`px-3 py-1 rounded-md text-sm ${\r\n                    currentPage === pageNum\r\n                      ? 'bg-primary text-white'\r\n                      : 'text-gray-700 hover:bg-gray-100'\r\n                  }`}\r\n                  data-testid={`${testId}-pagination-${pageNum}`}\r\n                >\r\n                  {pageNum}\r\n                </button>\r\n              );\r\n            })}\r\n            <button\r\n              onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}\r\n              disabled={currentPage === totalPages}\r\n              className={`px-3 py-1 rounded-md text-sm ${\r\n                currentPage === totalPages\r\n                  ? 'text-gray-400 cursor-not-allowed'\r\n                  : 'text-gray-700 hover:bg-gray-100'\r\n              }`}\r\n              data-testid={`${testId}-pagination-next`}\r\n            >\r\n              Next\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default memo(DataTable) as typeof DataTable;\r\n\r\n\r\n\r\n\r\n", "/**\r\n * Button Component\r\n * \r\n * A reusable button component with various styles and states.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\n\r\nexport type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'danger' | 'success' | 'text' | 'link';\r\nexport type ButtonSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';\r\n\r\nexport interface ButtonProps {\r\n  children: ReactNode;\r\n  variant?: ButtonVariant;\r\n  size?: ButtonSize;\r\n  className?: string;\r\n  onClick?: () => void;\r\n  disabled?: boolean;\r\n  type?: 'button' | 'submit' | 'reset';\r\n  icon?: ReactNode;\r\n  iconPosition?: 'left' | 'right';\r\n  fullWidth?: boolean;\r\n  loading?: boolean;\r\n  rounded?: boolean;\r\n  href?: string;\r\n  target?: string;\r\n  rel?: string;\r\n  title?: string;\r\n  ariaLabel?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst Button: React.FC<ButtonProps> = ({\r\n  children,\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  onClick,\r\n  disabled = false,\r\n  type = 'button',\r\n  icon,\r\n  iconPosition = 'left',\r\n  fullWidth = false,\r\n  loading = false,\r\n  rounded = false,\r\n  href,\r\n  target,\r\n  rel,\r\n  title,\r\n  ariaLabel,\r\n  testId,\r\n}) => {\r\n  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2';\r\n  \r\n  const variantClasses = {\r\n    primary: 'bg-primary text-white hover:bg-primary/90 focus-visible:ring-primary',\r\n    secondary: 'bg-gray-200 text-gray-800 hover:bg-gray-300 focus-visible:ring-gray-300',\r\n    outline: 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus-visible:ring-primary',\r\n    danger: 'bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500',\r\n    success: 'bg-green-600 text-white hover:bg-green-700 focus-visible:ring-green-500',\r\n    text: 'bg-transparent text-primary hover:bg-gray-100 focus-visible:ring-primary',\r\n    link: 'bg-transparent text-primary hover:underline focus-visible:ring-transparent p-0',\r\n  };\r\n  \r\n  const sizeClasses = {\r\n    xs: 'text-xs px-2 py-1',\r\n    sm: 'text-xs px-3 py-1.5',\r\n    md: 'text-sm px-4 py-2',\r\n    lg: 'text-base px-5 py-2.5',\r\n    xl: 'text-lg px-6 py-3',\r\n  };\r\n  \r\n  const disabledClasses = disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer';\r\n  const widthClass = fullWidth ? 'w-full' : '';\r\n  const roundedClass = rounded ? 'rounded-full' : 'rounded-lg';\r\n  \r\n  const buttonClasses = `\r\n    ${baseClasses}\r\n    ${variantClasses[variant]}\r\n    ${sizeClasses[size]}\r\n    ${disabledClasses}\r\n    ${widthClass}\r\n    ${roundedClass}\r\n    ${className}\r\n  `;\r\n  \r\n  const content = (\r\n    <>\r\n      {loading && (\r\n        <svg\r\n          className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-current\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          fill=\"none\"\r\n          viewBox=\"0 0 24 24\"\r\n          aria-hidden=\"true\"\r\n        >\r\n          <circle\r\n            className=\"opacity-25\"\r\n            cx=\"12\"\r\n            cy=\"12\"\r\n            r=\"10\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"4\"\r\n          />\r\n          <path\r\n            className=\"opacity-75\"\r\n            fill=\"currentColor\"\r\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n          />\r\n        </svg>\r\n      )}\r\n\r\n      {icon && iconPosition === 'left' && !loading && (\r\n        <span className=\"mr-2\">{icon}</span>\r\n      )}\r\n\r\n      {children}\r\n\r\n      {icon && iconPosition === 'right' && (\r\n        <span className=\"ml-2\">{icon}</span>\r\n      )}\r\n    </>\r\n  );\r\n  \r\n  // If href is provided, render an anchor tag\r\n  if (href) {\r\n    return (\r\n      <a\r\n        href={href}\r\n        className={buttonClasses}\r\n        target={target}\r\n        rel={rel || (target === '_blank' ? 'noopener noreferrer' : undefined)}\r\n        onClick={onClick}\r\n        title={title}\r\n        aria-label={ariaLabel}\r\n        data-testid={testId}\r\n      >\r\n        {content}\r\n      </a>\r\n    );\r\n  }\r\n  \r\n  // Otherwise render a button\r\n  return (\r\n    <button\r\n      type={type}\r\n      className={buttonClasses}\r\n      onClick={onClick}\r\n      disabled={disabled || loading}\r\n      title={title}\r\n      aria-label={ariaLabel}\r\n      data-testid={testId}\r\n    >\r\n      {content}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default memo(Button);\r\n", "/**\r\n * Orders Page\r\n *\r\n * This page displays and manages orders in the system.\r\n */\r\n\r\nimport React, { useState, useMemo, useCallback, memo } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport Card from '../components/common/Card';\r\nimport Button from '../components/common/Button';\r\nimport PageHeader from '../components/layout/PageHeader';\r\nimport LoadingSpinner from '../components/common/LoadingSpinner';\r\nimport { ArrowDownTrayIcon } from '@heroicons/react/24/outline';\r\nimport { ROUTES } from '../constants/routes';\r\nimport {\r\n  OrderList,\r\n  OrderFilter,\r\n  Order,\r\n  useOrders\r\n} from '../features/orders/index';\r\n\r\nconst OrdersPage: React.FC = () => {\r\n  const navigate = useNavigate();\r\n\r\n  // Use the useOrders hook for API integration\r\n  const { orders, isLoading } = useOrders();\r\n\r\n  const [activeFilter, setActiveFilter] = useState<'all' | 'pending' | 'approved' | 'completed' | 'rejected'>('all');\r\n  const [isExporting, setIsExporting] = useState(false);\r\n\r\n  // Memoize filtered orders to prevent unnecessary recalculations\r\n  const filteredOrders = useMemo(() => {\r\n    if (activeFilter === 'all') return orders;\r\n    return orders.filter(order => order.status === activeFilter);\r\n  }, [orders, activeFilter]);\r\n\r\n  // Memoize event handlers to prevent unnecessary re-renders\r\n  const handleOrderClick = useCallback((order: Order) => {\r\n    navigate(ROUTES.getOrderDetailsRoute(order.id));\r\n  }, [navigate]);\r\n\r\n  const handleExportOrders = useCallback(() => {\r\n    setIsExporting(true);\r\n    // Simulate export process\r\n    setTimeout(() => {\r\n      setIsExporting(false);\r\n      console.log('Exporting orders...');\r\n    }, 1500);\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <PageHeader\r\n        title=\"Orders\"\r\n        description=\"Manage and track all orders in the system\"\r\n        actions={\r\n          <Button\r\n            variant=\"outline\"\r\n            icon={<ArrowDownTrayIcon className=\"h-5 w-5\" />}\r\n            onClick={handleExportOrders}\r\n            loading={isExporting}\r\n          >\r\n            Export Orders\r\n          </Button>\r\n        }\r\n      />\r\n\r\n      <Card>\r\n        <OrderFilter\r\n          activeFilter={activeFilter}\r\n          onFilterChange={setActiveFilter}\r\n        />\r\n\r\n        {isLoading ? (\r\n          <div className=\"flex justify-center py-8\">\r\n            <LoadingSpinner />\r\n          </div>\r\n        ) : (\r\n          <OrderList\r\n            orders={filteredOrders}\r\n            onOrderClick={handleOrderClick}\r\n            title={`${activeFilter.charAt(0).toUpperCase() + activeFilter.slice(1)} Orders (${filteredOrders.length})`}\r\n          />\r\n        )}\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(OrdersPage);"], "names": ["Card", "_ref", "title", "subtitle", "children", "className", "bodyClassName", "headerClassName", "footerClassName", "icon", "footer", "onClick", "hoverable", "noPadding", "bordered", "loading", "testId", "cardClasses", "headerClasses", "bodyClasses", "footerClasses", "_jsxs", "_jsx", "memo", "size", "variant", "color", "useCurrentColor", "sizeMap", "sm", "spinner", "dots", "pulse", "ripple", "md", "lg", "currentColor", "role", "style", "borderTopColor", "borderRightColor", "backgroundColor", "ArrowDownTrayIcon", "svgRef", "titleId", "props", "React", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "id", "strokeLinecap", "strokeLinejoin", "d", "TruckIcon", "ClockIcon", "XCircleIcon", "MagnifyingGlassIcon", "ChevronUpIcon", "DataTable", "columns", "data", "onRowClick", "description", "pagination", "pageSize", "CONFIG", "DEFAULT_PAGE_SIZE", "selectable", "onSelectionChange", "actions", "emptyMessage", "rowClassName", "initialSortKey", "initialSortDirection", "sortConfig", "setSortConfig", "useState", "key", "direction", "searchTerm", "setSearchTerm", "currentPage", "setCurrentPage", "selectedRows", "setSelectedRows", "hoveredRow", "setHoveredRow", "sortedData", "useMemo", "sort", "a", "b", "aValue", "bValue", "localeCompare", "filteredData", "filter", "row", "entries", "some", "_ref2", "_key", "value", "undefined", "String", "toLowerCase", "includes", "totalPages", "Math", "ceil", "length", "paginatedData", "startIndex", "slice", "handlePageChange", "page", "renderStatusBadge", "status", "bgColor", "statusLower", "type", "placeholder", "onChange", "e", "target", "LoadingSpinner", "event", "newSelectedRows", "checked", "Array", "from", "_", "i", "selectedItems", "map", "idx", "item", "column", "align", "sortable", "width", "handleSort", "label", "ChevronDownIcon", "index", "onMouseEnter", "onMouseLeave", "handleRowSelect", "stopPropagation", "indexOf", "splice", "push", "render", "colSpan", "min", "max", "disabled", "pageNum", "<PERSON><PERSON>", "iconPosition", "fullWidth", "rounded", "href", "rel", "aria<PERSON><PERSON><PERSON>", "buttonClasses", "primary", "secondary", "outline", "danger", "success", "text", "link", "xs", "xl", "content", "_Fragment", "cx", "cy", "r", "OrdersPage", "navigate", "useNavigate", "orders", "isLoading", "useOrders", "activeFilter", "setActiveFilter", "isExporting", "setIsExporting", "filteredOrders", "order", "handleOrderClick", "useCallback", "ROUTES", "getOrderDetailsRoute", "handleExportOrders", "setTimeout", "console", "log", "<PERSON><PERSON><PERSON><PERSON>", "OrderFilter", "onFilterChange", "OrderList", "onOrderClick", "char<PERSON>t", "toUpperCase"], "sourceRoot": ""}