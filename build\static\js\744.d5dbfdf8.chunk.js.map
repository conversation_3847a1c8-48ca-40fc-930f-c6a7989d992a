{"version": 3, "file": "static/js/744.d5dbfdf8.chunk.js", "mappings": "oKA+BO,MAuMP,EAvM+B,WAA2C,IAA1CA,EAA+BC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EACjE,MAAM,oBAAEG,GAAsB,EAAI,gBAAEC,GAAkB,EAAI,QAAEC,GAAYN,GAClE,iBAAEO,IAAqBC,EAAAA,EAAAA,MAEtBC,EAAYC,IAAiBC,EAAAA,EAAAA,UAAqB,CACvDC,UAAU,EACVC,MAAO,KACPC,UAAW,OAIPC,GAAaC,EAAAA,EAAAA,cAAY,KAC7BN,EAAc,CACZE,UAAU,EACVC,MAAO,KACPC,UAAW,MACX,GACD,IAGGG,GAA0BD,EAAAA,EAAAA,cAAY,CAACH,EAAYK,KACvD,MAAMC,GAAWC,EAAAA,EAAAA,IACfP,EACAT,EAAuBiB,IACrBd,EAAiB,CACfe,KAAMD,EAAaC,KACnBC,MAAOF,EAAaE,MACpBC,QAASH,EAAaG,SACtB,OACArB,GAkBN,OAfAO,EAAc,CACZE,UAAU,EACVC,MAAOM,EACPL,UAAW,SACPI,GAAW,CAAEA,aAGfb,GAAmBQ,aAAiBY,QACtCC,EAAAA,EAAAA,IAAYb,EAAOK,GAGjBZ,GACFA,EAAQO,EAAOK,GAGVC,CAAQ,GACd,CAACf,EAAqBC,EAAiBE,EAAkBD,IAGtDqB,GAAiCX,EAAAA,EAAAA,cAAY,CACjDY,EACAJ,EACAK,EACAX,KAEA,MAAMY,GAAkBC,EAAAA,EAAAA,IAAsBH,EAAOJ,EAASK,GAqB9D,OAnBAnB,EAAc,CACZE,UAAU,EACVC,MAAOiB,EACPhB,UAAW,gBACPI,GAAW,CAAEA,aAGfd,GACFG,EAAiB,CACfe,KAAM,QACNC,MAAO,mBACPC,QAASM,EAAgBN,UAIzBlB,GACFA,EAAQwB,EAAiBZ,GAGpBY,CAAe,GACrB,CAAC1B,EAAqBG,EAAkBD,IAGrC0B,GAA2BhB,EAAAA,EAAAA,cAAY,CAC3CH,EACAoB,EACAf,MAEAgB,EAAAA,EAAAA,IACErB,EACAoB,EACA7B,EAAuBiB,IACrBd,EAAiB,CACfe,KAAMD,EAAaC,KACnBC,MAAOF,EAAaE,MACpBC,QAASH,EAAaG,SACtB,OACArB,GAGNO,EAAc,CACZE,UAAU,EACVC,QACAC,UAAW,UACPI,GAAW,CAAEA,aAGfb,GAAmBQ,aAAiBY,QACtCC,EAAAA,EAAAA,IAAYb,EAAOK,GAGjBZ,GACFA,EAAQO,EAAOK,EACjB,GACC,CAACd,EAAqBC,EAAiBE,EAAkBD,IAGtD6B,GAAqBnB,EAAAA,EAAAA,cAAY,CAACH,EAAYK,KAClD,MAAMkB,EAAWvB,aAAiBY,MAAQZ,EAAQ,IAAIY,MAAMY,OAAOxB,IA2BnE,OAzBAH,EAAc,CACZE,UAAU,EACVC,MAAOuB,EACPtB,UAAW,aACPI,GAAW,CAAEA,aAGfd,GACFG,EAAiB,CACfe,KAAM,QACNC,MAAO,QACPC,QAASY,EAASZ,UAIlBnB,IACFqB,EAAAA,EAAAA,IAAYU,EAAUlB,IAGxBoB,EAAAA,EAAAA,IAASF,EAAUlB,GAEfZ,GACFA,EAAQO,EAAOK,GAGVkB,CAAQ,GACd,CAAChC,EAAqBC,EAAiBE,EAAkBD,IAGtDiC,GAAoBvB,EAAAA,EAAAA,cAAYwB,MACpCC,EACAvB,KAEA,IAEE,OADAH,UACa0B,GACf,CAAE,MAAO5B,GAEP,OADAI,EAAwBJ,EAAOK,GACxB,IACT,IACC,CAACH,EAAYE,IAGVyB,GAAwB1B,EAAAA,EAAAA,cAAYwB,MACxCC,EACAR,EACAf,KAEA,IAEE,OADAH,UACa0B,GACf,CAAE,MAAO5B,GAEP,OADAmB,EAAyBnB,EAAOoB,EAAef,GACxC,IACT,IACC,CAACH,EAAYiB,IAEhB,MAAO,IAEFvB,EAGHW,eAAgBH,EAChBc,sBAAuBJ,EACvBO,gBAAiBF,EACjBG,qBACApB,aAGAwB,oBACAG,wBAGAC,WAAa9B,GACXA,GAA0B,kBAAVA,GAAsB,WAAYA,EACpD+B,kBAAoB/B,GAClBA,GAA0B,kBAAVA,GAAsB,UAAWA,EAEvD,C,8EChNA,MAAMgC,EAA2EC,IAAA,IAAC,MAChFjC,EAAK,WACLkC,GACDD,EAAA,OACCE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yGAAwGC,SAAA,EACrHC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6BAA4BC,SAAC,kBAC5CC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,0CAAyCC,SAAC,0BACxDC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iDAAgDC,SAC1DrC,EAAMW,SAAW,kCAEpB2B,EAAAA,EAAAA,KAAA,UACEC,QAASL,EACTE,UAAU,6EAA4EC,SACvF,gBAGG,EAMD,SAASG,EACdC,GAEC,IADDC,EAA2BtD,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAE/B,MACEuD,SAAUC,EAAoBZ,EAAoB,QAClDvC,EAAO,gBACPD,GAAkB,EAAI,QACtBa,GACEqC,EAEEG,GAAmBC,EAAAA,EAAAA,aAAmB,CAACC,EAAOC,KAiBhDV,EAAAA,EAAAA,KAACW,EAAAA,EAAa,CACZN,UAAUL,EAAAA,EAAAA,KAACM,EAAiB,CAAC5C,MAAO,IAAIY,MAASsB,WAAYA,IAAMgB,OAAOC,SAASC,WACnF3D,QAlBgB4D,CAACrD,EAAcsD,KAE7B9D,IACFqB,EAAAA,EAAAA,IAAYb,EAAOK,GAAWoC,EAAUc,aAAed,EAAUe,KAAM,CACrEC,eAAgBH,EAAUG,eAC1BC,eAAe,IAKfjE,GACFA,EAAQO,EAAOsD,EACjB,EAMuBjB,UAErBC,EAAAA,EAAAA,KAACG,EAAS,IAAMM,EAAeC,IAAKA,QAQ1C,OAFAH,EAAiBU,YAAc,qBAAqBd,EAAUc,aAAed,EAAUe,QAEhFX,CACT,CAKO,MAsBP,G,4CCrGO,MAAMc,EAAa,SAACC,GAA0E,IAAtDzE,EAAmCC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EACpF,IAAKwE,EAAY,MAAO,IAExB,IACE,MAAMC,EAAO,IAAIC,KAAKF,GAGhBG,EAA6C,CACjDC,KAAM,UACNC,MAAO,QACPC,IAAK,aACF/E,GAGL,OAAO,IAAIgF,KAAKC,eAAe,QAASL,GAAgBM,OAAOR,EACjE,CAAE,MAAO7D,GAEP,OADAsE,QAAQtE,MAAM,yBAA0BA,GACjC4D,CACT,CACF,EAkBaW,EAAiB,SAC5BC,GAGY,IAFZC,EAAgBrF,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,MACnBsF,EAActF,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,QAEjB,IACE,OAAO,IAAI+E,KAAKQ,aAAaD,EAAQ,CACnCE,MAAO,WACPH,WACAI,sBAAuB,EACvBC,sBAAuB,IACtBT,OAAOG,EACZ,CAAE,MAAOxE,GAEP,OADAsE,QAAQtE,MAAM,6BAA8BA,GACrC,GAAGyE,KAAYD,EAAOO,QAAQ,IACvC,CACF,C,yGC7CA,MAiDA,EAjDgD9C,IAIzC,IAJ0C,OAC/C+C,EACAvE,KAAMwE,EAAQ,OAAM,UACpB7C,EAAY,IACbH,EAEC,IAAK+C,EACH,OACE1C,EAAAA,EAAAA,KAAA,QAAMF,UAAW,qGAAqGA,IAAYC,SAAC,YAMvI,MAAM6C,EAAYF,EAAOG,cACzB,IAAIC,EAAa,GACbC,EAAO,KAGO,WAAdH,GAAwC,aAAdA,GAA0C,cAAdA,GACxDE,EAAa,8BACbC,GAAO/C,EAAAA,EAAAA,KAACgD,EAAAA,EAAe,CAAClD,UAAU,kBACX,YAAd8C,GAAyC,eAAdA,GACpCE,EAAa,4BACbC,GAAO/C,EAAAA,EAAAA,KAACiD,EAAAA,EAAS,CAACnD,UAAU,kBACL,WAAd8C,GAAwC,aAAdA,GACnCE,EAAa,0BACbC,GAAO/C,EAAAA,EAAAA,KAACkD,EAAAA,EAAW,CAACpD,UAAU,kBACP,YAAd8C,GACTE,EAAa,gCACbC,GAAO/C,EAAAA,EAAAA,KAACmD,EAAAA,EAAS,CAACrD,UAAU,kBACL,YAAd8C,GACTE,EAAa,gCACbC,GAAO/C,EAAAA,EAAAA,KAACoD,EAAAA,EAAqB,CAACtD,UAAU,kBAExCgD,EAAa,4BAIf,MAAMO,EAAkBX,EAASA,EAAOY,OAAO,GAAGC,cAAgBb,EAAOc,MAAM,GAAK,UAEpF,OACE3D,EAAAA,EAAAA,MAAA,QAAMC,UAAW,2EAA2EgD,KAAchD,IAAYC,SAAA,CACnHgD,EACAM,IACI,C,gDC7DX,SAASI,EAAiB9D,EAIvB+D,GAAQ,IAJgB,MACzBtF,EAAK,QACLuF,KACGlD,GACJd,EACC,OAAoBiE,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbzD,IAAKgD,EACL,kBAAmBC,GAClBlD,GAAQrC,EAAqBwF,EAAAA,cAAoB,QAAS,CAC3DQ,GAAIT,GACHvF,GAAS,KAAmBwF,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,iHAEP,CACA,MACA,EADiCX,EAAAA,WAAiBH,E,yDCH3C,MAwBP,EAxB8B9D,IASC,IAT+B,KAC5D6E,EAAI,QACJC,EAAO,WACPC,EAAU,MACVtG,EAAK,WACLuG,GAAa,EAAI,QACjBC,GAAU,EAAK,aACfC,EAAe,oBAAmB,UAClC/E,EAAY,IACWH,EACvB,OACEK,EAAAA,EAAAA,KAAC8E,EAAAA,EAAS,CACRL,QAASA,EACTD,KAAMA,EACNE,WAAYA,EACZtG,MAAOA,EACPuG,WAAYA,EACZC,QAASA,EACTC,aAAcA,EACd/E,UAAWA,GACX,C,qGCxCN,SAASiF,EAAYpF,EAIlB+D,GAAQ,IAJW,MACpBtF,EAAK,QACLuF,KACGlD,GACJd,EACC,OAAoBiE,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbzD,IAAKgD,EACL,kBAAmBC,GAClBlD,GAAQrC,EAAqBwF,EAAAA,cAAoB,QAAS,CAC3DQ,GAAIT,GACHvF,GAAS,KAAmBwF,EAAAA,cAAoB,OAAQ,CACzDS,cAAe,QACfC,eAAgB,QAChBC,EAAG,4MAEP,CACA,MACA,EADiCX,EAAAA,WAAiBmB,G,qDCDlD,MA+RA,GAAe7E,EAAAA,EAAAA,KA/Ra8E,KAC1B,MAAOC,EAAWC,IAAgB1H,EAAAA,EAAAA,UAAsC,QACjE2H,EAAWC,IAAgB5H,EAAAA,EAAAA,WAAS,IACpC6H,EAAoBC,IAAyB9H,EAAAA,EAAAA,WAAS,IACtD+H,EAAcC,IAAmBhI,EAAAA,EAAAA,UAAsB,OACvDiI,EAAwBC,IAA6BlI,EAAAA,EAAAA,WAAS,IAC9DmI,EAAmBC,IAAwBpI,EAAAA,EAAAA,WAAS,IACpDqI,EAAcC,IAAmBtI,EAAAA,EAAAA,UAAsB,OACvDuI,EAAYC,IAAiBxI,EAAAA,EAAAA,WAAS,IAGvC,MACJyI,EACAd,UAAWe,EACXC,aAAcC,EACdC,aAAcC,EAAU,iBACxBC,IACEC,EAAAA,EAAAA,OAGE,mBACJxH,EAAkB,kBAClBI,EAAiB,sBACjBG,EAAqB,WACrB3B,IACE6I,EAAAA,EAAAA,GAAgB,CAClBxJ,qBAAqB,EACrBC,iBAAiB,IAIbwJ,GAAgBC,EAAAA,EAAAA,UAAQ,IACV,QAAd1B,EAA4BgB,EACzBA,EAAMW,QAAOC,GAAQA,EAAKnE,SAAWuC,KAC3C,CAACgB,EAAOhB,IAEL6B,GAAiBjJ,EAAAA,EAAAA,cAAagJ,IAClCrB,EAAgBqB,GAChBnB,GAA0B,EAAK,GAC9B,IAGGqB,GAAkBlJ,EAAAA,EAAAA,cAAagJ,IACnCC,EAAeD,EAAK,GACnB,CAACC,IAQEE,GAAmBnJ,EAAAA,EAAAA,cAAagJ,IACpCf,EAAgBe,GAChBjB,GAAqB,EAAK,GACzB,IAEGqB,GAAoBpJ,EAAAA,EAAAA,cAAYwB,UACpC,IAAKwG,EAAc,OAEnBG,GAAc,GACd,MAAMkB,QAAe9H,GAAkBC,gBAC/BiH,EAAWT,EAAazB,KACvB,IACN,eAAeyB,EAAa3E,QAE/B8E,GAAc,GACVkB,GACFtB,GAAqB,GACrBE,EAAgB,OAEhB9D,QAAQtE,MAAM,wBAChB,GACC,CAACmI,EAAczG,EAAmBkH,IAE/Ba,GAAoBtJ,EAAAA,EAAAA,cAAYwB,UACpC+F,GAAa,GAEb,MAAM8B,QAAeE,EAAAA,EAAAA,KACnB/H,gBAEQ,IAAIgI,SAAQ,CAACC,EAASC,KAC1BC,YAAW,KACLC,KAAKC,SAAW,GAClBH,EAAO,IAAIjJ,MAAM,kBAEjBgJ,GAAQ,EACV,GACC,KAAK,IAGVtF,QAAQ2F,IAAI,uBACL,IAET,CACEC,QAAS,IACTC,QAAS,EACTC,cAAe,iBAIdZ,EAAOa,SACV/I,EAAmBkI,EAAOxJ,MAAO,gBAGnC0H,GAAa,EAAM,GAClB,CAACpG,IAEEgJ,GAAgBnK,EAAAA,EAAAA,cAAYwB,UAChC+F,GAAa,GACbxH,IAEA,MAAMsJ,QAAe3H,GAAsBF,UACzC,MAAM4I,QAAgB7B,EAAW8B,GAEjC,OADA5C,GAAsB,GACf2C,CAAO,QACbjL,EAAW,YAEdoI,GAAa,GAET8B,GACFlF,QAAQ2F,IAAI,2BAA4BT,EAC1C,GACC,CAAC3H,EAAuB6G,EAAYxI,IAEjCuK,GAAyBtK,EAAAA,EAAAA,cAAYwB,UACzC,MAAMwH,EAAOZ,EAAMmC,MAAKC,GAAKA,EAAEjE,KAAOkE,IACtC,IAAKzB,EAAM,OAEX,MAAM0B,EAA4B,WAAhB1B,EAAKnE,OAAsB,SAAW,eAEnCtD,GAAkBC,gBAC/BkH,EAAiB+B,EAAQC,GAG3BhD,GAAgBA,EAAanB,KAAOkE,GACtC9C,EAAgB,IAAKD,EAAc7C,OAAQ6F,IAG7C7C,GAA0B,IACnB,IACN,0BAA0BmB,EAAK3F,SAGhCc,QAAQtE,MAAM,+BAChB,GACC,CAACuI,EAAO7G,EAAmBmH,EAAkBhB,IAEhD,OACE1F,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8EAA6EC,SAAA,EAC1FF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mCAAkCC,SAAC,WACjDC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BAA4BC,SAAC,gDAE5CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uBAAsBC,SAAA,EACnCC,EAAAA,EAAAA,KAACwI,EAAAA,EAAM,CACLC,QAAQ,UACR1F,MAAM/C,EAAAA,EAAAA,KAACyD,EAAAA,EAAiB,CAAC3D,UAAU,YACnCG,QAASkH,EACTvC,QAASO,GAAae,EAAanG,SACpC,kBAGDC,EAAAA,EAAAA,KAACwI,EAAAA,EAAM,CACLzF,MAAM/C,EAAAA,EAAAA,KAAC+E,EAAY,CAACjF,UAAU,YAC9BG,QAASA,IAAMqF,GAAsB,GAAMvF,SAC5C,oBAMLF,EAAAA,EAAAA,MAAC6I,EAAAA,EAAI,CAAA3I,SAAA,EACHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4BAA2BC,SAAA,EACxCC,EAAAA,EAAAA,KAACwI,EAAAA,EAAM,CACLC,QAAuB,QAAdxD,EAAsB,UAAY,UAC3C0D,KAAK,KACL1I,QAASA,IAAMiF,EAAa,OAAOnF,SACpC,eAGDC,EAAAA,EAAAA,KAACwI,EAAAA,EAAM,CACLC,QAAuB,WAAdxD,EAAyB,UAAY,UAC9C0D,KAAK,KACL1I,QAASA,IAAMiF,EAAa,UAAUnF,SACvC,kBAGDC,EAAAA,EAAAA,KAACwI,EAAAA,EAAM,CACLC,QAAuB,WAAdxD,EAAyB,UAAY,UAC9C0D,KAAK,KACL1I,QAASA,IAAMiF,EAAa,UAAUnF,SACvC,qBAKHC,EAAAA,EAAAA,KAAC4I,EAAAA,GAAQ,CACP3C,MAAOS,EACPmC,WAAY/B,EACZgC,WAzJgBjC,IACtB7E,QAAQ2F,IAAI,0BAA2Bd,EAAK,EAyJtCkC,aAAc/B,EACdgC,YAAajC,EACb3I,MAAO,GAAG6G,EAAU3B,OAAO,GAAGC,cAAgB0B,EAAUzB,MAAM,OAAOkD,EAAc3J,UACnF6H,QAASsB,QAKblG,EAAAA,EAAAA,KAACiJ,EAAAA,EAAK,CACJC,OAAQ7D,EACR8D,QAASA,IAAM7D,GAAsB,GACrClH,MAAM,eACNuK,KAAK,KAAI5I,UAETC,EAAAA,EAAAA,KAACoJ,EAAAA,GAAW,CACVC,SAAUrB,EACVsB,SAAUA,IAAMhE,GAAsB,GACtCH,UAAWA,MAKdI,IACCvF,EAAAA,EAAAA,KAACiJ,EAAAA,EAAK,CACJC,OAAQzD,EACR0D,QAASA,IAAMzD,GAA0B,GACzCtH,MAAM,eACNuK,KAAK,KACLY,QACE1J,EAAAA,EAAAA,MAAA2J,EAAAA,SAAA,CAAAzJ,SAAA,EACEC,EAAAA,EAAAA,KAACwI,EAAAA,EAAM,CACLC,QAAQ,UACRxI,QAASA,IAAMyF,GAA0B,GAAO3F,SACjD,WAGDC,EAAAA,EAAAA,KAACwI,EAAAA,EAAM,CACLC,QAAiC,WAAxBlD,EAAa7C,OAAsB,SAAW,UACvDzC,QAASA,IAAMkI,EAAuB5C,EAAanB,IAAIrE,SAE9B,WAAxBwF,EAAa7C,OAAsB,WAAa,qBAGtD3C,UAEDC,EAAAA,EAAAA,KAACyJ,EAAAA,GAAgB,CAAC5C,KAAMtB,MAK3BM,IACC7F,EAAAA,EAAAA,KAACiJ,EAAAA,EAAK,CACJC,OAAQvD,EACRwD,QAASA,IAAMvD,GAAqB,GACpCxH,MAAM,cACNuK,KAAK,KACLY,QACE1J,EAAAA,EAAAA,MAAA2J,EAAAA,SAAA,CAAAzJ,SAAA,EACEC,EAAAA,EAAAA,KAACwI,EAAAA,EAAM,CACLC,QAAQ,UACRxI,QAASA,IAAM2F,GAAqB,GACpC8D,SAAU3D,EAAWhG,SACtB,YAGDC,EAAAA,EAAAA,KAACwI,EAAAA,EAAM,CACLC,QAAQ,SACRxI,QAASgH,EACTrC,QAASmB,EAAWhG,SACrB,mBAIJA,UAEDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,CAAC,oCACH8F,EAAa3E,KAAK,0CAItD,GAKkC,CAC1Cb,SAAUV,IAAA,IAAC,MAAEjC,EAAK,WAAEkC,GAAYD,EAAA,OAC9BE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8DAA6DC,SAAA,EAC1EC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6BAA4BC,SAAC,kBAC5CC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6BAA4BC,SAAC,sBAC3CC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0CAAyCC,SACnDrC,EAAMW,SAAW,oDAEpB2B,EAAAA,EAAAA,KAAA,UACEC,QAASL,EACTE,UAAU,kFAAiFC,SAC5F,kBAGG,EAERhC,QAAS,a,6ECxSX,MAAMkL,EAA8BtJ,IAiB7B,IAjB8B,OACnCuJ,EAAM,QACNC,EAAO,MACP/K,EAAK,SACL2B,EAAQ,KACR4I,EAAO,KAAI,OACXY,EAAM,WACNI,GAAa,EAAI,qBACjBC,GAAuB,EAAI,gBAC3BC,GAAkB,EAAI,SACtBC,GAAW,EAAI,UACfhK,EAAY,GAAE,cACdiK,EAAgB,GAAE,gBAClBC,EAAkB,GAAE,gBACpBC,EAAkB,GAAE,kBACpBC,EAAoB,GAAE,OACtBC,GACDxK,EACC,MAAMyK,GAAWC,EAAAA,EAAAA,QAAuB,MA2DxC,IAxDAC,EAAAA,EAAAA,YAAU,KACR,MAAMC,EAAgBC,IAChBb,GAAwB,WAAVa,EAAEC,KAClBtB,GACF,EASF,OANID,IACFwB,SAASC,iBAAiB,UAAWJ,GAErCG,SAASE,KAAKtI,MAAMuI,SAAW,UAG1B,KACLH,SAASI,oBAAoB,UAAWP,GACxCG,SAASE,KAAKtI,MAAMuI,SAAW,MAAM,CACtC,GACA,CAAC3B,EAAQC,EAASQ,KAGrBW,EAAAA,EAAAA,YAAU,KACR,IAAKpB,IAAWkB,EAASW,QAAS,OAElC,MAAMC,EAAoBZ,EAASW,QAAQE,iBACzC,4EAGF,GAAiC,IAA7BD,EAAkBjO,OAAc,OAEpC,MAAMmO,EAAeF,EAAkB,GACjCG,EAAcH,EAAkBA,EAAkBjO,OAAS,GAE3DqO,EAAgBZ,IACN,QAAVA,EAAEC,MAEFD,EAAEa,SACAX,SAASY,gBAAkBJ,IAC7BC,EAAYI,QACZf,EAAEgB,kBAGAd,SAASY,gBAAkBH,IAC7BD,EAAaK,QACbf,EAAEgB,kBAEN,EAMF,OAHAd,SAASC,iBAAiB,UAAWS,GACrCF,EAAaK,QAEN,KACLb,SAASI,oBAAoB,UAAWM,EAAa,CACtD,GACA,CAAClC,KAECA,EAAQ,OAAO,KAGpB,MAUMuC,GACJ5L,EAAAA,EAAAA,MAAC6L,EAAAA,SAAQ,CAAA3L,SAAA,EAEPC,EAAAA,EAAAA,KAAA,OACEF,UAAW,gEAAgEoK,IAC3EjK,QAAS2J,EAAuBT,OAAUnM,EAC1C,cAAa,GAAGmN,gBAIlBnK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qCAAoCC,UACjDC,EAAAA,EAAAA,KAAA,OAAKF,UAAW,yBAAyBgK,EAAW,SAAW,yCAAyC/J,UACtGF,EAAAA,EAAAA,MAAA,OACEa,IAAK0J,EACLtK,UAAW,GAxBD,CAClB6L,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,YACJC,GAAI,YACJC,KAAM,mBAkB4BrD,2GAA8G7I,IACxIG,QAAUuK,GAAMA,EAAEyB,kBAClB,cAAa9B,EAAOpK,SAAA,EAGpBF,EAAAA,EAAAA,MAAA,OAAKC,UAAW,wEAAwEkK,IAAkBjK,SAAA,CACtF,kBAAV3B,GACN4B,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sCAAqCC,SAAE3B,IAErDA,EAEDyL,IACC7J,EAAAA,EAAAA,KAAA,UACE7B,KAAK,SACL2B,UAAU,wGACVG,QAASkJ,EACT,aAAW,cACX,cAAa,GAAGgB,iBAAsBpK,UAEtCC,EAAAA,EAAAA,KAACkM,EAAAA,EAAS,CAACpM,UAAU,kBAM3BE,EAAAA,EAAAA,KAAA,OAAKF,UAAW,aAAaiK,IAAgBhK,SAC1CA,IAIFwJ,IACCvJ,EAAAA,EAAAA,KAAA,OAAKF,UAAW,4EAA4EmK,IAAkBlK,SAC3GwJ,cAUf,OAAO4C,EAAAA,EAAAA,cAAaV,EAAcf,SAASE,KAAK,EAGlD,GAAewB,EAAAA,EAAAA,MAAKnD,E", "sources": ["hooks/useErrorHandler.ts", "components/common/withErrorBoundary.tsx", "utils/formatters.ts", "components/common/StatusBadge.tsx", "../node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js", "components/common/EntityList/BaseEntityList.tsx", "../node_modules/@heroicons/react/24/outline/esm/UserPlusIcon.js", "pages/UsersPage.tsx", "components/common/Modal.tsx"], "sourcesContent": ["/**\r\n * Error <PERSON>ler Hook\r\n * \r\n * This hook provides React-specific error handling utilities and state management.\r\n */\r\n\r\nimport { useState, useCallback } from 'react';\r\nimport { \r\n  handleApiError, \r\n  handleValidationError, \r\n  handleFormError,\r\n  logError,\r\n  reportError,\r\n  type ApiError,\r\n  type ValidationError \r\n} from '../utils/errorHandling';\r\nimport useNotification from './useNotification';\r\n\r\ninterface ErrorState {\r\n  hasError: boolean;\r\n  error: Error | ApiError | ValidationError | null;\r\n  errorType: 'api' | 'validation' | 'form' | 'general' | null;\r\n  context?: string;\r\n}\r\n\r\ninterface UseErrorHandlerOptions {\r\n  enableNotifications?: boolean;\r\n  enableReporting?: boolean;\r\n  onError?: (error: any, context?: string) => void;\r\n}\r\n\r\nexport const useErrorHandler = (options: UseErrorHandlerOptions = {}) => {\r\n  const { enableNotifications = true, enableReporting = true, onError } = options;\r\n  const { showNotification } = useNotification();\r\n  \r\n  const [errorState, setErrorState] = useState<ErrorState>({\r\n    hasError: false,\r\n    error: null,\r\n    errorType: null\r\n  });\r\n\r\n  // Clear error state\r\n  const clearError = useCallback(() => {\r\n    setErrorState({\r\n      hasError: false,\r\n      error: null,\r\n      errorType: null\r\n    });\r\n  }, []);\r\n\r\n  // Handle API errors\r\n  const handleApiErrorWithState = useCallback((error: any, context?: string) => {\r\n    const apiError = handleApiError(\r\n      error,\r\n      enableNotifications ? (notification: { type: string; title: string; message: string }) => {\r\n        showNotification({\r\n          type: notification.type as 'error' | 'success' | 'warning' | 'info',\r\n          title: notification.title,\r\n          message: notification.message\r\n        });\r\n      } : undefined\r\n    );\r\n\r\n    setErrorState({\r\n      hasError: true,\r\n      error: apiError,\r\n      errorType: 'api',\r\n      ...(context && { context })\r\n    });\r\n\r\n    if (enableReporting && error instanceof Error) {\r\n      reportError(error, context);\r\n    }\r\n\r\n    if (onError) {\r\n      onError(error, context);\r\n    }\r\n\r\n    return apiError;\r\n  }, [enableNotifications, enableReporting, showNotification, onError]);\r\n\r\n  // Handle validation errors\r\n  const handleValidationErrorWithState = useCallback((\r\n    field: string,\r\n    message: string,\r\n    code?: string,\r\n    context?: string\r\n  ) => {\r\n    const validationError = handleValidationError(field, message, code);\r\n\r\n    setErrorState({\r\n      hasError: true,\r\n      error: validationError,\r\n      errorType: 'validation',\r\n      ...(context && { context })\r\n    });\r\n\r\n    if (enableNotifications) {\r\n      showNotification({\r\n        type: 'error',\r\n        title: 'Validation Error',\r\n        message: validationError.message\r\n      });\r\n    }\r\n\r\n    if (onError) {\r\n      onError(validationError, context);\r\n    }\r\n\r\n    return validationError;\r\n  }, [enableNotifications, showNotification, onError]);\r\n\r\n  // Handle form errors\r\n  const handleFormErrorWithState = useCallback((\r\n    error: any,\r\n    setFieldError?: (field: string, message: string) => void,\r\n    context?: string\r\n  ) => {\r\n    handleFormError(\r\n      error,\r\n      setFieldError,\r\n      enableNotifications ? (notification: { type: string; title: string; message: string }) => {\r\n        showNotification({\r\n          type: notification.type as 'error' | 'success' | 'warning' | 'info',\r\n          title: notification.title,\r\n          message: notification.message\r\n        });\r\n      } : undefined\r\n    );\r\n\r\n    setErrorState({\r\n      hasError: true,\r\n      error,\r\n      errorType: 'form',\r\n      ...(context && { context })\r\n    });\r\n\r\n    if (enableReporting && error instanceof Error) {\r\n      reportError(error, context);\r\n    }\r\n\r\n    if (onError) {\r\n      onError(error, context);\r\n    }\r\n  }, [enableNotifications, enableReporting, showNotification, onError]);\r\n\r\n  // Handle general errors\r\n  const handleGeneralError = useCallback((error: any, context?: string) => {\r\n    const errorObj = error instanceof Error ? error : new Error(String(error));\r\n\r\n    setErrorState({\r\n      hasError: true,\r\n      error: errorObj,\r\n      errorType: 'general',\r\n      ...(context && { context })\r\n    });\r\n\r\n    if (enableNotifications) {\r\n      showNotification({\r\n        type: 'error',\r\n        title: 'Error',\r\n        message: errorObj.message\r\n      });\r\n    }\r\n\r\n    if (enableReporting) {\r\n      reportError(errorObj, context);\r\n    }\r\n\r\n    logError(errorObj, context);\r\n\r\n    if (onError) {\r\n      onError(error, context);\r\n    }\r\n\r\n    return errorObj;\r\n  }, [enableNotifications, enableReporting, showNotification, onError]);\r\n\r\n  // Async operation wrapper with error handling\r\n  const withErrorHandling = useCallback(async <T>(\r\n    operation: () => Promise<T>,\r\n    context?: string\r\n  ): Promise<T | null> => {\r\n    try {\r\n      clearError();\r\n      return await operation();\r\n    } catch (error) {\r\n      handleApiErrorWithState(error, context);\r\n      return null;\r\n    }\r\n  }, [clearError, handleApiErrorWithState]);\r\n\r\n  // Form submission wrapper with error handling\r\n  const withFormErrorHandling = useCallback(async <T>(\r\n    operation: () => Promise<T>,\r\n    setFieldError?: (field: string, message: string) => void,\r\n    context?: string\r\n  ): Promise<T | null> => {\r\n    try {\r\n      clearError();\r\n      return await operation();\r\n    } catch (error) {\r\n      handleFormErrorWithState(error, setFieldError, context);\r\n      return null;\r\n    }\r\n  }, [clearError, handleFormErrorWithState]);\r\n\r\n  return {\r\n    // Error state\r\n    ...errorState,\r\n    \r\n    // Error handlers\r\n    handleApiError: handleApiErrorWithState,\r\n    handleValidationError: handleValidationErrorWithState,\r\n    handleFormError: handleFormErrorWithState,\r\n    handleGeneralError,\r\n    clearError,\r\n    \r\n    // Wrapper functions\r\n    withErrorHandling,\r\n    withFormErrorHandling,\r\n    \r\n    // Utility functions\r\n    isApiError: (error: any): error is ApiError => \r\n      error && typeof error === 'object' && 'status' in error,\r\n    isValidationError: (error: any): error is ValidationError => \r\n      error && typeof error === 'object' && 'field' in error,\r\n  };\r\n};\r\n\r\nexport default useErrorHandler;\r\n", "/**\r\n * Higher-Order Component for Error Boundary\r\n * \r\n * This HOC wraps components with an error boundary to catch and handle errors gracefully.\r\n */\r\n\r\nimport React, { ComponentType, forwardRef } from 'react';\r\nimport ErrorBoundary from './ErrorBoundary';\r\nimport { reportError } from '../../utils/errorHandling';\r\n\r\ninterface ErrorBoundaryConfig {\r\n  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>;\r\n  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;\r\n  enableReporting?: boolean;\r\n  context?: string;\r\n}\r\n\r\n/**\r\n * Default error fallback component\r\n */\r\nconst DefaultErrorFallback: React.FC<{ error: Error; resetError: () => void }> = ({ \r\n  error, \r\n  resetError \r\n}) => (\r\n  <div className=\"flex flex-col items-center justify-center min-h-[200px] p-4 border border-red-200 rounded-lg bg-red-50\">\r\n    <div className=\"text-red-500 text-2xl mb-2\">⚠️</div>\r\n    <h3 className=\"text-lg font-semibold text-red-800 mb-2\">Something went wrong</h3>\r\n    <p className=\"text-red-600 text-sm mb-4 text-center max-w-md\">\r\n      {error.message || 'An unexpected error occurred'}\r\n    </p>\r\n    <button\r\n      onClick={resetError}\r\n      className=\"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors\"\r\n    >\r\n      Try Again\r\n    </button>\r\n  </div>\r\n);\r\n\r\n/**\r\n * Higher-order component that wraps a component with an error boundary\r\n */\r\nexport function withErrorBoundary<P extends object>(\r\n  Component: ComponentType<P>,\r\n  config: ErrorBoundaryConfig = {}\r\n) {\r\n  const {\r\n    fallback: FallbackComponent = DefaultErrorFallback,\r\n    onError,\r\n    enableReporting = true,\r\n    context\r\n  } = config;\r\n\r\n  const WrappedComponent = forwardRef<any, P>((props, ref) => {\r\n    const handleError = (error: Error, errorInfo: React.ErrorInfo) => {\r\n      // Report error if enabled\r\n      if (enableReporting) {\r\n        reportError(error, context || Component.displayName || Component.name, {\r\n          componentStack: errorInfo.componentStack,\r\n          errorBoundary: true\r\n        });\r\n      }\r\n\r\n      // Call custom error handler if provided\r\n      if (onError) {\r\n        onError(error, errorInfo);\r\n      }\r\n    };\r\n\r\n    return (\r\n      <ErrorBoundary\r\n        fallback={<FallbackComponent error={new Error()} resetError={() => window.location.reload()} />}\r\n        onError={handleError}\r\n      >\r\n        <Component {...(props as any)} ref={ref} />\r\n      </ErrorBoundary>\r\n    );\r\n  });\r\n\r\n  // Set display name for debugging\r\n  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;\r\n\r\n  return WrappedComponent;\r\n}\r\n\r\n/**\r\n * Hook for creating error boundary configuration\r\n */\r\nexport const useErrorBoundaryConfig = (\r\n  onError?: (error: Error, errorInfo: React.ErrorInfo) => void,\r\n  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>,\r\n  context?: string\r\n): ErrorBoundaryConfig => {\r\n  return {\r\n    ...(onError && { onError }),\r\n    ...(fallback && { fallback }),\r\n    ...(context && { context }),\r\n    enableReporting: true\r\n  };\r\n};\r\n\r\n/**\r\n * Decorator for class components\r\n */\r\nexport const errorBoundary = (config: ErrorBoundaryConfig = {}) => {\r\n  return <P extends object>(Component: ComponentType<P>) => {\r\n    return withErrorBoundary(Component, config);\r\n  };\r\n};\r\n\r\nexport default withErrorBoundary;\r\n", "/**\r\n * Formatters\r\n * \r\n * This file contains utility functions for formatting data.\r\n */\r\n\r\n/**\r\n * Format a date string to a human-readable format\r\n */\r\nexport const formatDate = (dateString: string, options: Intl.DateTimeFormatOptions = {}): string => {\r\n  if (!dateString) return '-';\r\n  \r\n  try {\r\n    const date = new Date(dateString);\r\n    \r\n    // Default options\r\n    const defaultOptions: Intl.DateTimeFormatOptions = {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      ...options\r\n    };\r\n    \r\n    return new Intl.DateTimeFormat('en-US', defaultOptions).format(date);\r\n  } catch (error) {\r\n    console.error('Error formatting date:', error);\r\n    return dateString;\r\n  }\r\n};\r\n\r\n/**\r\n * Format a date string to include time\r\n */\r\nexport const formatDateTime = (dateString: string): string => {\r\n  return formatDate(dateString, {\r\n    year: 'numeric',\r\n    month: 'short',\r\n    day: 'numeric',\r\n    hour: '2-digit',\r\n    minute: '2-digit'\r\n  });\r\n};\r\n\r\n/**\r\n * Format a number as currency\r\n */\r\nexport const formatCurrency = (\r\n  amount: number,\r\n  currency: string = 'USD',\r\n  locale: string = 'en-US'\r\n): string => {\r\n  try {\r\n    return new Intl.NumberFormat(locale, {\r\n      style: 'currency',\r\n      currency,\r\n      minimumFractionDigits: 2,\r\n      maximumFractionDigits: 2\r\n    }).format(amount);\r\n  } catch (error) {\r\n    console.error('Error formatting currency:', error);\r\n    return `${currency} ${amount.toFixed(2)}`;\r\n  }\r\n};\r\n\r\n/**\r\n * Format a number with commas\r\n */\r\nexport const formatNumber = (\r\n  number: number,\r\n  options: Intl.NumberFormatOptions = {}\r\n): string => {\r\n  try {\r\n    return new Intl.NumberFormat('en-US', options).format(number);\r\n  } catch (error) {\r\n    console.error('Error formatting number:', error);\r\n    return number.toString();\r\n  }\r\n};\r\n\r\n/**\r\n * Format a phone number\r\n */\r\nexport const formatPhoneNumber = (phoneNumber: string): string => {\r\n  if (!phoneNumber) return '-';\r\n  \r\n  // Remove all non-numeric characters\r\n  const cleaned = phoneNumber.replace(/\\D/g, '');\r\n  \r\n  // Format based on length\r\n  if (cleaned.length === 10) {\r\n    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;\r\n  } else if (cleaned.length === 11 && cleaned.startsWith('1')) {\r\n    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;\r\n  }\r\n  \r\n  // If it doesn't match expected formats, return as is\r\n  return phoneNumber;\r\n};\r\n\r\n/**\r\n * Truncate text with ellipsis\r\n */\r\nexport const truncateText = (text: string, maxLength: number): string => {\r\n  if (!text) return '';\r\n  if (text.length <= maxLength) return text;\r\n  \r\n  return `${text.slice(0, maxLength)}...`;\r\n};\r\n\r\n/**\r\n * Format file size\r\n */\r\nexport const formatFileSize = (bytes: number): string => {\r\n  if (bytes === 0) return '0 Bytes';\r\n  \r\n  const k = 1024;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n  \r\n  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;\r\n};\r\n\r\n/**\r\n * Format a duration in milliseconds to a human-readable format\r\n */\r\nexport const formatDuration = (milliseconds: number): string => {\r\n  const seconds = Math.floor(milliseconds / 1000);\r\n  const minutes = Math.floor(seconds / 60);\r\n  const hours = Math.floor(minutes / 60);\r\n  const days = Math.floor(hours / 24);\r\n  \r\n  if (days > 0) {\r\n    return `${days} day${days > 1 ? 's' : ''}`;\r\n  } else if (hours > 0) {\r\n    return `${hours} hour${hours > 1 ? 's' : ''}`;\r\n  } else if (minutes > 0) {\r\n    return `${minutes} minute${minutes > 1 ? 's' : ''}`;\r\n  } else {\r\n    return `${seconds} second${seconds !== 1 ? 's' : ''}`;\r\n  }\r\n};\r\n", "import React from 'react';\r\nimport {\r\n  CheckCircleIcon,\r\n  XCircleIcon,\r\n  ClockIcon,\r\n  TruckIcon,\r\n  ExclamationCircleIcon\r\n} from '@heroicons/react/24/outline';\r\n\r\nexport type StatusType = 'user' | 'supplier' | 'order' | 'verification' | 'category';\r\n\r\ninterface StatusBadgeProps {\r\n  status: string;\r\n  type?: StatusType;\r\n  className?: string;\r\n}\r\n\r\nconst StatusBadge: React.FC<StatusBadgeProps> = ({\r\n  status,\r\n  type: _type = 'user',\r\n  className = ''\r\n}) => {\r\n  // Handle undefined or null status\r\n  if (!status) {\r\n    return (\r\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 ${className}`}>\r\n        Unknown\r\n      </span>\r\n    );\r\n  }\r\n\r\n  const statusKey = status.toLowerCase();\r\n  let colorClass = '';\r\n  let icon = null;\r\n  \r\n  // Common statuses across entity types\r\n  if (statusKey === 'active' || statusKey === 'verified' || statusKey === 'completed') {\r\n    colorClass = 'bg-green-100 text-green-800';\r\n    icon = <CheckCircleIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'pending' || statusKey === 'processing') {\r\n    colorClass = 'bg-blue-100 text-blue-800';\r\n    icon = <ClockIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'banned' || statusKey === 'rejected') {\r\n    colorClass = 'bg-red-100 text-red-800';\r\n    icon = <XCircleIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'shipped') {\r\n    colorClass = 'bg-purple-100 text-purple-800';\r\n    icon = <TruckIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else if (statusKey === 'warning') {\r\n    colorClass = 'bg-yellow-100 text-yellow-800';\r\n    icon = <ExclamationCircleIcon className=\"w-4 h-4 mr-1\" />;\r\n  } else {\r\n    colorClass = 'bg-gray-100 text-gray-800';\r\n  }\r\n  \r\n  // Format the status text (capitalize first letter)\r\n  const formattedStatus = status ? status.charAt(0).toUpperCase() + status.slice(1) : 'Unknown';\r\n  \r\n  return (\r\n    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass} ${className}`}>\r\n      {icon}\r\n      {formattedStatus}\r\n    </span>\r\n  );\r\n};\r\n\r\nexport default StatusBadge;\r\n", "import * as React from \"react\";\nfunction ArrowDownTrayIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ArrowDownTrayIcon);\nexport default ForwardRef;", "/**\r\n * BaseEntityList Component\r\n *\r\n * A reusable list component for displaying entities in a data table.\r\n * This component is generic and can be used for any entity type.\r\n */\r\n\r\nimport DataTable from '../DataTable';\r\nimport type { Column } from '../DataTable';\r\n\r\nexport interface BaseEntityListProps<T> {\r\n  data: T[];\r\n  columns: Column<T>[];\r\n  onRowClick?: (entity: T) => void;\r\n  title?: string;\r\n  pagination?: boolean;\r\n  loading?: boolean;\r\n  emptyMessage?: string;\r\n  className?: string;\r\n}\r\n\r\nexport const BaseEntityList = <T extends Record<string, any>>({\r\n  data,\r\n  columns,\r\n  onRowClick,\r\n  title,\r\n  pagination = true,\r\n  loading = false,\r\n  emptyMessage = 'No data available',\r\n  className = ''\r\n}: BaseEntityListProps<T>) => {\r\n  return (\r\n    <DataTable<T>\r\n      columns={columns}\r\n      data={data}\r\n      onRowClick={onRowClick}\r\n      title={title}\r\n      pagination={pagination}\r\n      loading={loading}\r\n      emptyMessage={emptyMessage}\r\n      className={className}\r\n    />\r\n  );\r\n};\r\n\r\nexport default BaseEntityList;", "import * as React from \"react\";\nfunction UserPlusIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M18 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0ZM3 19.235v-.11a6.375 6.375 0 0 1 12.75 0v.109A12.318 12.318 0 0 1 9.374 21c-2.331 0-4.512-.645-6.374-1.766Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(UserPlusIcon);\nexport default ForwardRef;", "/**\r\n * Users Page\r\n *\r\n * This page displays and manages users in the system.\r\n */\r\n\r\nimport React, { useState, useMemo, useCallback } from 'react';\r\nimport Button from '../components/common/Button';\r\nimport Card from '../components/common/Card';\r\nimport Modal from '../components/common/Modal';\r\nimport { UserPlusIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline';\r\nimport {\r\n  AddUserForm,\r\n  UserDetailsModal,\r\n  UserList,\r\n  useUsers,\r\n  type User,\r\n  type UserFormDataFrontend\r\n} from '../features/users/index';\r\nimport useErrorHandler from '../hooks/useErrorHandler';\r\nimport { safeAsyncOperation } from '../utils/errorHandling';\r\nimport withErrorBoundary from '../components/common/withErrorBoundary';\r\n\r\nconst UsersPage: React.FC = () => {\r\n  const [activeTab, setActiveTab] = useState<'all' | 'active' | 'banned'>('all');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isAddUserModalOpen, setIsAddUserModalOpen] = useState(false);\r\n  const [selectedUser, setSelectedUser] = useState<User | null>(null);\r\n  const [isUserDetailsModalOpen, setIsUserDetailsModalOpen] = useState(false);\r\n  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);\r\n  const [userToDelete, setUserToDelete] = useState<User | null>(null);\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n\r\n  // Use users feature hook\r\n  const {\r\n    users,\r\n    isLoading: usersLoading,\r\n    createEntity: createUser,\r\n    deleteEntity: deleteUser,\r\n    updateUserStatus\r\n  } = useUsers();\r\n\r\n  // Error handling\r\n  const {\r\n    handleGeneralError,\r\n    withErrorHandling,\r\n    withFormErrorHandling,\r\n    clearError\r\n  } = useErrorHandler({\r\n    enableNotifications: true,\r\n    enableReporting: true\r\n  });\r\n\r\n  // Memoize filtered users to prevent unnecessary recalculations\r\n  const filteredUsers = useMemo(() => {\r\n    if (activeTab === 'all') return users;\r\n    return users.filter(user => user.status === activeTab);\r\n  }, [users, activeTab]);\r\n\r\n  const handleViewUser = useCallback((user: User) => {\r\n    setSelectedUser(user);\r\n    setIsUserDetailsModalOpen(true);\r\n  }, []);\r\n\r\n  // Memoize event handlers to prevent unnecessary re-renders\r\n  const handleUserClick = useCallback((user: User) => {\r\n    handleViewUser(user);\r\n  }, [handleViewUser]);\r\n\r\n  // Note: Edit functionality is now handled directly in the UserList component\r\n  // This is kept for backward compatibility\r\n  const handleEditUser = (user: User) => {\r\n    console.log('Edit user (deprecated):', user);\r\n  };\r\n\r\n  const handleDeleteUser = useCallback((user: User) => {\r\n    setUserToDelete(user);\r\n    setIsDeleteModalOpen(true);\r\n  }, []);\r\n\r\n  const confirmDeleteUser = useCallback(async () => {\r\n    if (!userToDelete) return;\r\n\r\n    setIsDeleting(true);\r\n    const result = await withErrorHandling(async () => {\r\n      await deleteUser(userToDelete.id);\r\n      return true;\r\n    }, `Delete user ${userToDelete.name}`);\r\n\r\n    setIsDeleting(false);\r\n    if (result) {\r\n      setIsDeleteModalOpen(false);\r\n      setUserToDelete(null);\r\n    } else {\r\n      console.error('Failed to delete user');\r\n    }\r\n  }, [userToDelete, withErrorHandling, deleteUser]);\r\n\r\n  const handleExportUsers = useCallback(async () => {\r\n    setIsLoading(true);\r\n\r\n    const result = await safeAsyncOperation(\r\n      async () => {\r\n        // Simulate export process with potential failure\r\n        await new Promise((resolve, reject) => {\r\n          setTimeout(() => {\r\n            if (Math.random() < 0.1) {\r\n              reject(new Error('Export failed'));\r\n            } else {\r\n              resolve(true);\r\n            }\r\n          }, 1500);\r\n        });\r\n\r\n        console.log('Exporting users...');\r\n        return true;\r\n      },\r\n      {\r\n        timeout: 5000,\r\n        retries: 2,\r\n        operationName: 'Export Users'\r\n      }\r\n    );\r\n\r\n    if (!result.success) {\r\n      handleGeneralError(result.error, 'Export Users');\r\n    }\r\n\r\n    setIsLoading(false);\r\n  }, [handleGeneralError]);\r\n\r\n  const handleAddUser = useCallback(async (userData: UserFormDataFrontend) => {\r\n    setIsLoading(true);\r\n    clearError();\r\n\r\n    const result = await withFormErrorHandling(async () => {\r\n      const newUser = await createUser(userData);\r\n      setIsAddUserModalOpen(false);\r\n      return newUser;\r\n    }, undefined, 'Add User');\r\n\r\n    setIsLoading(false);\r\n\r\n    if (result) {\r\n      console.log('User added successfully:', result);\r\n    }\r\n  }, [withFormErrorHandling, createUser, clearError]);\r\n\r\n  const handleToggleUserStatus = useCallback(async (userId: string) => {\r\n    const user = users.find(u => u.id === userId);\r\n    if (!user) return;\r\n\r\n    const newStatus = user.status === 'active' ? 'banned' : 'active';\r\n\r\n    const result = await withErrorHandling(async () => {\r\n      await updateUserStatus(userId, newStatus);\r\n\r\n      // Update the selectedUser state if it's the same user\r\n      if (selectedUser && selectedUser.id === userId) {\r\n        setSelectedUser({ ...selectedUser, status: newStatus });\r\n      }\r\n\r\n      setIsUserDetailsModalOpen(false);\r\n      return true;\r\n    }, `Toggle status for user ${user.name}`);\r\n\r\n    if (!result) {\r\n      console.error('Failed to toggle user status');\r\n    }\r\n  }, [users, withErrorHandling, updateUserStatus, selectedUser]);\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n        <div>\r\n          <h1 className=\"text-2xl font-bold text-gray-800\">Users</h1>\r\n          <p className=\"mt-1 text-sm text-gray-500\">Manage your users and their permissions</p>\r\n        </div>\r\n        <div className=\"flex flex-wrap gap-3\">\r\n          <Button\r\n            variant=\"outline\"\r\n            icon={<ArrowDownTrayIcon className=\"h-5 w-5\" />}\r\n            onClick={handleExportUsers}\r\n            loading={isLoading || usersLoading}\r\n          >\r\n            Export Users\r\n          </Button>\r\n          <Button\r\n            icon={<UserPlusIcon className=\"h-5 w-5\" />}\r\n            onClick={() => setIsAddUserModalOpen(true)}\r\n          >\r\n            Add User\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      <Card>\r\n        <div className=\"flex flex-wrap gap-3 mb-6\">\r\n          <Button\r\n            variant={activeTab === 'all' ? 'primary' : 'outline'}\r\n            size=\"sm\"\r\n            onClick={() => setActiveTab('all')}\r\n          >\r\n            All Users\r\n          </Button>\r\n          <Button\r\n            variant={activeTab === 'active' ? 'primary' : 'outline'}\r\n            size=\"sm\"\r\n            onClick={() => setActiveTab('active')}\r\n          >\r\n            Active Users\r\n          </Button>\r\n          <Button\r\n            variant={activeTab === 'banned' ? 'primary' : 'outline'}\r\n            size=\"sm\"\r\n            onClick={() => setActiveTab('banned')}\r\n          >\r\n            Banned Users\r\n          </Button>\r\n        </div>\r\n\r\n        <UserList\r\n          users={filteredUsers}\r\n          onViewUser={handleViewUser}\r\n          onEditUser={handleEditUser}\r\n          onDeleteUser={handleDeleteUser}\r\n          onUserClick={handleUserClick}\r\n          title={`${activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} (${filteredUsers.length})`}\r\n          loading={usersLoading}\r\n        />\r\n      </Card>\r\n\r\n      {/* Add User Modal */}\r\n      <Modal\r\n        isOpen={isAddUserModalOpen}\r\n        onClose={() => setIsAddUserModalOpen(false)}\r\n        title=\"Add New User\"\r\n        size=\"lg\"\r\n      >\r\n        <AddUserForm\r\n          onSubmit={handleAddUser}\r\n          onCancel={() => setIsAddUserModalOpen(false)}\r\n          isLoading={isLoading}\r\n        />\r\n      </Modal>\r\n\r\n      {/* User Details Modal */}\r\n      {selectedUser && (\r\n        <Modal\r\n          isOpen={isUserDetailsModalOpen}\r\n          onClose={() => setIsUserDetailsModalOpen(false)}\r\n          title=\"User Details\"\r\n          size=\"lg\"\r\n          footer={\r\n            <>\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={() => setIsUserDetailsModalOpen(false)}\r\n              >\r\n                Close\r\n              </Button>\r\n              <Button\r\n                variant={selectedUser.status === 'active' ? 'danger' : 'success'}\r\n                onClick={() => handleToggleUserStatus(selectedUser.id)}\r\n              >\r\n                {selectedUser.status === 'active' ? 'Ban User' : 'Activate User'}\r\n              </Button>\r\n            </>\r\n          }\r\n        >\r\n          <UserDetailsModal user={selectedUser} />\r\n        </Modal>\r\n      )}\r\n\r\n      {/* Delete Confirmation Modal */}\r\n      {userToDelete && (\r\n        <Modal\r\n          isOpen={isDeleteModalOpen}\r\n          onClose={() => setIsDeleteModalOpen(false)}\r\n          title=\"Delete User\"\r\n          size=\"sm\"\r\n          footer={\r\n            <>\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={() => setIsDeleteModalOpen(false)}\r\n                disabled={isDeleting}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button\r\n                variant=\"danger\"\r\n                onClick={confirmDeleteUser}\r\n                loading={isDeleting}\r\n              >\r\n                Delete User\r\n              </Button>\r\n            </>\r\n          }\r\n        >\r\n          <div className=\"text-sm text-gray-500\">\r\n            Are you sure you want to delete \"{userToDelete.name}\"? This action cannot be undone.\r\n          </div>\r\n        </Modal>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\n// Wrap with error boundary\r\nexport default withErrorBoundary(UsersPage, {\r\n  fallback: ({ error, resetError }) => (\r\n    <div className=\"flex flex-col items-center justify-center min-h-[400px] p-8\">\r\n      <div className=\"text-red-500 text-4xl mb-4\">⚠️</div>\r\n      <h2 className=\"text-xl font-semibold mb-2\">Users Page Error</h2>\r\n      <p className=\"text-gray-600 mb-4 text-center max-w-md\">\r\n        {error.message || 'An error occurred while loading the users page'}\r\n      </p>\r\n      <button\r\n        onClick={resetError}\r\n        className=\"px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition-colors\"\r\n      >\r\n        Reload Page\r\n      </button>\r\n    </div>\r\n  ),\r\n  context: 'UsersPage'\r\n});", "/**\r\n * Modal Component\r\n * \r\n * A reusable modal dialog component.\r\n */\r\n\r\nimport React, { Fragment, useEffect, useRef, memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\nimport { XMarkIcon } from '@heroicons/react/24/outline';\r\nimport { createPortal } from 'react-dom';\r\n\r\nexport interface ModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title: string | ReactNode;\r\n  children: ReactNode;\r\n  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full';\r\n  footer?: ReactNode;\r\n  closeOnEsc?: boolean;\r\n  closeOnBackdropClick?: boolean;\r\n  showCloseButton?: boolean;\r\n  centered?: boolean;\r\n  className?: string;\r\n  bodyClassName?: string;\r\n  headerClassName?: string;\r\n  footerClassName?: string;\r\n  backdropClassName?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst Modal: React.FC<ModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  children,\r\n  size = 'md',\r\n  footer,\r\n  closeOnEsc = true,\r\n  closeOnBackdropClick = true,\r\n  showCloseButton = true,\r\n  centered = true,\r\n  className = '',\r\n  bodyClassName = '',\r\n  headerClassName = '',\r\n  footerClassName = '',\r\n  backdropClassName = '',\r\n  testId,\r\n}) => {\r\n  const modalRef = useRef<HTMLDivElement>(null);\r\n  \r\n  // Handle Escape key press\r\n  useEffect(() => {\r\n    const handleEscape = (e: KeyboardEvent) => {\r\n      if (closeOnEsc && e.key === 'Escape') {\r\n        onClose();\r\n      }\r\n    };\r\n\r\n    if (isOpen) {\r\n      document.addEventListener('keydown', handleEscape);\r\n      // Prevent scrolling on the body when modal is open\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener('keydown', handleEscape);\r\n      document.body.style.overflow = 'auto';\r\n    };\r\n  }, [isOpen, onClose, closeOnEsc]);\r\n  \r\n  // Focus trap inside modal\r\n  useEffect(() => {\r\n    if (!isOpen || !modalRef.current) return;\r\n    \r\n    const focusableElements = modalRef.current.querySelectorAll(\r\n      'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\r\n    );\r\n    \r\n    if (focusableElements.length === 0) return;\r\n    \r\n    const firstElement = focusableElements[0] as HTMLElement;\r\n    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;\r\n    \r\n    const handleTabKey = (e: KeyboardEvent) => {\r\n      if (e.key !== 'Tab') return;\r\n      \r\n      if (e.shiftKey) {\r\n        if (document.activeElement === firstElement) {\r\n          lastElement.focus();\r\n          e.preventDefault();\r\n        }\r\n      } else {\r\n        if (document.activeElement === lastElement) {\r\n          firstElement.focus();\r\n          e.preventDefault();\r\n        }\r\n      }\r\n    };\r\n    \r\n    document.addEventListener('keydown', handleTabKey);\r\n    firstElement.focus();\r\n    \r\n    return () => {\r\n      document.removeEventListener('keydown', handleTabKey);\r\n    };\r\n  }, [isOpen]);\r\n\r\n  if (!isOpen) return null;\r\n  \r\n  // Size classes\r\n  const sizeClasses = {\r\n    xs: 'max-w-xs',\r\n    sm: 'max-w-md',\r\n    md: 'max-w-lg',\r\n    lg: 'max-w-2xl',\r\n    xl: 'max-w-4xl',\r\n    full: 'max-w-full mx-4',\r\n  };\r\n  \r\n  // Modal content\r\n  const modalContent = (\r\n    <Fragment>\r\n      {/* Backdrop */}\r\n      <div \r\n        className={`fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity ${backdropClassName}`}\r\n        onClick={closeOnBackdropClick ? onClose : undefined}\r\n        data-testid={`${testId}-backdrop`}\r\n      />\r\n\r\n      {/* Modal */}\r\n      <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n        <div className={`flex min-h-full items-${centered ? 'center' : 'start'} justify-center p-4 text-center`}>\r\n          <div \r\n            ref={modalRef}\r\n            className={`${sizeClasses[size]} w-full transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all ${className}`}\r\n            onClick={(e) => e.stopPropagation()}\r\n            data-testid={testId}\r\n          >\r\n            {/* Header */}\r\n            <div className={`flex items-center justify-between px-6 py-4 border-b border-gray-100 ${headerClassName}`}>\r\n              {typeof title === 'string' ? (\r\n                <h3 className=\"text-lg font-semibold text-gray-800\">{title}</h3>\r\n              ) : (\r\n                title\r\n              )}\r\n              {showCloseButton && (\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary rounded-full p-1\"\r\n                  onClick={onClose}\r\n                  aria-label=\"Close modal\"\r\n                  data-testid={`${testId}-close-button`}\r\n                >\r\n                  <XMarkIcon className=\"h-6 w-6\" />\r\n                </button>\r\n              )}\r\n            </div>\r\n\r\n            {/* Content */}\r\n            <div className={`px-6 py-4 ${bodyClassName}`}>\r\n              {children}\r\n            </div>\r\n\r\n            {/* Footer */}\r\n            {footer && (\r\n              <div className={`px-6 py-4 bg-gray-50 border-t border-gray-100 flex justify-end space-x-3 ${footerClassName}`}>\r\n                {footer}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Fragment>\r\n  );\r\n  \r\n  // Use portal to render modal at the end of the document body\r\n  return createPortal(modalContent, document.body);\r\n};\r\n\r\nexport default memo(Modal);\r\n"], "names": ["options", "arguments", "length", "undefined", "enableNotifications", "enableReporting", "onError", "showNotification", "useNotification", "errorState", "setErrorState", "useState", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorType", "clearError", "useCallback", "handleApiErrorWithState", "context", "apiError", "handleApiError", "notification", "type", "title", "message", "Error", "reportError", "handleValidationErrorWithState", "field", "code", "validationError", "handleValidationError", "handleFormErrorWithState", "setFieldError", "handleFormError", "handleGeneralError", "errorObj", "String", "logError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "async", "operation", "withFormError<PERSON>andling", "isApiError", "isValidationError", "DefaultError<PERSON><PERSON><PERSON>", "_ref", "resetError", "_jsxs", "className", "children", "_jsx", "onClick", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Component", "config", "fallback", "FallbackComponent", "WrappedComponent", "forwardRef", "props", "ref", "Error<PERSON>ou<PERSON><PERSON>", "window", "location", "reload", "handleError", "errorInfo", "displayName", "name", "componentStack", "errorBoundary", "formatDate", "dateString", "date", "Date", "defaultOptions", "year", "month", "day", "Intl", "DateTimeFormat", "format", "console", "formatCurrency", "amount", "currency", "locale", "NumberFormat", "style", "minimumFractionDigits", "maximumFractionDigits", "toFixed", "status", "_type", "statusKey", "toLowerCase", "colorClass", "icon", "CheckCircleIcon", "ClockIcon", "XCircleIcon", "TruckIcon", "ExclamationCircleIcon", "formattedStatus", "char<PERSON>t", "toUpperCase", "slice", "ArrowDownTrayIcon", "svgRef", "titleId", "React", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "id", "strokeLinecap", "strokeLinejoin", "d", "data", "columns", "onRowClick", "pagination", "loading", "emptyMessage", "DataTable", "UserPlusIcon", "UsersPage", "activeTab", "setActiveTab", "isLoading", "setIsLoading", "isAddUserModalOpen", "setIsAddUserModalOpen", "selected<PERSON>ser", "setSelectedUser", "isUserDetailsModalOpen", "setIsUserDetailsModalOpen", "isDeleteModalOpen", "setIsDeleteModalOpen", "userToDelete", "setUserToDelete", "isDeleting", "setIsDeleting", "users", "usersLoading", "createEntity", "createUser", "deleteEntity", "deleteUser", "updateUserStatus", "useUsers", "useErrorHandler", "filteredUsers", "useMemo", "filter", "user", "handleViewUser", "handleUserClick", "handleDeleteUser", "confirmDeleteUser", "result", "handleExportUsers", "safeAsyncOperation", "Promise", "resolve", "reject", "setTimeout", "Math", "random", "log", "timeout", "retries", "operationName", "success", "handleAddUser", "newUser", "userData", "handleToggleUserStatus", "find", "u", "userId", "newStatus", "<PERSON><PERSON>", "variant", "Card", "size", "UserList", "onViewUser", "onEditUser", "onDeleteUser", "onUserClick", "Modal", "isOpen", "onClose", "AddUserForm", "onSubmit", "onCancel", "footer", "_Fragment", "UserDetailsModal", "disabled", "closeOnEsc", "closeOnBackdropClick", "showCloseButton", "centered", "bodyClassName", "headerClassName", "footerClassName", "backdropClassName", "testId", "modalRef", "useRef", "useEffect", "handleEscape", "e", "key", "document", "addEventListener", "body", "overflow", "removeEventListener", "current", "focusableElements", "querySelectorAll", "firstElement", "lastElement", "handleTabKey", "shift<PERSON>ey", "activeElement", "focus", "preventDefault", "modalContent", "Fragment", "xs", "sm", "md", "lg", "xl", "full", "stopPropagation", "XMarkIcon", "createPortal", "memo"], "sourceRoot": ""}