{"version": 3, "file": "static/js/88.0d6c7c3f.chunk.js", "mappings": "sLAyBA,MAAMA,EAAwCC,IAOvC,IAPwC,MAC7CC,EAAK,YACLC,EAAW,QACXC,EAAO,YACPC,EAAW,UACXC,EAAY,GAAE,OACdC,GACDN,EACC,OACEO,EAAAA,EAAAA,MAAA,OACEF,UAAW,QAAQA,IACnB,cAAaC,EAAOE,SAAA,CAGnBJ,GAAeA,EAAYK,OAAS,IACnCC,EAAAA,EAAAA,KAAA,OAAKL,UAAU,YAAY,aAAW,aAAYG,UAChDD,EAAAA,EAAAA,MAAA,MAAIF,UAAU,oDAAmDG,SAAA,EAC/DE,EAAAA,EAAAA,KAAA,MAAAF,UACEE,EAAAA,EAAAA,KAACC,EAAAA,GAAI,CACHC,GAAG,IACHP,UAAU,uCACV,aAAW,OAAMG,UAEjBE,EAAAA,EAAAA,KAACG,EAAAA,EAAQ,CAACR,UAAU,gBAIvBD,EAAYU,KAAI,CAACC,EAAMC,KACtBT,EAAAA,EAAAA,MAAA,MAAgBF,UAAU,oBAAmBG,SAAA,EAC3CE,EAAAA,EAAAA,KAACO,EAAAA,EAAgB,CAACZ,UAAU,+BAC3BU,EAAKG,MAAQF,EAAQZ,EAAYK,OAAS,GACzCC,EAAAA,EAAAA,KAACC,EAAAA,GAAI,CACHC,GAAIG,EAAKG,KACTb,UAAU,qBAAoBG,SAE7BO,EAAKI,SAGRT,EAAAA,EAAAA,KAAA,QAAML,UAAU,4BAA2BG,SAAEO,EAAKI,UAV7CH,WAmBjBT,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8EAA6EG,SAAA,EAC1FD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAIL,UAAU,mCAAkCG,SAAEP,IACjDC,GAAsC,kBAAhBA,GACrBQ,EAAAA,EAAAA,KAAA,KAAGL,UAAU,6BAA4BG,SAAEN,IAE3CA,KAIHC,IACCO,EAAAA,EAAAA,KAAA,OAAKL,UAAU,oCAAmCG,SAC/CL,SAIH,EAIV,GAAeiB,EAAAA,EAAAA,MAAKrB,E,gDC3FpB,SAASkB,EAAgBjB,EAItBqB,GAAQ,IAJe,MACxBpB,EAAK,QACLqB,KACGC,GACJvB,EACC,OAAoBwB,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQtB,EAAqBuB,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHrB,GAAS,KAAmBuB,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,8BAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBP,E,gDCvBlD,SAASoB,EAAWrC,EAIjBqB,GAAQ,IAJU,MACnBpB,EAAK,QACLqB,KACGC,GACJvB,EACC,OAAoBwB,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQtB,EAAqBuB,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHrB,GAAS,KAAmBuB,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,0EAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBa,E,6ECMlD,MAAMC,EAA8BtC,IAiB7B,IAjB8B,OACnCuC,EAAM,QACNC,EAAO,MACPvC,EAAK,SACLO,EAAQ,KACRiC,EAAO,KAAI,OACXC,EAAM,WACNC,GAAa,EAAI,qBACjBC,GAAuB,EAAI,gBAC3BC,GAAkB,EAAI,SACtBC,GAAW,EAAI,UACfzC,EAAY,GAAE,cACd0C,EAAgB,GAAE,gBAClBC,EAAkB,GAAE,gBACpBC,EAAkB,GAAE,kBACpBC,EAAoB,GAAE,OACtB5C,GACDN,EACC,MAAMmD,GAAWC,EAAAA,EAAAA,QAAuB,MA2DxC,IAxDAC,EAAAA,EAAAA,YAAU,KACR,MAAMC,EAAgBC,IAChBZ,GAAwB,WAAVY,EAAEC,KAClBhB,GACF,EASF,OANID,IACFkB,SAASC,iBAAiB,UAAWJ,GAErCG,SAASE,KAAKC,MAAMC,SAAW,UAG1B,KACLJ,SAASK,oBAAoB,UAAWR,GACxCG,SAASE,KAAKC,MAAMC,SAAW,MAAM,CACtC,GACA,CAACtB,EAAQC,EAASG,KAGrBU,EAAAA,EAAAA,YAAU,KACR,IAAKd,IAAWY,EAASY,QAAS,OAElC,MAAMC,EAAoBb,EAASY,QAAQE,iBACzC,4EAGF,GAAiC,IAA7BD,EAAkBvD,OAAc,OAEpC,MAAMyD,EAAeF,EAAkB,GACjCG,EAAcH,EAAkBA,EAAkBvD,OAAS,GAE3D2D,EAAgBb,IACN,QAAVA,EAAEC,MAEFD,EAAEc,SACAZ,SAASa,gBAAkBJ,IAC7BC,EAAYI,QACZhB,EAAEiB,kBAGAf,SAASa,gBAAkBH,IAC7BD,EAAaK,QACbhB,EAAEiB,kBAEN,EAMF,OAHAf,SAASC,iBAAiB,UAAWU,GACrCF,EAAaK,QAEN,KACLd,SAASK,oBAAoB,UAAWM,EAAa,CACtD,GACA,CAAC7B,KAECA,EAAQ,OAAO,KAGpB,MAUMkC,GACJlE,EAAAA,EAAAA,MAACmE,EAAAA,SAAQ,CAAAlE,SAAA,EAEPE,EAAAA,EAAAA,KAAA,OACEL,UAAW,gEAAgE6C,IAC3EyB,QAAS/B,EAAuBJ,OAAUoC,EAC1C,cAAa,GAAGtE,gBAIlBI,EAAAA,EAAAA,KAAA,OAAKL,UAAU,qCAAoCG,UACjDE,EAAAA,EAAAA,KAAA,OAAKL,UAAW,yBAAyByC,EAAW,SAAW,yCAAyCtC,UACtGD,EAAAA,EAAAA,MAAA,OACEyB,IAAKmB,EACL9C,UAAW,GAxBD,CAClBwE,GAAI,WACJC,GAAI,WACJC,GAAI,WACJC,GAAI,YACJC,GAAI,YACJC,KAAM,mBAkB4BzC,2GAA8GpC,IACxIsE,QAAUpB,GAAMA,EAAE4B,kBAClB,cAAa7E,EAAOE,SAAA,EAGpBD,EAAAA,EAAAA,MAAA,OAAKF,UAAW,wEAAwE2C,IAAkBxC,SAAA,CACtF,kBAAVP,GACNS,EAAAA,EAAAA,KAAA,MAAIL,UAAU,sCAAqCG,SAAEP,IAErDA,EAED4C,IACCnC,EAAAA,EAAAA,KAAA,UACE0E,KAAK,SACL/E,UAAU,wGACVsE,QAASnC,EACT,aAAW,cACX,cAAa,GAAGlC,iBAAsBE,UAEtCE,EAAAA,EAAAA,KAAC2E,EAAAA,EAAS,CAAChF,UAAU,kBAM3BK,EAAAA,EAAAA,KAAA,OAAKL,UAAW,aAAa0C,IAAgBvC,SAC1CA,IAIFkC,IACChC,EAAAA,EAAAA,KAAA,OAAKL,UAAW,4EAA4E4C,IAAkBzC,SAC3GkC,cAUf,OAAO4C,EAAAA,EAAAA,cAAab,EAAchB,SAASE,KAAK,EAGlD,GAAevC,EAAAA,EAAAA,MAAKkB,E,gDClLpB,SAASiD,EAAQvF,EAIdqB,GAAQ,IAJO,MAChBpB,EAAK,QACLqB,KACGC,GACJvB,EACC,OAAoBwB,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQtB,EAAqBuB,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHrB,GAAS,KAAmBuB,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,2BAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB+D,E,gDCvBlD,SAASC,EAAOxF,EAIbqB,GAAQ,IAJM,MACfpB,EAAK,QACLqB,KACGC,GACJvB,EACC,OAAoBwB,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBC,GAClBC,GAAQtB,EAAqBuB,EAAAA,cAAoB,QAAS,CAC3DS,GAAIX,GACHrB,GAAS,KAAmBuB,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,6LACYZ,EAAAA,cAAoB,OAAQ,CAC3CU,cAAe,QACfC,eAAgB,QAChBC,EAAG,wCAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBgE,E,wICDlD,MA4GA,EA5GkDxF,IAQ3C,IAR4C,WACjDyF,EAAU,gBACVC,EAAe,eACfC,EAAc,eACdC,EAAc,iBACdC,EAAgB,MAChB5F,EAAQ,aAAY,QACpB6F,GAAU,GACX9F,EACC,MAAM+F,EAAU,CACd,CACEvC,IAAK,KACLrC,MAAO,KACP6E,UAAU,EACVC,OAASC,IACPxF,EAAAA,EAAAA,KAAA,QAAML,UAAU,wBAAuBG,SAAE0F,KAG7C,CACE1C,IAAK,OACLrC,MAAO,OACP6E,UAAU,EACVC,OAASC,IACPxF,EAAAA,EAAAA,KAAA,QAAML,UAAU,4BAA2BG,SAAE0F,KAGjD,CAAE1C,IAAK,cAAerC,MAAO,cAAe6E,UAAU,GACtD,CACExC,IAAK,eACLrC,MAAO,WACP6E,UAAU,EACVC,OAASC,IACPxF,EAAAA,EAAAA,KAAA,QAAML,UAAU,cAAaG,SAAE0F,KAGnC,CACE1C,IAAK,SACLrC,MAAO,SACP6E,UAAU,EACVC,OAASC,IAEL3F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBG,SAAA,CACrB,WAAV0F,GACCxF,EAAAA,EAAAA,KAACyF,EAAAA,EAAe,CAAC9F,UAAU,iCAE3BK,EAAAA,EAAAA,KAAC2B,EAAAA,EAAW,CAAChC,UAAU,+BAEzBK,EAAAA,EAAAA,KAAA,QAAAF,SAAO0F,EAAME,OAAO,GAAGC,cAAgBH,EAAMI,MAAM,SAK3D,CAAE9C,IAAK,YAAarC,MAAO,aAAc6E,UAAU,GACnD,CACExC,IAAK,UACLrC,MAAO,UACP8E,OAAQA,CAACM,EAAQC,KACfjG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BG,SAAA,CACzCmF,IACCjF,EAAAA,EAAAA,KAAA,UACEL,UAAU,sEACVsE,QAAUpB,IACRA,EAAE4B,kBACFQ,EAAea,EAAS,EACxBhG,UAEFE,EAAAA,EAAAA,KAAC8E,EAAAA,EAAO,CAACnF,UAAU,cAGtBuF,IACClF,EAAAA,EAAAA,KAAA,UACEL,UAAU,uEACVsE,QAAUpB,IACRA,EAAE4B,kBACFS,EAAeY,EAAS,EACxBhG,UAEFE,EAAAA,EAAAA,KAAC+F,EAAAA,EAAU,CAACpG,UAAU,cAGzBwF,IACCnF,EAAAA,EAAAA,KAAA,UACEL,UAAU,sEACVsE,QAAUpB,IACRA,EAAE4B,kBACFU,EAAiBW,EAAS,EAC1BhG,UAEFE,EAAAA,EAAAA,KAACgG,EAAAA,EAAS,CAACrG,UAAU,mBAQjC,OACEK,EAAAA,EAAAA,KAACiG,EAAAA,EAAS,CACRZ,QAASA,EACTa,KAAMnB,EACNoB,WAAYnB,EACZzF,MAAOA,EACP6G,YAAY,EACZhB,QAASA,GACT,E,4BClHN,MAmJA,EAnJwD9F,IAIjD,IAJkD,SACvD+G,EAAQ,SACRC,EAAQ,UACRC,GAAY,GACbjH,EACC,MAAOkH,EAAUC,IAAeC,EAAAA,EAAAA,UAA2B,CACzDC,KAAM,GACNnH,YAAa,GACboH,OAAQ,SACRC,sBAAsB,EACtBC,sBAAsB,KAGjBC,EAAQC,IAAaN,EAAAA,EAAAA,UAAiC,CAAC,GAExDO,EAAgBpE,IACpB,MAAM,KAAE8D,EAAI,MAAEnB,GAAU3C,EAAEqE,OAC1BT,GAAYU,IAAI,IAAUA,EAAM,CAACR,GAAOnB,MAGpCuB,EAAOJ,IACTK,GAAUG,IAAI,IAAUA,EAAM,CAACR,GAAO,MACxC,EAwBF,OACE9G,EAAAA,EAAAA,MAAA,QAAMwG,SATcxD,IACpBA,EAAEiB,iBAdqBsD,MACvB,MAAMC,GAAmBC,EAAAA,EAAAA,GAAa,CACpCX,KAAMH,EAASG,KACfnH,YAAagH,EAAShH,aACrB,CACDmH,KAAM,CAACY,EAAAA,GAAgBC,SAAS,8BAChChI,YAAa,CAAC+H,EAAAA,GAAgBC,SAAS,8BAIzC,OADAR,EAAUK,GACsC,IAAzCtG,OAAO0G,KAAKJ,GAAkBtH,MAAY,EAM7CqH,IACFf,EAASG,EACX,EAI8B7G,UAAU,YAAWG,SAAA,EACjDD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWG,SAAA,EACxBD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACED,EAAAA,EAAAA,MAAA,SAAO6H,QAAQ,OAAO/H,UAAU,0CAAyCG,SAAA,CAAC,kBAC1DE,EAAAA,EAAAA,KAAA,QAAML,UAAU,eAAcG,SAAC,UAE/CE,EAAAA,EAAAA,KAAA,SACE0E,KAAK,OACLnD,GAAG,OACHoF,KAAK,OACLnB,MAAOgB,EAASG,KAChBgB,SAAUV,EACVtH,UAAW,8GACToH,EAAOJ,KAAO,iBAAmB,MAGpCI,EAAOJ,OAAQ3G,EAAAA,EAAAA,KAAA,KAAGL,UAAU,4BAA2BG,SAAEiH,EAAOJ,WAGnE9G,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACED,EAAAA,EAAAA,MAAA,SAAO6H,QAAQ,cAAc/H,UAAU,0CAAyCG,SAAA,CAAC,gBACnEE,EAAAA,EAAAA,KAAA,QAAML,UAAU,eAAcG,SAAC,UAE7CE,EAAAA,EAAAA,KAAA,YACEuB,GAAG,cACHoF,KAAK,cACLiB,KAAM,EACNpC,MAAOgB,EAAShH,YAChBmI,SAAUV,EACVtH,UAAW,8GACToH,EAAOvH,YAAc,iBAAmB,MAG3CuH,EAAOvH,cAAeQ,EAAAA,EAAAA,KAAA,KAAGL,UAAU,4BAA2BG,SAAEiH,EAAOvH,kBAG1EK,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEE,EAAAA,EAAAA,KAAA,SAAO0H,QAAQ,SAAS/H,UAAU,0CAAyCG,SAAC,YAG5ED,EAAAA,EAAAA,MAAA,UACE0B,GAAG,SACHoF,KAAK,SACLnB,MAAOgB,EAASI,OAChBe,SAAUV,EACVtH,UAAU,4GAA2GG,SAAA,EAErHE,EAAAA,EAAAA,KAAA,UAAQwF,MAAM,SAAQ1F,SAAC,YACvBE,EAAAA,EAAAA,KAAA,UAAQwF,MAAM,WAAU1F,SAAC,oBAI7BD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yBAAwBG,SAAA,EACrCE,EAAAA,EAAAA,KAAA,OAAAF,UACED,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBG,SAAA,EAClCE,EAAAA,EAAAA,KAAA,SACE0E,KAAK,WACLiC,KAAK,uBACLkB,QAASrB,EAASK,qBAClBc,SAAW9E,GAAM4D,GAAYU,IAAI,IAAUA,EAAMN,qBAAsBhE,EAAEqE,OAAOW,YAChFlI,UAAU,6DAEZK,EAAAA,EAAAA,KAAA,QAAML,UAAU,6BAA4BG,SAAC,kCAGjDE,EAAAA,EAAAA,KAAA,OAAAF,UACED,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBG,SAAA,EAClCE,EAAAA,EAAAA,KAAA,SACE0E,KAAK,WACLiC,KAAK,uBACLkB,QAASrB,EAASM,qBAClBa,SAAW9E,GAAM4D,GAAYU,IAAI,IAAUA,EAAML,qBAAsBjE,EAAEqE,OAAOW,YAChFlI,UAAU,6DAEZK,EAAAA,EAAAA,KAAA,QAAML,UAAU,6BAA4BG,SAAC,wCAMrDD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA4BG,SAAA,EACzCE,EAAAA,EAAAA,KAAC8H,EAAAA,EAAM,CACLpD,KAAK,SACLqD,QAAQ,UACR9D,QAASqC,EACT0B,SAAUzB,EAAUzG,SACrB,YAGDE,EAAAA,EAAAA,KAAC8H,EAAAA,EAAM,CACLpD,KAAK,SACLU,QAASmB,EAAUzG,SACpB,sBAIE,E,2CCrJJ,MA0EP,EA1E6B,CAI3BmI,cAAeC,UACb,IACE,MAAMC,QAAiBC,EAAAA,EAAUC,IAAgB,cAAe,CAAEC,WAClE,OAAOC,EAAAA,GAAmBC,QAAQL,EAAU,aAC9C,CAAE,MAAOM,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFE,gBAAiBT,UACf,IACE,MAAMC,QAAiBC,EAAAA,EAAUC,IAAc,eAAe9G,KAC9D,OAAOgH,EAAAA,GAAmBK,QAAQT,EAAU,WAAY5G,EAC1D,CAAE,MAAOkH,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFI,eAAgBX,UACd,IACE,MAAMC,QAAiBC,EAAAA,EAAUU,KAAe,cAAeC,GAC/D,OAAOR,EAAAA,GAAmBS,OAAOb,EAAU,WAC7C,CAAE,MAAOM,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFQ,eAAgBf,MAAO3G,EAAYwH,KACjC,IACE,MAAMZ,QAAiBC,EAAAA,EAAUc,IAAc,eAAe3H,IAAMwH,GACpE,OAAOR,EAAAA,GAAmBY,OAAOhB,EAAU,WAAY5G,EACzD,CAAE,MAAOkH,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFW,eAAgBlB,UACd,IACE,MAAMC,QAAiBC,EAAAA,EAAUiB,OAAO,eAAe9H,KACvD,OAAOgH,EAAAA,GAAmBc,OAAOlB,EAAU,WAAY5G,EACzD,CAAE,MAAOkH,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,GAMFa,iBAAkBpB,UAChB,IACE,MAAMC,QAAiBC,EAAAA,EAAUC,IAAgB,cAAe,CAAEC,OAAQ,CAAEiB,cAC5E,OAAOhB,EAAAA,GAAmBC,QAAQL,EAAU,iBAAiB,EAC/D,CAAE,MAAOM,GACP,MAAMC,EAAAA,EAAAA,IAAeD,EACvB,ICrCJ,EAjC6B,WAAuC,IAAtCe,EAAOC,UAAA1J,OAAA,QAAAmE,IAAAuF,UAAA,GAAAA,UAAA,GAAG,CAAEC,cAAc,GACtD,MAAMC,GAAWC,EAAAA,EAAAA,GAAc,CAC7BC,OAAQC,EAAc7B,cACtBW,QAASkB,EAAcnB,gBACvBK,OAAQc,EAAcjB,eACtBM,OAAQW,EAAcb,eACtBI,OAAQS,EAAcV,gBACrB,CACDW,WAAY,aACZL,aAAcF,EAAQE,eAMlBM,GAAuBC,EAAAA,EAAAA,cAAY,IAG/BN,EAASO,SAAwB9J,KAAI0F,IAAQ,IAChDA,EACHqE,cAAerE,EAASqE,eAAiB,QAE1C,CAACR,EAASO,WAEb,MAAO,IACFP,EACH5E,WAAY4E,EAASO,SACrBE,gBAAiBT,EAASU,cAC1B1B,gBAAiBgB,EAASW,cAC1BN,uBAEJ,E", "sources": ["components/layout/PageHeader.tsx", "../node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js", "../node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js", "components/common/Modal.tsx", "../node_modules/@heroicons/react/24/outline/esm/PlusIcon.js", "../node_modules/@heroicons/react/24/outline/esm/EyeIcon.js", "features/categories/components/CategoryList.tsx", "features/categories/components/AddCategoryForm.tsx", "features/categories/api/categoriesApi.ts", "features/categories/hooks/useCategories.ts"], "sourcesContent": ["/**\r\n * PageHeader Component\r\n * \r\n * A consistent header component for pages with title, description, and actions.\r\n */\r\n\r\nimport React, { memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';\r\n\r\nexport interface BreadcrumbItem {\r\n  label: string;\r\n  path?: string;\r\n}\r\n\r\nexport interface PageHeaderProps {\r\n  title: string;\r\n  description?: string | ReactNode;\r\n  actions?: ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n  className?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst PageHeader: React.FC<PageHeaderProps> = ({\r\n  title,\r\n  description,\r\n  actions,\r\n  breadcrumbs,\r\n  className = '',\r\n  testId,\r\n}) => {\r\n  return (\r\n    <div \r\n      className={`mb-6 ${className}`}\r\n      data-testid={testId}\r\n    >\r\n      {/* Breadcrumbs */}\r\n      {breadcrumbs && breadcrumbs.length > 0 && (\r\n        <nav className=\"flex mb-4\" aria-label=\"Breadcrumb\">\r\n          <ol className=\"flex items-center space-x-1 text-sm text-gray-500\">\r\n            <li>\r\n              <Link \r\n                to=\"/\" \r\n                className=\"flex items-center hover:text-primary\"\r\n                aria-label=\"Home\"\r\n              >\r\n                <HomeIcon className=\"h-4 w-4\" />\r\n              </Link>\r\n            </li>\r\n            \r\n            {breadcrumbs.map((item, index) => (\r\n              <li key={index} className=\"flex items-center\">\r\n                <ChevronRightIcon className=\"h-4 w-4 mx-1 text-gray-400\" />\r\n                {item.path && index < breadcrumbs.length - 1 ? (\r\n                  <Link \r\n                    to={item.path} \r\n                    className=\"hover:text-primary\"\r\n                  >\r\n                    {item.label}\r\n                  </Link>\r\n                ) : (\r\n                  <span className=\"font-medium text-gray-700\">{item.label}</span>\r\n                )}\r\n              </li>\r\n            ))}\r\n          </ol>\r\n        </nav>\r\n      )}\r\n      \r\n      {/* Header Content */}\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n        <div>\r\n          <h1 className=\"text-2xl font-bold text-gray-800\">{title}</h1>\r\n          {description && typeof description === 'string' ? (\r\n            <p className=\"mt-1 text-sm text-gray-500\">{description}</p>\r\n          ) : (\r\n            description\r\n          )}\r\n        </div>\r\n        \r\n        {actions && (\r\n          <div className=\"flex flex-wrap gap-3 mt-2 sm:mt-0\">\r\n            {actions}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(PageHeader);\r\n", "import * as React from \"react\";\nfunction ChevronRightIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChevronRightIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction XCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(XCircleIcon);\nexport default ForwardRef;", "/**\r\n * Modal Component\r\n * \r\n * A reusable modal dialog component.\r\n */\r\n\r\nimport React, { Fragment, useEffect, useRef, memo } from 'react';\r\nimport type { ReactNode } from 'react';\r\nimport { XMarkIcon } from '@heroicons/react/24/outline';\r\nimport { createPortal } from 'react-dom';\r\n\r\nexport interface ModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title: string | ReactNode;\r\n  children: ReactNode;\r\n  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full';\r\n  footer?: ReactNode;\r\n  closeOnEsc?: boolean;\r\n  closeOnBackdropClick?: boolean;\r\n  showCloseButton?: boolean;\r\n  centered?: boolean;\r\n  className?: string;\r\n  bodyClassName?: string;\r\n  headerClassName?: string;\r\n  footerClassName?: string;\r\n  backdropClassName?: string;\r\n  testId?: string;\r\n}\r\n\r\nconst Modal: React.FC<ModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  children,\r\n  size = 'md',\r\n  footer,\r\n  closeOnEsc = true,\r\n  closeOnBackdropClick = true,\r\n  showCloseButton = true,\r\n  centered = true,\r\n  className = '',\r\n  bodyClassName = '',\r\n  headerClassName = '',\r\n  footerClassName = '',\r\n  backdropClassName = '',\r\n  testId,\r\n}) => {\r\n  const modalRef = useRef<HTMLDivElement>(null);\r\n  \r\n  // Handle Escape key press\r\n  useEffect(() => {\r\n    const handleEscape = (e: KeyboardEvent) => {\r\n      if (closeOnEsc && e.key === 'Escape') {\r\n        onClose();\r\n      }\r\n    };\r\n\r\n    if (isOpen) {\r\n      document.addEventListener('keydown', handleEscape);\r\n      // Prevent scrolling on the body when modal is open\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener('keydown', handleEscape);\r\n      document.body.style.overflow = 'auto';\r\n    };\r\n  }, [isOpen, onClose, closeOnEsc]);\r\n  \r\n  // Focus trap inside modal\r\n  useEffect(() => {\r\n    if (!isOpen || !modalRef.current) return;\r\n    \r\n    const focusableElements = modalRef.current.querySelectorAll(\r\n      'button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])'\r\n    );\r\n    \r\n    if (focusableElements.length === 0) return;\r\n    \r\n    const firstElement = focusableElements[0] as HTMLElement;\r\n    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;\r\n    \r\n    const handleTabKey = (e: KeyboardEvent) => {\r\n      if (e.key !== 'Tab') return;\r\n      \r\n      if (e.shiftKey) {\r\n        if (document.activeElement === firstElement) {\r\n          lastElement.focus();\r\n          e.preventDefault();\r\n        }\r\n      } else {\r\n        if (document.activeElement === lastElement) {\r\n          firstElement.focus();\r\n          e.preventDefault();\r\n        }\r\n      }\r\n    };\r\n    \r\n    document.addEventListener('keydown', handleTabKey);\r\n    firstElement.focus();\r\n    \r\n    return () => {\r\n      document.removeEventListener('keydown', handleTabKey);\r\n    };\r\n  }, [isOpen]);\r\n\r\n  if (!isOpen) return null;\r\n  \r\n  // Size classes\r\n  const sizeClasses = {\r\n    xs: 'max-w-xs',\r\n    sm: 'max-w-md',\r\n    md: 'max-w-lg',\r\n    lg: 'max-w-2xl',\r\n    xl: 'max-w-4xl',\r\n    full: 'max-w-full mx-4',\r\n  };\r\n  \r\n  // Modal content\r\n  const modalContent = (\r\n    <Fragment>\r\n      {/* Backdrop */}\r\n      <div \r\n        className={`fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity ${backdropClassName}`}\r\n        onClick={closeOnBackdropClick ? onClose : undefined}\r\n        data-testid={`${testId}-backdrop`}\r\n      />\r\n\r\n      {/* Modal */}\r\n      <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n        <div className={`flex min-h-full items-${centered ? 'center' : 'start'} justify-center p-4 text-center`}>\r\n          <div \r\n            ref={modalRef}\r\n            className={`${sizeClasses[size]} w-full transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all ${className}`}\r\n            onClick={(e) => e.stopPropagation()}\r\n            data-testid={testId}\r\n          >\r\n            {/* Header */}\r\n            <div className={`flex items-center justify-between px-6 py-4 border-b border-gray-100 ${headerClassName}`}>\r\n              {typeof title === 'string' ? (\r\n                <h3 className=\"text-lg font-semibold text-gray-800\">{title}</h3>\r\n              ) : (\r\n                title\r\n              )}\r\n              {showCloseButton && (\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary rounded-full p-1\"\r\n                  onClick={onClose}\r\n                  aria-label=\"Close modal\"\r\n                  data-testid={`${testId}-close-button`}\r\n                >\r\n                  <XMarkIcon className=\"h-6 w-6\" />\r\n                </button>\r\n              )}\r\n            </div>\r\n\r\n            {/* Content */}\r\n            <div className={`px-6 py-4 ${bodyClassName}`}>\r\n              {children}\r\n            </div>\r\n\r\n            {/* Footer */}\r\n            {footer && (\r\n              <div className={`px-6 py-4 bg-gray-50 border-t border-gray-100 flex justify-end space-x-3 ${footerClassName}`}>\r\n                {footer}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Fragment>\r\n  );\r\n  \r\n  // Use portal to render modal at the end of the document body\r\n  return createPortal(modalContent, document.body);\r\n};\r\n\r\nexport default memo(Modal);\r\n", "import * as React from \"react\";\nfunction PlusIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 4.5v15m7.5-7.5h-15\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PlusIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction EyeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EyeIcon);\nexport default ForwardRef;", "/**\r\n * Category List Component\r\n * \r\n * This component displays a list of categories in a data table.\r\n */\r\n\r\nimport React from 'react';\r\nimport DataTable from '../../../components/common/DataTable';\r\nimport type{ Category } from '../types/index';\r\nimport { \r\n  CheckCircleIcon, \r\n  XCircleIcon,\r\n  PencilIcon,\r\n  TrashIcon,\r\n  EyeIcon\r\n} from '@heroicons/react/24/outline';\r\n\r\ninterface CategoryListProps {\r\n  categories: Category[];\r\n  onCategoryClick: (category: Category) => void;\r\n  onViewCategory?: (category: Category) => void;\r\n  onEditCategory?: (category: Category) => void;\r\n  onDeleteCategory?: (category: Category) => void;\r\n  title?: string;\r\n  loading?: boolean;\r\n}\r\n\r\nconst CategoryList: React.FC<CategoryListProps> = ({\r\n  categories,\r\n  onCategoryClick,\r\n  onViewCategory,\r\n  onEditCategory,\r\n  onDeleteCategory,\r\n  title = 'Categories',\r\n  loading = false\r\n}) => {\r\n  const columns = [\r\n    { \r\n      key: 'id', \r\n      label: 'ID', \r\n      sortable: true,\r\n      render: (value: string) => (\r\n        <span className=\"text-xs text-gray-500\">{value}</span>\r\n      )\r\n    },\r\n    { \r\n      key: 'name', \r\n      label: 'Name', \r\n      sortable: true,\r\n      render: (value: string) => (\r\n        <span className=\"font-medium text-gray-900\">{value}</span>\r\n      )\r\n    },\r\n    { key: 'description', label: 'Description', sortable: true },\r\n    { \r\n      key: 'productCount', \r\n      label: 'Products', \r\n      sortable: true,\r\n      render: (value: number) => (\r\n        <span className=\"font-medium\">{value}</span>\r\n      )\r\n    },\r\n    { \r\n      key: 'status', \r\n      label: 'Status', \r\n      sortable: true,\r\n      render: (value: string) => {\r\n        return (\r\n          <div className=\"flex items-center\">\r\n            {value === 'active' ? (\r\n              <CheckCircleIcon className=\"w-4 h-4 text-green-500 mr-1\" />\r\n            ) : (\r\n              <XCircleIcon className=\"w-4 h-4 text-red-500 mr-1\" />\r\n            )}\r\n            <span>{value.charAt(0).toUpperCase() + value.slice(1)}</span>\r\n          </div>\r\n        );\r\n      }\r\n    },\r\n    { key: 'createdAt', label: 'Created At', sortable: true },\r\n    {\r\n      key: 'actions',\r\n      label: 'Actions',\r\n      render: (_: any, category: Category) => (\r\n        <div className=\"flex items-center space-x-2\">\r\n          {onViewCategory && (\r\n            <button\r\n              className=\"p-1 text-gray-500 hover:text-primary rounded-full hover:bg-gray-100\"\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                onViewCategory(category);\r\n              }}\r\n            >\r\n              <EyeIcon className=\"w-5 h-5\" />\r\n            </button>\r\n          )}\r\n          {onEditCategory && (\r\n            <button\r\n              className=\"p-1 text-gray-500 hover:text-blue-600 rounded-full hover:bg-gray-100\"\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                onEditCategory(category);\r\n              }}\r\n            >\r\n              <PencilIcon className=\"w-5 h-5\" />\r\n            </button>\r\n          )}\r\n          {onDeleteCategory && (\r\n            <button\r\n              className=\"p-1 text-gray-500 hover:text-red-600 rounded-full hover:bg-gray-100\"\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                onDeleteCategory(category);\r\n              }}\r\n            >\r\n              <TrashIcon className=\"w-5 h-5\" />\r\n            </button>\r\n          )}\r\n        </div>\r\n      )\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <DataTable\r\n      columns={columns}\r\n      data={categories}\r\n      onRowClick={onCategoryClick}\r\n      title={title}\r\n      pagination={true}\r\n      loading={loading}\r\n    />\r\n  );\r\n};\r\n\r\nexport default CategoryList;\r\n", "/**\r\n * Add Category Form Component\r\n * \r\n * This component provides a form for adding new categories.\r\n */\r\n\r\nimport React, { useState } from 'react';\r\nimport Button from '../../../components/common/Button';\r\nimport type { CategoryFormData } from '../types/index';\r\nimport { validateForm, validationRules } from '../../../utils/validation';\r\n\r\ninterface AddCategoryFormProps {\r\n  onSubmit: (categoryData: CategoryFormData) => void;\r\n  onCancel: () => void;\r\n  isLoading?: boolean;\r\n}\r\n\r\nconst AddCategoryForm: React.FC<AddCategoryFormProps> = ({\r\n  onSubmit,\r\n  onCancel,\r\n  isLoading = false\r\n}) => {\r\n  const [formData, setFormData] = useState<CategoryFormData>({\r\n    name: '',\r\n    description: '',\r\n    status: 'active',\r\n    visibleInSupplierApp: true,\r\n    visibleInCustomerApp: true\r\n  });\r\n\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({ ...prev, [name]: value }));\r\n    \r\n    // Clear error when field is edited\r\n    if (errors[name]) {\r\n      setErrors(prev => ({ ...prev, [name]: '' }));\r\n    }\r\n  };\r\n\r\n  const validateFormData = () => {\r\n    const validationErrors = validateForm({\r\n      name: formData.name,\r\n      description: formData.description\r\n    }, {\r\n      name: [validationRules.required('Category name is required')],\r\n      description: [validationRules.required('Description is required')]\r\n    });\r\n\r\n    setErrors(validationErrors);\r\n    return Object.keys(validationErrors).length === 0;\r\n  };\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    if (validateFormData()) {\r\n      onSubmit(formData);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n      <div className=\"space-y-4\">\r\n        <div>\r\n          <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">\r\n            Category Name <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <input\r\n            type=\"text\"\r\n            id=\"name\"\r\n            name=\"name\"\r\n            value={formData.name}\r\n            onChange={handleChange}\r\n            className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm ${\r\n              errors.name ? 'border-red-300' : ''\r\n            }`}\r\n          />\r\n          {errors.name && <p className=\"mt-1 text-sm text-red-600\">{errors.name}</p>}\r\n        </div>\r\n\r\n        <div>\r\n          <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700\">\r\n            Description <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <textarea\r\n            id=\"description\"\r\n            name=\"description\"\r\n            rows={3}\r\n            value={formData.description}\r\n            onChange={handleChange}\r\n            className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm ${\r\n              errors.description ? 'border-red-300' : ''\r\n            }`}\r\n          />\r\n          {errors.description && <p className=\"mt-1 text-sm text-red-600\">{errors.description}</p>}\r\n        </div>\r\n\r\n        <div>\r\n          <label htmlFor=\"status\" className=\"block text-sm font-medium text-gray-700\">\r\n            Status\r\n          </label>\r\n          <select\r\n            id=\"status\"\r\n            name=\"status\"\r\n            value={formData.status}\r\n            onChange={handleChange}\r\n            className=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm\"\r\n          >\r\n            <option value=\"active\">Active</option>\r\n            <option value=\"inactive\">Inactive</option>\r\n          </select>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-2 gap-4\">\r\n          <div>\r\n            <label className=\"flex items-center\">\r\n              <input\r\n                type=\"checkbox\"\r\n                name=\"visibleInSupplierApp\"\r\n                checked={formData.visibleInSupplierApp}\r\n                onChange={(e) => setFormData(prev => ({ ...prev, visibleInSupplierApp: e.target.checked }))}\r\n                className=\"rounded border-gray-300 text-primary focus:ring-primary\"\r\n              />\r\n              <span className=\"ml-2 text-sm text-gray-700\">Visible in Supplier App</span>\r\n            </label>\r\n          </div>\r\n          <div>\r\n            <label className=\"flex items-center\">\r\n              <input\r\n                type=\"checkbox\"\r\n                name=\"visibleInCustomerApp\"\r\n                checked={formData.visibleInCustomerApp}\r\n                onChange={(e) => setFormData(prev => ({ ...prev, visibleInCustomerApp: e.target.checked }))}\r\n                className=\"rounded border-gray-300 text-primary focus:ring-primary\"\r\n              />\r\n              <span className=\"ml-2 text-sm text-gray-700\">Visible in Customer App</span>\r\n            </label>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex justify-end space-x-3\">\r\n        <Button \r\n          type=\"button\" \r\n          variant=\"outline\" \r\n          onClick={onCancel}\r\n          disabled={isLoading}\r\n        >\r\n          Cancel\r\n        </Button>\r\n        <Button \r\n          type=\"submit\" \r\n          loading={isLoading}\r\n        >\r\n          Add Category\r\n        </Button>\r\n      </div>\r\n    </form>\r\n  );\r\n};\r\n\r\nexport default AddCategoryForm;\r\n", "/**\r\n * Categories API Service\r\n * \r\n * This file provides methods for interacting with the categories API endpoints.\r\n */\r\n\r\nimport apiClient from '../../../api';\r\nimport { handleApiError } from '../../../utils/errorHandling';\r\nimport { responseValidators } from '../../../utils/apiHelpers';\r\nimport type { Category, CategoryFormData } from '../types';\r\n\r\nexport const categoriesApi = {\r\n  /**\r\n   * Get all categories\r\n   */\r\n  getCategories: async (params?: Record<string, any>): Promise<Category[]> => {\r\n    try {\r\n      const response = await apiClient.get<Category[]>('/categories', { params });\r\n      return responseValidators.getList(response, 'categories');\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get a category by ID\r\n   */\r\n  getCategoryById: async (id: string): Promise<Category> => {\r\n    try {\r\n      const response = await apiClient.get<Category>(`/categories/${id}`);\r\n      return responseValidators.getById(response, 'category', id);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Create a new category\r\n   */\r\n  createCategory: async (categoryData: CategoryFormData): Promise<Category> => {\r\n    try {\r\n      const response = await apiClient.post<Category>('/categories', categoryData);\r\n      return responseValidators.create(response, 'category');\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update a category\r\n   */\r\n  updateCategory: async (id: string, categoryData: Partial<CategoryFormData>): Promise<Category> => {\r\n    try {\r\n      const response = await apiClient.put<Category>(`/categories/${id}`, categoryData);\r\n      return responseValidators.update(response, 'category', id);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Delete a category\r\n   */\r\n  deleteCategory: async (id: string): Promise<void> => {\r\n    try {\r\n      const response = await apiClient.delete(`/categories/${id}`);\r\n      return responseValidators.delete(response, 'category', id);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get subcategories for a parent category\r\n   */\r\n  getSubcategories: async (parentId: string): Promise<Category[]> => {\r\n    try {\r\n      const response = await apiClient.get<Category[]>('/categories', { params: { parentId } });\r\n      return responseValidators.getList(response, 'subcategories', true);\r\n    } catch (error) {\r\n      throw handleApiError(error);\r\n    }\r\n  }\r\n};\r\n\r\nexport default categoriesApi;\r\n", "/**\r\n * Categories Hook\r\n * \r\n * This hook provides methods and state for working with categories.\r\n */\r\n\r\nimport { useCallback } from 'react';\r\nimport { useEntityData } from '../../../hooks/useEntityData';\r\nimport categoriesApi from '../api/categoriesApi';\r\nimport type { Category } from '../types';\r\n\r\nexport const useCategories = (options = { initialFetch: true }) => {\r\n  const baseHook = useEntityData({\r\n    getAll: categoriesApi.getCategories,\r\n    getById: categoriesApi.getCategoryById,\r\n    create: categoriesApi.createCategory,\r\n    update: categoriesApi.updateCategory,\r\n    delete: categoriesApi.deleteCategory\r\n  }, {\r\n    entityName: 'categories',\r\n    initialFetch: options.initialFetch\r\n  });\r\n  \r\n  // Note: useNotification is available through useEntityData if needed\r\n  \r\n  // Category-specific methods\r\n  const getCategoryHierarchy = useCallback(() => {\r\n    // In the new hierarchy, all categories are top-level\r\n    // Subcategories are now embedded within categories\r\n    return (baseHook.entities as Category[]).map(category => ({\r\n      ...category,\r\n      subcategories: category.subcategories || []\r\n    }));\r\n  }, [baseHook.entities]);\r\n  \r\n  return {\r\n    ...baseHook,\r\n    categories: baseHook.entities as Category[],\r\n    fetchCategories: baseHook.fetchEntities,\r\n    getCategoryById: baseHook.getEntityById,\r\n    getCategoryHierarchy\r\n  };\r\n};\r\n\r\nexport default useCategories;\r\n\r\n\r\n\r\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "_ref", "title", "description", "actions", "breadcrumbs", "className", "testId", "_jsxs", "children", "length", "_jsx", "Link", "to", "HomeIcon", "map", "item", "index", "ChevronRightIcon", "path", "label", "memo", "svgRef", "titleId", "props", "React", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "id", "strokeLinecap", "strokeLinejoin", "d", "XCircleIcon", "Modal", "isOpen", "onClose", "size", "footer", "closeOnEsc", "closeOnBackdropClick", "showCloseButton", "centered", "bodyClassName", "headerClassName", "footerClassName", "backdropClassName", "modalRef", "useRef", "useEffect", "handleEscape", "e", "key", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "current", "focusableElements", "querySelectorAll", "firstElement", "lastElement", "handleTabKey", "shift<PERSON>ey", "activeElement", "focus", "preventDefault", "modalContent", "Fragment", "onClick", "undefined", "xs", "sm", "md", "lg", "xl", "full", "stopPropagation", "type", "XMarkIcon", "createPortal", "PlusIcon", "EyeIcon", "categories", "onCategoryClick", "onViewCategory", "onEditCategory", "onDeleteCategory", "loading", "columns", "sortable", "render", "value", "CheckCircleIcon", "char<PERSON>t", "toUpperCase", "slice", "_", "category", "PencilIcon", "TrashIcon", "DataTable", "data", "onRowClick", "pagination", "onSubmit", "onCancel", "isLoading", "formData", "setFormData", "useState", "name", "status", "visibleInSupplierApp", "visibleInCustomerApp", "errors", "setErrors", "handleChange", "target", "prev", "validateFormData", "validationErrors", "validateForm", "validationRules", "required", "keys", "htmlFor", "onChange", "rows", "checked", "<PERSON><PERSON>", "variant", "disabled", "getCategories", "async", "response", "apiClient", "get", "params", "responseValidators", "getList", "error", "handleApiError", "getCategoryById", "getById", "createCategory", "post", "categoryData", "create", "updateCategory", "put", "update", "deleteCategory", "delete", "getSubcategories", "parentId", "options", "arguments", "initialFetch", "baseHook", "useEntityData", "getAll", "categoriesApi", "entityName", "getCategoryHierarchy", "useCallback", "entities", "subcategories", "fetchCategories", "fetchEntities", "getEntityById"], "sourceRoot": ""}