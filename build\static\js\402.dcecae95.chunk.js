"use strict";(self.webpackChunkadmin_dashboard=self.webpackChunkadmin_dashboard||[]).push([[402],{402:(e,t,s)=>{s.r(t),s.d(t,{default:()=>L});var r=s(5043),a=s(2806),i=s(3593),n=s(8100),l=s(3927),o=s(9705),c=s(7422),d=s(8300),m=s(8682),u=s(9850),x=s(7012),h=s(4538),p=s(7098),g=s(9531),f=s(9422),y=s(7591),v=s(579);const j=e=>{let{verifications:t,onViewVerification:s,onApproveClick:r,onRejectClick:a,title:i="Verifications",description:n}=e;const l=[{key:"companyName",label:"Company Name",sortable:!0,render:(e,t)=>(0,v.jsxs)("div",{className:"flex items-center",children:[(0,v.jsx)("div",{className:"mr-3",children:(0,v.jsx)(d.A,{alt:t.companyName,name:t.companyName,size:"sm"})}),(0,v.jsxs)("div",{children:[(0,v.jsx)("div",{className:"font-medium text-gray-900",children:t.companyName}),(0,v.jsxs)("div",{className:"text-xs text-gray-500",children:["ID: ",t.id]})]})]})},{key:"email",label:"Email",sortable:!0,render:e=>(0,v.jsxs)("div",{className:"flex items-center",children:[(0,v.jsx)(m.A,{className:"w-4 h-4 text-gray-400 mr-2"}),(0,v.jsx)("span",{children:e})]})},{key:"phone",label:"Phone",sortable:!0,render:e=>(0,v.jsxs)("div",{className:"flex items-center",children:[(0,v.jsx)(u.A,{className:"w-4 h-4 text-gray-400 mr-2"}),(0,v.jsx)("span",{children:e})]})},{key:"status",label:"Status",sortable:!0,render:e=>{if(!e)return(0,v.jsxs)("div",{className:"flex items-center",children:[(0,v.jsx)(x.A,{className:"w-4 h-4 text-gray-400 mr-1"}),(0,v.jsx)("span",{children:"Unknown"})]});let t;switch(e){case"verified":t=(0,v.jsx)(h.A,{className:"w-4 h-4 text-green-500 mr-1"});break;case"pending":t=(0,v.jsx)(x.A,{className:"w-4 h-4 text-yellow-500 mr-1"});break;case"rejected":t=(0,v.jsx)(p.A,{className:"w-4 h-4 text-red-500 mr-1"});break;default:t=(0,v.jsx)(x.A,{className:"w-4 h-4 text-gray-400 mr-1"})}return(0,v.jsxs)("div",{className:"flex items-center",children:[t,(0,v.jsx)("span",{children:e?e.charAt(0).toUpperCase()+e.slice(1):"Unknown"})]})}},{key:"submittedDate",label:"Submitted Date",sortable:!0,render:e=>new Date(e).toLocaleDateString()},{key:"actions",label:"Actions",render:(e,t)=>(0,v.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,v.jsx)("button",{className:"p-1 text-gray-500 hover:text-primary rounded-full hover:bg-gray-100",onClick:e=>{e.stopPropagation(),s(t)},title:"View verification details",children:(0,v.jsx)(g.A,{className:"w-5 h-5"})}),(0,v.jsx)("button",{className:"p-1 text-gray-500 hover:text-green-600 rounded-full hover:bg-gray-100",onClick:e=>{e.stopPropagation(),r(t)},title:"Approve verification",children:(0,v.jsx)(f.A,{className:"w-5 h-5"})}),(0,v.jsx)("button",{className:"p-1 text-gray-500 hover:text-red-600 rounded-full hover:bg-gray-100",onClick:e=>{e.stopPropagation(),a(t)},title:"Reject verification",children:(0,v.jsx)(y.A,{className:"w-5 h-5"})})]})}];return(0,v.jsx)(c.A,{columns:l,data:t,title:i,description:n,pagination:!0,selectable:!1})};var b=s(7907);const w=e=>{let{verification:t,onClose:s,onApprove:r,onReject:a}=e;return(0,v.jsxs)("div",{className:"space-y-6",children:[(0,v.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,v.jsxs)("div",{children:[(0,v.jsx)("h3",{className:"text-sm font-medium text-gray-500",children:"Company Information"}),(0,v.jsxs)("div",{className:"mt-2 space-y-2",children:[(0,v.jsxs)("p",{className:"text-sm text-gray-700",children:[(0,v.jsx)("span",{className:"font-medium",children:"Company Name:"})," ",t.companyName]}),(0,v.jsxs)("p",{className:"text-sm text-gray-700",children:[(0,v.jsx)("span",{className:"font-medium",children:"Contact Person:"})," ",t.contactPerson]}),(0,v.jsxs)("p",{className:"text-sm text-gray-700",children:[(0,v.jsx)("span",{className:"font-medium",children:"Email:"})," ",t.email]}),(0,v.jsxs)("p",{className:"text-sm text-gray-700",children:[(0,v.jsx)("span",{className:"font-medium",children:"Phone:"})," ",t.phone]}),(0,v.jsxs)("p",{className:"text-sm text-gray-700",children:[(0,v.jsx)("span",{className:"font-medium",children:"Submitted Date:"})," ",new Date(t.submittedDate).toLocaleString()]})]})]}),(0,v.jsxs)("div",{children:[(0,v.jsx)("h3",{className:"text-sm font-medium text-gray-500",children:"Submitted Documents"}),(0,v.jsx)("div",{className:"mt-2",children:(0,v.jsx)("ul",{className:"divide-y divide-gray-200",children:t.documents.map(((e,t)=>(0,v.jsx)("li",{className:"py-2",children:(0,v.jsxs)("div",{className:"flex items-center justify-between",children:[(0,v.jsxs)("div",{children:[(0,v.jsx)("p",{className:"text-sm font-medium text-gray-700",children:e.name}),(0,v.jsxs)("p",{className:"text-xs text-gray-500",children:[e.type," Document"]})]}),(0,v.jsx)(b.A,{variant:"outline",size:"xs",href:e.url,target:"_blank",children:"View"})]})},t)))})})]})]}),(0,v.jsx)("div",{className:"border-t border-gray-200 pt-4",children:(0,v.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,v.jsx)(b.A,{variant:"outline",onClick:s,children:"Close"}),(0,v.jsx)(b.A,{variant:"success",icon:(0,v.jsx)(f.A,{className:"h-4 w-4"}),onClick:r,children:"Approve"}),(0,v.jsx)(b.A,{variant:"danger",icon:(0,v.jsx)(y.A,{className:"h-4 w-4"}),onClick:a,children:"Reject"})]})})]})},N=e=>{let{isOpen:t,onClose:s,onApprove:r,verification:a}=e;return a?(0,v.jsxs)(n.A,{isOpen:t,onClose:s,title:"Approve Supplier",size:"sm",footer:(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(b.A,{variant:"outline",onClick:s,children:"Cancel"}),(0,v.jsx)(b.A,{variant:"success",onClick:r,children:"Approve"})]}),children:[(0,v.jsxs)("p",{className:"text-sm text-gray-600",children:["Are you sure you want to approve ",(0,v.jsx)("span",{className:"font-medium",children:a.companyName})," as a verified supplier?"]}),(0,v.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"This will grant them access to list products and receive orders on the platform."})]}):null},k=e=>{let{isOpen:t,onClose:s,onReject:r,verification:a,rejectReason:i,setRejectReason:l}=e;return a?(0,v.jsx)(n.A,{isOpen:t,onClose:s,title:"Reject Supplier",size:"sm",footer:(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(b.A,{variant:"outline",onClick:s,children:"Cancel"}),(0,v.jsx)(b.A,{variant:"danger",onClick:r,children:"Reject"})]}),children:(0,v.jsxs)("div",{className:"space-y-4",children:[(0,v.jsxs)("p",{className:"text-sm text-gray-600",children:["Are you sure you want to reject ",(0,v.jsx)("span",{className:"font-medium",children:a.companyName}),"'s verification request?"]}),(0,v.jsxs)("div",{children:[(0,v.jsx)("label",{htmlFor:"reject-reason",className:"block text-sm font-medium text-gray-700",children:"Reason for Rejection"}),(0,v.jsx)("textarea",{id:"reject-reason",rows:3,className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm",placeholder:"Please provide a reason for rejection",value:i,onChange:e=>l(e.target.value)})]}),(0,v.jsx)("p",{className:"text-xs text-gray-500",children:"This reason will be sent to the supplier via email."})]})}):null};var C=s(1568),A=s(4703),$=s(8479);const E={getVerifications:async e=>{try{const t=await C.A.get("/verifications",{params:e});return $.lg.getList(t,"verifications")}catch(t){throw(0,A.hS)(t)}},getVerificationById:async e=>{try{const t=await C.A.get(`/verifications/${e}`);return $.lg.getById(t,"verification",e)}catch(t){throw(0,A.hS)(t)}},createVerification:async e=>{try{const t=await C.A.post("/verifications",e);return $.lg.create(t,"verification")}catch(t){throw(0,A.hS)(t)}},updateVerificationStatus:async(e,t)=>{try{const s=await C.A.put(`/verifications/${e}/status`,{status:t});return $.lg.update(s,"verification",e)}catch(s){throw(0,A.hS)(s)}},getVerificationsByStatus:async e=>{try{const t=await C.A.get("/verifications",{params:{status:e}});return $.lg.getList(t,"verifications",!0)}catch(t){throw(0,A.hS)(t)}},getVerificationsByUser:async e=>{try{const t=await C.A.get("/verifications",{params:{userId:e}});return $.lg.getList(t,"verifications",!0)}catch(t){throw(0,A.hS)(t)}},updateVerification:async(e,t)=>{try{const s=await C.A.put(`/verifications/${e}`,t);return $.lg.update(s,"verification",e)}catch(s){throw(0,A.hS)(s)}},approveVerification:async(e,t)=>{try{const s=await C.A.put(`/verifications/${e}/approve`,{notes:t});return $.lg.update(s,"verification",e)}catch(s){throw(0,A.hS)(s)}},rejectVerification:async(e,t)=>{try{const s=await C.A.put(`/verifications/${e}/reject`,{notes:t});return $.lg.update(s,"verification",e)}catch(s){throw(0,A.hS)(s)}}},S=()=>{const[e,t]=(0,r.useState)([]),[s,a]=(0,r.useState)(!1),[i,n]=(0,r.useState)(null),{showNotification:l}=(0,o.A)(),c=(0,r.useRef)(l),d=(0,r.useRef)(!1);(0,r.useEffect)((()=>{c.current=l}));const m=(0,r.useCallback)((async()=>{a(!0),n(null);try{const e=await E.getVerifications();t(e)}catch(e){n(e),c.current({type:"error",title:"Error",message:"Failed to fetch verifications"})}finally{a(!1)}}),[]),u=(0,r.useCallback)((async e=>{a(!0),n(null);try{return await E.getVerificationById(e)}catch(t){throw n(t),c.current({type:"error",title:"Error",message:`Failed to fetch verification ${e}`}),t}finally{a(!1)}}),[]),x=(0,r.useCallback)((async e=>{a(!0),n(null);try{return await E.getVerificationsByStatus(e)}catch(t){throw n(t),c.current({type:"error",title:"Error",message:`Failed to fetch verifications with status ${e}`}),t}finally{a(!1)}}),[]),h=(0,r.useCallback)((async(e,s)=>{a(!0),n(null);try{const r=await E.updateVerification(e,s);return t((t=>t.map((t=>t.id===e?r:t)))),c.current({type:"success",title:"Success",message:"Verification updated successfully"}),r}catch(r){throw n(r),c.current({type:"error",title:"Error",message:"Failed to update verification"}),r}finally{a(!1)}}),[]),p=(0,r.useCallback)((async function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";a(!0),n(null);try{const r=await E.approveVerification(e,s);return t((t=>t.map((t=>t.id===e?r:t)))),r}catch(r){throw n(r),c.current({type:"error",title:"Error",message:"Failed to approve verification"}),r}finally{a(!1)}}),[]),g=(0,r.useCallback)((async(e,s)=>{a(!0),n(null);try{const r=await E.rejectVerification(e,s);return t((t=>t.map((t=>t.id===e?r:t)))),r}catch(r){throw n(r),c.current({type:"error",title:"Error",message:"Failed to reject verification"}),r}finally{a(!1)}}),[]);return(0,r.useEffect)((()=>{d.current||(d.current=!0,m())}),[]),{verifications:e,isLoading:s,error:i,fetchVerifications:m,getVerificationById:u,getVerificationsByStatus:x,updateVerification:h,approveVerification:p,rejectVerification:g}},L=()=>{const{verifications:e,isLoading:t,approveVerification:s,rejectVerification:c}=S(),{showSuccess:d,showError:m}=(0,o.A)(),[u,x]=(0,r.useState)(null),[h,p]=(0,r.useState)(!1),[g,f]=(0,r.useState)(!1),[y,b]=(0,r.useState)(!1),[C,A]=(0,r.useState)(""),[$,E]=(0,r.useState)(new Set),L=(0,r.useMemo)((()=>e.filter((e=>!$.has(e.id)&&"pending"===e.status))),[e,$]);return(0,v.jsxs)("div",{children:[(0,v.jsx)(a.A,{title:"Supplier Verifications",description:"Review and manage supplier verification requests",breadcrumbs:[{label:"Verifications"}]}),(0,v.jsx)(i.A,{children:t?(0,v.jsx)("div",{className:"flex justify-center py-8",children:(0,v.jsx)(l.A,{})}):0===L.length?(0,v.jsxs)("div",{className:"text-center py-12",children:[(0,v.jsx)("div",{className:"text-gray-500 text-lg mb-2",children:"No pending verifications"}),(0,v.jsx)("div",{className:"text-gray-400 text-sm",children:"All verification requests have been processed or there are no new requests."})]}):(0,v.jsx)(j,{verifications:L,onViewVerification:e=>{x(e),p(!0)},onApproveClick:e=>{x(e),f(!0)},onRejectClick:e=>{x(e),b(!0)},title:"Pending Verifications",description:"Suppliers waiting for verification approval"})}),(0,v.jsx)(n.A,{isOpen:h,onClose:()=>p(!1),title:"Verification Details",size:"lg",children:u&&(0,v.jsx)(w,{verification:u,onClose:()=>p(!1),onApprove:()=>{p(!1),f(!0)},onReject:()=>{p(!1),b(!0)}})}),(0,v.jsx)(N,{isOpen:g,onClose:()=>f(!1),onApprove:async()=>{if(u)try{await s(u.id),E((e=>new Set(e).add(u.id))),d(`${u.companyName} has been approved`),f(!1),x(null)}catch(e){console.error("Error approving verification:",e)}},verification:u}),(0,v.jsx)(k,{isOpen:y,onClose:()=>b(!1),onReject:async()=>{if(C.trim()){if(u)try{await c(u.id,C),E((e=>new Set(e).add(u.id))),d(`${u.companyName} has been rejected`),b(!1),A(""),x(null)}catch(e){console.error("Error rejecting verification:",e)}}else m("Please provide a reason for rejection")},verification:u,rejectReason:C,setRejectReason:A})]})}},2806:(e,t,s)=>{s.d(t,{A:()=>c});var r=s(5043),a=s(5475),i=s(5501),n=s(6365),l=s(579);const o=e=>{let{title:t,description:s,actions:r,breadcrumbs:o,className:c="",testId:d}=e;return(0,l.jsxs)("div",{className:`mb-6 ${c}`,"data-testid":d,children:[o&&o.length>0&&(0,l.jsx)("nav",{className:"flex mb-4","aria-label":"Breadcrumb",children:(0,l.jsxs)("ol",{className:"flex items-center space-x-1 text-sm text-gray-500",children:[(0,l.jsx)("li",{children:(0,l.jsx)(a.N_,{to:"/",className:"flex items-center hover:text-primary","aria-label":"Home",children:(0,l.jsx)(i.A,{className:"h-4 w-4"})})}),o.map(((e,t)=>(0,l.jsxs)("li",{className:"flex items-center",children:[(0,l.jsx)(n.A,{className:"h-4 w-4 mx-1 text-gray-400"}),e.path&&t<o.length-1?(0,l.jsx)(a.N_,{to:e.path,className:"hover:text-primary",children:e.label}):(0,l.jsx)("span",{className:"font-medium text-gray-700",children:e.label})]},t)))]})}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-800",children:t}),s&&"string"===typeof s?(0,l.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:s}):s]}),r&&(0,l.jsx)("div",{className:"flex flex-wrap gap-3 mt-2 sm:mt-0",children:r})]})]})},c=(0,r.memo)(o)},3593:(e,t,s)=>{s.d(t,{A:()=>n});var r=s(5043),a=s(579);const i=e=>{let{title:t,subtitle:s,children:r,className:i="",bodyClassName:n="",headerClassName:l="",footerClassName:o="",icon:c,footer:d,onClick:m,hoverable:u=!1,noPadding:x=!1,bordered:h=!0,loading:p=!1,testId:g}=e;const f=`\n    bg-white rounded-xl ${h?"border border-gray-100":""} overflow-hidden transition-all duration-300\n    ${u?"hover:shadow-md hover:border-gray-200 transform hover:-translate-y-1":"shadow-sm"}\n    ${m?"cursor-pointer":""}\n    ${i}\n  `,y=`\n    px-6 py-4 border-b border-gray-100 flex items-center justify-between\n    ${l}\n  `,v=`\n    ${x?"":"p-6"}\n    ${n}\n  `,j=`\n    px-6 py-4 bg-gray-50 border-t border-gray-100\n    ${o}\n  `;return p?(0,a.jsxs)("div",{className:f,"data-testid":g,children:[(t||s||c)&&(0,a.jsxs)("div",{className:y,children:[(0,a.jsxs)("div",{className:"w-full",children:[t&&(0,a.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 animate-pulse"}),s&&(0,a.jsx)("div",{className:"h-4 mt-2 bg-gray-200 rounded w-1/2 animate-pulse"})]}),c&&(0,a.jsx)("div",{className:"h-8 w-8 bg-gray-200 rounded-full animate-pulse"})]}),(0,a.jsx)("div",{className:v,children:(0,a.jsx)("div",{className:"h-24 bg-gray-200 rounded animate-pulse"})}),d&&(0,a.jsx)("div",{className:j,children:(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 animate-pulse"})})]}):(0,a.jsxs)("div",{className:f,onClick:m,"data-testid":g,children:[(t||s||c)&&(0,a.jsxs)("div",{className:y,children:[(0,a.jsxs)("div",{children:["string"===typeof t?(0,a.jsx)("h3",{className:"text-lg font-semibold text-primary",children:t}):t,"string"===typeof s?(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:s}):s]}),c&&(0,a.jsx)("div",{className:"text-primary",children:c})]}),(0,a.jsx)("div",{className:v,children:r}),d&&(0,a.jsx)("div",{className:j,children:d})]})},n=(0,r.memo)(i)},3927:(e,t,s)=>{s.d(t,{A:()=>a});s(5043);var r=s(579);const a=e=>{let{size:t="md",className:s="",variant:a="spinner",color:i="#F28B22",useCurrentColor:n=!1}=e;const l={sm:{spinner:"w-5 h-5",dots:"w-1 h-1",pulse:"w-4 h-4",ripple:"w-6 h-6"},md:{spinner:"w-8 h-8",dots:"w-1.5 h-1.5",pulse:"w-6 h-6",ripple:"w-10 h-10"},lg:{spinner:"w-12 h-12",dots:"w-2 h-2",pulse:"w-8 h-8",ripple:"w-16 h-16"}},o=n?"currentColor":i;return"spinner"===a?(0,r.jsxs)("div",{className:`flex justify-center items-center ${s}`,role:"status","aria-label":"Loading",children:[(0,r.jsx)("div",{className:`spinner-smooth rounded-full border-2 border-gray-200 ${l[t].spinner}`,style:{borderTopColor:o,borderRightColor:o}}),(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"dots"===a?(0,r.jsxs)("div",{className:`flex justify-center items-center space-x-1 dots-bounce ${s}`,role:"status","aria-label":"Loading",children:[(0,r.jsx)("div",{className:`${l[t].dots} rounded-full dot`,style:{backgroundColor:o}}),(0,r.jsx)("div",{className:`${l[t].dots} rounded-full dot`,style:{backgroundColor:o}}),(0,r.jsx)("div",{className:`${l[t].dots} rounded-full dot`,style:{backgroundColor:o}}),(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"pulse"===a?(0,r.jsxs)("div",{className:`flex justify-center items-center ${s}`,role:"status","aria-label":"Loading",children:[(0,r.jsx)("div",{className:`${l[t].pulse} rounded-full pulse-smooth`,style:{backgroundColor:o}}),(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})]}):"ripple"===a?(0,r.jsxs)("div",{className:`flex justify-center items-center ${s}`,role:"status","aria-label":"Loading",children:[(0,r.jsx)("div",{className:`${l[t].ripple} rounded-full ripple-effect`,style:{color:o},children:(0,r.jsx)("div",{className:`${l[t].pulse} rounded-full pulse-smooth mx-auto`,style:{backgroundColor:o}})}),(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})]}):null}},6365:(e,t,s)=>{s.d(t,{A:()=>i});var r=s(5043);function a(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))}const i=r.forwardRef(a)},7012:(e,t,s)=>{s.d(t,{A:()=>i});var r=s(5043);function a(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const i=r.forwardRef(a)},7098:(e,t,s)=>{s.d(t,{A:()=>i});var r=s(5043);function a(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const i=r.forwardRef(a)},7422:(e,t,s)=>{s.d(t,{A:()=>x});var r=s(5043);function a(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}const i=r.forwardRef(a);function n(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"}))}const l=r.forwardRef(n);var o=s(2517),c=s(3927),d=s(1308),m=s(579);function u(e){let{columns:t,data:s,onRowClick:a,title:n,description:u,loading:x=!1,pagination:h=!0,pageSize:p=d.PI.DEFAULT_PAGE_SIZE,selectable:g=!0,onSelectionChange:f,actions:y,emptyMessage:v="No results found",className:j="",headerClassName:b="",bodyClassName:w="",footerClassName:N="",rowClassName:k,initialSortKey:C,initialSortDirection:A="asc",testId:$}=e;const[E,S]=(0,r.useState)(C?{key:C,direction:A}:null),[L,V]=(0,r.useState)(""),[R,I]=(0,r.useState)(1),[B,O]=(0,r.useState)([]),[M,P]=(0,r.useState)(null),D=(0,r.useMemo)((()=>E?[...s].sort(((e,t)=>{const s=e[E.key],r=t[E.key];return null==s&&null==r?0:null==s?"asc"===E.direction?-1:1:null==r?"asc"===E.direction?1:-1:"string"===typeof s&&"string"===typeof r?"asc"===E.direction?s.localeCompare(r):r.localeCompare(s):s<r?"asc"===E.direction?-1:1:s>r?"asc"===E.direction?1:-1:0})):s),[s,E]),z=(0,r.useMemo)((()=>L?D.filter((e=>Object.entries(e).some((e=>{let[t,s]=e;return null!==s&&void 0!==s&&("object"!==typeof s&&String(s).toLowerCase().includes(L.toLowerCase()))})))):D),[D,L]),F=Math.ceil(z.length/p),W=(0,r.useMemo)((()=>{const e=(R-1)*p;return z.slice(e,e+p)}),[z,R,p]),_=e=>{I(e)},Z=e=>{let t="bg-gray-100 text-gray-800";if("string"===typeof e){const s=e.toLowerCase();s.includes("active")||s.includes("approved")||s.includes("verified")||s.includes("completed")||s.includes("success")?t="bg-green-100 text-green-800":s.includes("pending")||s.includes("processing")?t="bg-yellow-100 text-yellow-800":s.includes("rejected")||s.includes("banned")||s.includes("failed")||s.includes("error")?t="bg-red-100 text-red-800":s.includes("inactive")&&(t="bg-gray-100 text-gray-800")}return(0,m.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${t}`,children:e})};return(0,m.jsxs)("div",{className:`bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-md ${j}`,"data-testid":$,children:[(n||u)&&(0,m.jsxs)("div",{className:`px-6 py-4 border-b border-gray-100 ${b}`,children:["string"===typeof n?(0,m.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:n}):n,"string"===typeof u?(0,m.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:u}):u]}),(0,m.jsxs)("div",{className:"p-4 border-b border-gray-100 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,m.jsxs)("div",{className:"relative flex-1",children:[(0,m.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,m.jsx)(i,{className:"h-5 w-5 text-gray-400"})}),(0,m.jsx)("input",{type:"text",placeholder:"Search...",className:"block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200",value:L,onChange:e=>{V(e.target.value),I(1)},"data-testid":`${$}-search`})]}),(0,m.jsxs)("div",{className:"flex items-center space-x-2",children:[B.length>0&&(0,m.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,m.jsxs)("span",{className:"text-sm text-gray-500",children:[B.length," selected"]}),(0,m.jsx)("button",{className:"px-3 py-1.5 bg-red-50 text-red-600 rounded-md text-sm font-medium hover:bg-red-100 transition-colors",onClick:()=>{O([]),f&&f([])},"data-testid":`${$}-clear-selection`,children:"Clear"})]}),y]})]}),(0,m.jsx)("div",{className:`overflow-x-auto ${w}`,children:x?(0,m.jsx)("div",{className:"flex justify-center items-center py-20",children:(0,m.jsx)(c.A,{size:"lg",variant:"spinner"})}):(0,m.jsxs)("table",{className:"min-w-full divide-y divide-gray-100",children:[(0,m.jsx)("thead",{className:"bg-gray-50",children:(0,m.jsxs)("tr",{children:[g&&(0,m.jsx)("th",{className:"w-12 px-6 py-3",children:(0,m.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary",onChange:e=>{const t=e.target.checked?Array.from({length:W.length},((e,t)=>t)):[];if(O(t),f){const e=t.map((e=>W[e])).filter((e=>void 0!==e));f(e)}},checked:B.length===W.length&&W.length>0,"data-testid":`${$}-select-all`})}),t.map((e=>(0,m.jsx)("th",{className:`px-6 py-3 text-${e.align||"left"} text-xs font-medium text-gray-500 uppercase tracking-wider ${e.sortable?"cursor-pointer hover:bg-gray-100":""} transition-colors duration-200 ${e.width?e.width:""} ${e.className||""}`,onClick:()=>e.sortable&&(e=>{let t="asc";E&&E.key===e&&"asc"===E.direction&&(t="desc"),S({key:e,direction:t})})(e.key),"data-testid":`${$}-column-${e.key}`,children:(0,m.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,m.jsx)("span",{children:e.label}),e.sortable&&(0,m.jsx)("span",{className:"transition-colors duration-200 "+((null===E||void 0===E?void 0:E.key)===e.key?"text-primary":"text-gray-400"),children:(null===E||void 0===E?void 0:E.key)===e.key&&"asc"===E.direction?(0,m.jsx)(l,{className:"h-4 w-4"}):(null===E||void 0===E?void 0:E.key)===e.key&&"desc"===E.direction?(0,m.jsx)(o.A,{className:"h-4 w-4"}):(0,m.jsx)("span",{className:"text-gray-300",children:"\u2195"})})]})},e.key)))]})}),(0,m.jsx)("tbody",{className:"bg-white divide-y divide-gray-100",children:W.length>0?W.map(((e,s)=>(0,m.jsxs)("tr",{className:`group transition-all duration-200 ${a?"cursor-pointer":""} ${B.includes(s)?"bg-primary bg-opacity-5":""}\n                    ${M===s?"bg-gray-50":""}\n                    ${k?k(e,s):""}`,onClick:()=>a&&a(e),onMouseEnter:()=>P(s),onMouseLeave:()=>P(null),"data-testid":`${$}-row-${s}`,children:[g&&(0,m.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,m.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary",checked:B.includes(s),onChange:()=>{},onClick:e=>((e,t)=>{t.stopPropagation();const s=[...B];if(B.includes(e)){const t=s.indexOf(e);s.splice(t,1)}else s.push(e);if(O(s),f){const e=s.map((e=>W[e])).filter((e=>void 0!==e));f(e)}})(s,e),"data-testid":`${$}-row-${s}-checkbox`})}),t.map((t=>(0,m.jsx)("td",{className:`px-6 py-4 whitespace-nowrap text-sm text-gray-600 group-hover:text-gray-900 text-${t.align||"left"} ${t.className||""}`,"data-testid":`${$}-row-${s}-cell-${t.key}`,children:t.render?t.render(e[t.key],e):t.key.toLowerCase().includes("status")?Z(e[t.key]):e[t.key]},t.key)))]},s))):(0,m.jsx)("tr",{children:(0,m.jsx)("td",{colSpan:t.length+(g?1:0),className:"px-6 py-10 text-center text-gray-500","data-testid":`${$}-empty-message`,children:v})})})]})}),h&&F>1&&(0,m.jsxs)("div",{className:`px-6 py-4 border-t border-gray-100 flex items-center justify-between ${N}`,children:[(0,m.jsxs)("div",{className:"text-sm text-gray-500",children:["Showing ",(R-1)*p+1," to ",Math.min(R*p,z.length)," of ",z.length," entries"]}),(0,m.jsxs)("div",{className:"flex space-x-1",children:[(0,m.jsx)("button",{onClick:()=>_(Math.max(1,R-1)),disabled:1===R,className:"px-3 py-1 rounded-md text-sm "+(1===R?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"),"data-testid":`${$}-pagination-prev`,children:"Previous"}),Array.from({length:Math.min(5,F)},((e,t)=>{let s;return s=F<=5||R<=3?t+1:R>=F-2?F-4+t:R-2+t,(0,m.jsx)("button",{onClick:()=>_(s),className:"px-3 py-1 rounded-md text-sm "+(R===s?"bg-primary text-white":"text-gray-700 hover:bg-gray-100"),"data-testid":`${$}-pagination-${s}`,children:s},s)})),(0,m.jsx)("button",{onClick:()=>_(Math.min(F,R+1)),disabled:R===F,className:"px-3 py-1 rounded-md text-sm "+(R===F?"text-gray-400 cursor-not-allowed":"text-gray-700 hover:bg-gray-100"),"data-testid":`${$}-pagination-next`,children:"Next"})]})]})]})}const x=(0,r.memo)(u)},7907:(e,t,s)=>{s.d(t,{A:()=>n});var r=s(5043),a=s(579);const i=e=>{let{children:t,variant:s="primary",size:r="md",className:i="",onClick:n,disabled:l=!1,type:o="button",icon:c,iconPosition:d="left",fullWidth:m=!1,loading:u=!1,rounded:x=!1,href:h,target:p,rel:g,title:f,ariaLabel:y,testId:v}=e;const j=`\n    inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2\n    ${{primary:"bg-primary text-white hover:bg-primary/90 focus-visible:ring-primary",secondary:"bg-gray-200 text-gray-800 hover:bg-gray-300 focus-visible:ring-gray-300",outline:"bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus-visible:ring-primary",danger:"bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500",success:"bg-green-600 text-white hover:bg-green-700 focus-visible:ring-green-500",text:"bg-transparent text-primary hover:bg-gray-100 focus-visible:ring-primary",link:"bg-transparent text-primary hover:underline focus-visible:ring-transparent p-0"}[s]}\n    ${{xs:"text-xs px-2 py-1",sm:"text-xs px-3 py-1.5",md:"text-sm px-4 py-2",lg:"text-base px-5 py-2.5",xl:"text-lg px-6 py-3"}[r]}\n    ${l?"opacity-60 cursor-not-allowed":"cursor-pointer"}\n    ${m?"w-full":""}\n    ${x?"rounded-full":"rounded-lg"}\n    ${i}\n  `,b=(0,a.jsxs)(a.Fragment,{children:[u&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-current",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","aria-hidden":"true",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),c&&"left"===d&&!u&&(0,a.jsx)("span",{className:"mr-2",children:c}),t,c&&"right"===d&&(0,a.jsx)("span",{className:"ml-2",children:c})]});return h?(0,a.jsx)("a",{href:h,className:j,target:p,rel:g||("_blank"===p?"noopener noreferrer":void 0),onClick:n,title:f,"aria-label":y,"data-testid":v,children:b}):(0,a.jsx)("button",{type:o,className:j,onClick:n,disabled:l||u,title:f,"aria-label":y,"data-testid":v,children:b})},n=(0,r.memo)(i)},8100:(e,t,s)=>{s.d(t,{A:()=>o});var r=s(5043),a=s(7591),i=s(7950),n=s(579);const l=e=>{let{isOpen:t,onClose:s,title:l,children:o,size:c="md",footer:d,closeOnEsc:m=!0,closeOnBackdropClick:u=!0,showCloseButton:x=!0,centered:h=!0,className:p="",bodyClassName:g="",headerClassName:f="",footerClassName:y="",backdropClassName:v="",testId:j}=e;const b=(0,r.useRef)(null);if((0,r.useEffect)((()=>{const e=e=>{m&&"Escape"===e.key&&s()};return t&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="auto"}}),[t,s,m]),(0,r.useEffect)((()=>{if(!t||!b.current)return;const e=b.current.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');if(0===e.length)return;const s=e[0],r=e[e.length-1],a=e=>{"Tab"===e.key&&(e.shiftKey?document.activeElement===s&&(r.focus(),e.preventDefault()):document.activeElement===r&&(s.focus(),e.preventDefault()))};return document.addEventListener("keydown",a),s.focus(),()=>{document.removeEventListener("keydown",a)}}),[t]),!t)return null;const w=(0,n.jsxs)(r.Fragment,{children:[(0,n.jsx)("div",{className:`fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity ${v}`,onClick:u?s:void 0,"data-testid":`${j}-backdrop`}),(0,n.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,n.jsx)("div",{className:`flex min-h-full items-${h?"center":"start"} justify-center p-4 text-center`,children:(0,n.jsxs)("div",{ref:b,className:`${{xs:"max-w-xs",sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[c]} w-full transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all ${p}`,onClick:e=>e.stopPropagation(),"data-testid":j,children:[(0,n.jsxs)("div",{className:`flex items-center justify-between px-6 py-4 border-b border-gray-100 ${f}`,children:["string"===typeof l?(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:l}):l,x&&(0,n.jsx)("button",{type:"button",className:"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary rounded-full p-1",onClick:s,"aria-label":"Close modal","data-testid":`${j}-close-button`,children:(0,n.jsx)(a.A,{className:"h-6 w-6"})})]}),(0,n.jsx)("div",{className:`px-6 py-4 ${g}`,children:o}),d&&(0,n.jsx)("div",{className:`px-6 py-4 bg-gray-50 border-t border-gray-100 flex justify-end space-x-3 ${y}`,children:d})]})})})]});return(0,i.createPortal)(w,document.body)},o=(0,r.memo)(l)},8682:(e,t,s)=>{s.d(t,{A:()=>i});var r=s(5043);function a(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))}const i=r.forwardRef(a)},9422:(e,t,s)=>{s.d(t,{A:()=>i});var r=s(5043);function a(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))}const i=r.forwardRef(a)},9531:(e,t,s)=>{s.d(t,{A:()=>i});var r=s(5043);function a(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const i=r.forwardRef(a)},9850:(e,t,s)=>{s.d(t,{A:()=>i});var r=s(5043);function a(e,t){let{title:s,titleId:a,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},i),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))}const i=r.forwardRef(a)}}]);
//# sourceMappingURL=402.dcecae95.chunk.js.map